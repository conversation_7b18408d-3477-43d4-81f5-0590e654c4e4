# NotAMess Forms Project Rules

## Project Patterns

### 1. Package Management

- Use bun for all package installations
- Run `bun add` for adding new dependencies
- Run `bun add -D` for dev dependencies
- Use `bun install` for installing all dependencies
- Keep package.json up to date
- Lock files should be committed

### 2. Tailwind CSS Configuration

- Use direct CSS imports with `@import "tailwindcss"`
- Configure Tailwind directly in globals.css using @theme
- Use CSS variables for theme customization
- Keep styles modular and utility-first
- Use prose classes for rich text content
- Follow mobile-first approach
- Maintain consistent spacing
- Use CSS variables for theming
- Follow accessibility guidelines

### 3. Error Checking Protocol

- Always run type checking after code changes
- Verify linter errors are resolved
- Test affected components and features
- Check console for runtime errors
- Validate build process completes successfully
- Only update memory bank after all errors are cleared
- Document any persistent warnings or known issues

### 4. File Organization

- Use feature-based organization within app directory
- Group related components in feature-specific folders
- Keep shared components in components/ui
- Place hooks in lib/hooks
- Store constants in lib/constants
- Maintain types in types directory

### 5. Naming Conventions

- React components: PascalCase
- Files containing React components: PascalCase
- Hooks: camelCase prefixed with 'use'
- Utilities: camelCase
- Constants: UPPER_SNAKE_CASE
- Types/Interfaces: PascalCase
- CSS classes: kebab-case

### 6. Component Structure

- Use functional components with TypeScript
- Implement props interface for each component
- Export components as named exports
- Keep components focused and single-responsibility
- Use composition over inheritance

### 7. State Management

- Use Zustand for global state
- React Context for component trees
- Local state for component-specific data
- Prefer controlled components
- Implement proper state initialization

### 8. Form Handling

- Use controlled form components
- Implement form-level validation
- Use schema-based validation with Zod
- Handle form submission asynchronously
- Provide proper error feedback

### 9. Styling

- Use TailwindCSS for styling
- Follow mobile-first approach
- Maintain consistent spacing
- Use CSS variables for theming
- Follow accessibility guidelines

### 10. Error Handling

- Use try-catch for async operations
- Implement error boundaries
- Provide user-friendly error messages
- Log errors appropriately
- Handle edge cases gracefully

### 11. Testing

- Write unit tests for components
- Test hooks independently
- Implement integration tests
- Use proper test descriptions
- Follow arrange-act-assert pattern

### 12. Performance

- Implement proper code splitting
- Use React.memo where beneficial
- Optimize re-renders
- Lazy load components when appropriate
- Cache API responses

### 13. Security

- Validate all inputs
- Sanitize outputs
- Implement proper access control
- Use environment variables
- Follow security best practices

## User Preferences

### 1. Code Style

- Use semicolons
- Use single quotes
- 2 space indentation
- Max line length: 80 characters
- Trailing commas in multi-line

### 2. Git Workflow

- Use feature branches
- Write descriptive commit messages
- Follow conventional commits
- Squash commits when merging
- Keep PRs focused

### 3. Documentation

- Document complex functions
- Add component prop documentation
- Maintain up-to-date README
- Document API endpoints
- Include usage examples

## Tool Usage

### 1. VS Code

- Use ESLint extension
- Enable format on save
- Use GitLens for history
- Enable TailwindCSS IntelliSense
- Use Error Lens for inline errors

### 2. Terminal

- Use PowerShell
- Enable command history
- Use bun for package management
- Run tests with watch mode
- Use development server

### 3. Browser Tools

- Use Chrome DevTools
- Enable React Developer Tools
- Use Redux DevTools
- Monitor network requests
- Check accessibility

## Known Challenges

### 1. Form System

- Complex nested forms
- Dynamic validation rules
- Real-time updates
- Performance with large forms
- Multi-step forms

### 2. Authentication

- Role-based access
- Session management
- Token refresh
- Social authentication
- Password policies

### 3. Integration

- AI service reliability
- Blockchain transaction times
- API rate limits
- Third-party dependencies
- Data synchronization

## Project Evolution

### 1. Architecture Decisions

- App Router over Pages Router
- Supabase for backend
- TailwindCSS for styling
- Shadcn/UI for components
- Zustand for state management

### 2. Feature Progress

- Authentication system complete
- Form system in development
- AI integration planned
- Blockchain integration planned
- Professional services planned

### 3. Future Considerations

- Scaling strategy
- Internationalization
- Mobile optimization
- Performance optimization
- Security enhancements

## UI Components Standards

- Use Sonner for all toast notifications
  - Import from '@/components/ui/sonner'
  - Use bottom-right position
  - Use project's custom styling
  - Support success/error/warning/info types
  - Include action buttons when needed

# Next.js Project Rules

## Routing and Parameters

### Page Component Params

- In Next.js route handlers and page components, the `params` prop is a Promise that must be awaited.
- Always use the React `use` hook or async/await to handle params in page components.

Example:

```tsx
// For client components
import { use } from 'react';

type PageProps = {
  params: Promise<{
    id: string;
    username: string;
  }>;
};

export default function Page({ params }: PageProps) {
  const resolvedParams = use(params);
  // Now use resolvedParams.id, resolvedParams.username, etc.
}

// For server components
export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const resolvedParams = await params;
  return <div>My Post: {resolvedParams.slug}</div>;
}
```

### URL Structure

- Always include the username parameter in document routes
- Format should be: `/${username}/documents/${documentId}`
- Use the getUserRoute() helper function to generate routes

## Component Patterns

### Type Definitions

- Always define interfaces for untyped data structures
- Use explicit type annotations in component parameters
- Avoid implicit any types in callbacks and event handlers

import { FONT_GEIST_SANS } from '@/lib/constants';
import { cn, constructMetadata } from '@/lib/utils';
import './globals.css';

import { Viewport } from 'next';
import { Toaster } from 'sonner';

export const metadata = constructMetadata();

export const viewport: Viewport = {
  themeColor: '#f5f5f5',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(
          FONT_GEIST_SANS.variable,
          'bg-neutral-50 *:font-geist-sans'
        )}
      >
        {children}
        <Toaster position="bottom-right" />
      </body>
    </html>
  );
}

@import 'tailwindcss';

@import '../styles/scrollbar.css';

@import '../styles/tiptap-custom.css';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-accent-10: rgb(94, 234, 212);
  --color-accent-50: rgb(57, 201, 187);
  --color-accent-100: rgb(20, 184, 166);
  --color-accent-200: rgb(13, 174, 158);
  --color-accent-300: rgb(3, 162, 146);
  --color-notamess-10: #fcc1ab;
  --color-notamess-50: rgb(248, 115, 79);
  --color-notamess-100: rgb(246, 84, 40);
  --color-notamess-200: rgb(227, 55, 11);
  --color-notamess-300: rgb(229, 55, 15);
  
  --font-geist-sans: "Geist Sans",var(--font-geist-sans);
  --font-bircolage-grotesque: "Bricolage Grotesque",var(--font-bircolage-grotesque);
  --font-geist-mono: "Geist Mono",var(--font-geist-mono);
  --font-sf-mono: "SF Mono",var(--font-sf-mono);
  --font-kalam: "Kalam",var(--font-kalam);
  
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-pulse-link: pulse-link 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  
  @keyframes accordion-down {
    from {
      height: 0;
    }
    
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    
    to {
      height: 0;
    }
  }
  
  @keyframes pulse-link {
    0%, 100% {
      opacity: 1;
    }
    
    50% {
      opacity: 0.7;
    }
  }
}

/*
The default border color has changed to `currentColor` in Tailwind CSS v4,
so we've added these compatibility styles to make sure everything still
looks the same as it did with Tailwind CSS v3.

If we ever want to remove these styles, we need to add an explicit border
color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer utilities {
  .animate-pulse-link {
    animation: var(--animate-pulse-link);
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  /* colors */
  --color-border: hsl(0 0% 78%);
  --color-hover-color: var(--hovercolor);
  --btn-white-color: rgb(255, 255, 255);
  --btn-white-hover-color: rgb(248, 248, 248);
  --btn-white-shadow-inner: rgb(235, 235, 235);
  --btn-white-shadow-outer: rgb(235, 235, 235);
  --btn-dark-color: rgb(56, 56, 56);
  --btn-dark-hover-color: rgb(92, 92, 92);
  --btn-dark-shadow-inner: rgb(73, 73, 73);
  --btn-dark-shadow-outer: rgb(45, 45, 45);
  /* accent */
  --btn-accent-color: rgb(42, 54, 71);
  --btn-accent-hover-color: rgb(51, 64, 82);
  --btn-accent-shadow-inner: rgb(30, 41, 56);
  --btn-accent-shadow-outer: rgb(36, 47, 63);
  /* blue */
  --btn-blue-color: rgb(59, 130, 246);
  --btn-blue-hover-color: rgb(96, 165, 250);
  --btn-blue-shadow-inner: rgb(30, 102, 227);
  --btn-blue-shadow-outer: rgb(47, 117, 230);
  /* Red */
  --btn-red-color: rgb(246, 84, 40);
  --btn-red-hover-color: rgb(248, 115, 79);
  --btn-red-shadow-inner: rgb(227, 55, 11);
  --btn-red-shadow-outer: rgb(229, 55, 15);
  /* Orange */
  --btn-orange-color: rgb(249, 115, 22);
  --btn-orange-hover-color: rgb(251, 146, 60);
  --btn-orange-shadow-inner: rgb(230, 92, 0);
  --btn-orange-shadow-outer: rgb(234, 88, 12);
  /* Yellow */
  --btn-yellow-color: rgb(253, 186, 116);
  --btn-yellow-hover-color: rgb(254, 202, 147);
  --btn-yellow-shadow-inner: rgb(234, 159, 57);
  --btn-yellow-shadow-outer: rgb(237, 171, 86);
  /* Green */
  --btn-green-color: rgb(74, 222, 128);
  --btn-green-hover-color: rgb(110, 231, 159);
  --btn-green-shadow-inner: rgb(34, 197, 94);
  --btn-green-shadow-outer: rgb(52, 211, 115);
  /* Purple */
  --btn-purple-color: rgb(192, 132, 252);
  --btn-purple-hover-color: rgb(211, 167, 254);
  --btn-purple-shadow-inner: rgb(168, 103, 233);
  --btn-purple-shadow-outer: rgb(180, 121, 237);
  /* Teal */
  --btn-teal-color: rgb(20, 184, 166);
  --btn-teal-hover-color: rgb(57, 201, 187);
  --btn-teal-shadow-inner: rgb(3, 162, 146);
  --btn-teal-shadow-outer: rgb(13, 174, 158);
  /* Cyan */
  --btn-cyan-color: rgb(14, 165, 233);
  --btn-cyan-hover-color: rgb(51, 187, 241);
  --btn-cyan-shadow-inner: rgb(2, 142, 214);
  --btn-cyan-shadow-outer: rgb(10, 155, 225);
  /* Indigo */
  --btn-indigo-color: rgb(99, 102, 241);
  --btn-indigo-hover-color: rgb(130, 133, 245);
  --btn-indigo-shadow-inner: rgb(72, 75, 222);
  --btn-indigo-shadow-outer: rgb(86, 89, 228);
  /* Pink */
  --btn-pink-color: rgb(244, 114, 182);
  --btn-pink-hover-color: rgb(248, 146, 203);
  --btn-pink-shadow-inner: rgb(225, 82, 160);
  --btn-pink-shadow-outer: rgb(231, 98, 172);
}

code {
  font-family: var(--font-sf-mono);
}

h1 & h2 {
  font-family: var(--font-bircolage-grotesque);
}

p {
  font-family: var(--font-geist-sans);
}

.titleClass {
  font-family: var(--font-sf-mono);
}

::-moz-selection {
  /* Code for Firefox */
  color: rgb(3, 162, 146);
  background: rgb(94, 234, 212);
}

::selection {
  color: rgb(3, 162, 146);
  background: rgb(94, 234, 212);
}

/* default */
.shadow-default {
  background-color: var(--btn-red-color);
  box-shadow: var(--btn-red-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-red-shadow-outer) 0px 0px 0px 1px;
  /* border-radius: 999px; */
  opacity: 1;
}

.shadow-default:hover {
  background-color: var(--btn-red-hover-color);
  box-shadow: var(--btn-red-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-red-shadow-outer) 0px 0px 0px 1px;
  /* border-radius: 999px; */
  opacity: 1;
}

/* accent */
.shadow-accent {
  background-color: var(--btn-accent-color);
  box-shadow: var(--btn-accent-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-accent-shadow-outer) 0px 0px 0px 1px;
  border-radius: 999px;
  opacity: 1;
}

.shadow-accent:hover {
  background-color: var(--btn-accent-hover-color);
  box-shadow: var(--btn-accent-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-accent-shadow-outer) 0px 0px 0px 1px;
  border-radius: 999px;
  opacity: 1;
}

/* light */
.shadow-light {
  background-color: var(--btn-white-color);
  box-shadow: var(--btn-white-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-white-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-light:hover {
  background-color: var(--btn-white-hover-color);
  box-shadow: var(--btn-white-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-white-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* dark */
.shadow-dark {
  background-color: var(--btn-dark-color);
  box-shadow: var(--btn-dark-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-dark-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-dark:hover {
  background-color: var(--btn-dark-hover-color);
  box-shadow: var(--btn-dark-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-dark-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* blue */
.shadow-blue {
  background-color: var(--btn-blue-color);
  box-shadow: var(--btn-blue-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-blue-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-blue:hover {
  background-color: var(--btn-blue-hover-color);
  box-shadow: var(--btn-blue-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-blue-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* orange */
.shadow-orange {
  background-color: var(--btn-orange-color);
  box-shadow: var(--btn-orange-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-orange-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-orange:hover {
  background-color: var(--btn-orange-hover-color);
  box-shadow: var(--btn-orange-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-orange-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* yellow */
.shadow-yellow {
  background-color: var(--btn-yellow-color);
  box-shadow: var(--btn-yellow-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-yellow-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-yellow:hover {
  background-color: var(--btn-yellow-hover-color);
  box-shadow: var(--btn-yellow-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-yellow-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* green */
.shadow-green {
  background-color: var(--btn-green-color);
  box-shadow: var(--btn-green-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-green-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-green:hover {
  background-color: var(--btn-green-hover-color);
  box-shadow: var(--btn-green-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-green-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* purple */
.shadow-purple {
  background-color: var(--btn-purple-color);
  box-shadow: var(--btn-purple-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-purple-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-purple:hover {
  background-color: var(--btn-purple-hover-color);
  box-shadow: var(--btn-purple-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-purple-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* teal */
.shadow-teal {
  background-color: var(--btn-teal-color);
  box-shadow: var(--btn-teal-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-teal-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-teal:hover {
  background-color: var(--btn-teal-hover-color);
  box-shadow: var(--btn-teal-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-teal-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* cyan */
.shadow-cyan {
  background-color: var(--btn-cyan-color);
  box-shadow: var(--btn-cyan-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-cyan-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-cyan:hover {
  background-color: var(--btn-cyan-hover-color);
  box-shadow: var(--btn-cyan-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-cyan-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* indigo  */
.shadow-indigo {
  background-color: var(--btn-indigo-color);
  box-shadow: var(--btn-indigo-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-indigo-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-indigo:hover {
  background-color: var(--btn-indigo-hover-color);
  box-shadow: var(--btn-indigo-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-indigo-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

/* pink */
.shadow-pink {
  background-color: var(--btn-pink-color);
  box-shadow: var(--btn-pink-shadow-inner) 0px -2.4px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-pink-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}

.shadow-pink:hover {
  background-color: var(--btn-pink-hover-color);
  box-shadow: var(--btn-pink-shadow-inner) 0px 0px 0px 0px inset,
    rgba(143, 39, 9, 0.2) 0px 1px 3px 0px,
    var(--btn-pink-shadow-outer) 0px 0px 0px 1px;
  opacity: 1;
}
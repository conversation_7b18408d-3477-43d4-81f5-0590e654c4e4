'use client';

import {
  DocumentContent,
  DocumentSection,
  SectionBasedEditor,
} from '@/components/documents/editor';
import DocumentPreview from '@/components/documents/preview/DocumentPreview';
import { DirectDocumentViewer } from '@/components/documents/shared/DirectDocumentViewer';
import { PasswordAccessForm } from '@/components/documents/shared/PasswordAccessForm';
import { PinAccessForm } from '@/components/documents/shared/PinAccessForm';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { useDocuments } from '@/lib/hooks/use-supabase';
import { Document } from '@/lib/types/database-modules';
import { Edit, Save, X } from 'lucide-react';
import { use, useState } from 'react';
import { toast } from 'sonner';

type SharedDocumentPageProps = {
  params: Promise<{ token: string }>;
};

export default function SharedDocumentPage(props: SharedDocumentPageProps) {
  // Access params directly
  const { token } = use(props.params);
  const [pin, setPin] = useState<string | null>(null);
  const [pinRequired, setPinRequired] = useState(false);
  const [pinError, setPinError] = useState<string | null>(null);
  const [document, setDocument] = useState<Document | null>(null);
  const [permission, setPermission] = useState<'view' | 'edit' | 'comment'>(
    'view'
  );
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [contentSections, setContentSections] = useState<DocumentSection[]>([]);

  const handlePinSubmit = (pinValue: string) => {
    setPin(pinValue);
    setPinRequired(false);
  };

  // Handle errors from the DirectDocumentViewer
  const handleDocumentError = (error: string) => {
    // Handle PIN requirement
    if (error.includes('PIN') || error.includes('pin')) {
      setPinRequired(true);
      setPinError(null);
      return;
    }

    // For other errors, we can display them in a toast
    if (error.includes('disabled')) {
      toast.error('This share link has been disabled by the document owner.');
    } else if (error.includes('permission')) {
      toast.error('You do not have permission to access this document.');
    } else if (error.includes('expired')) {
      toast.error('This share link has expired.');
    }
  };

  // Navigation functions can be added here if needed

  const handleEditRequest = () => {
    if (permission !== 'edit') {
      toast.error('You do not have permission to edit this document');
      return;
    }

    if (isPasswordProtected) {
      // For password-protected documents, we need to verify the password first
      setPasswordError(null);
      setIsEditMode(true);
    } else {
      // Go directly to edit mode for non-password-protected documents
      setIsEditMode(true);
    }
  };

  // Get the document hooks outside of the handler function
  const { verifyEditPassword } = useDocuments();

  const handlePasswordSubmit = async (password: string) => {
    // Clear any previous errors
    setPasswordError(null);

    // Show loading toast
    toast.promise(
      async () => {
        try {
          if (!password || password.trim() === '') {
            throw new Error('Password cannot be empty');
          }

          // Use the hook that was initialized at component level
          const isValid = await verifyEditPassword(token, password);

          if (!isValid) {
            throw new Error('Incorrect password. Please try again.');
          }

          // Password is valid, enable edit mode
          setIsPasswordProtected(false); // Mark as verified
          setIsEditMode(true); // Now enable edit mode

          return 'Password verified successfully';
        } catch (error) {
          console.error('Error verifying password:', error);
          // Set the error message in the form
          setPasswordError(
            error instanceof Error
              ? error.message
              : 'An error occurred while verifying the password'
          );
          // Re-throw for the toast
          throw error;
        }
      },
      {
        loading: 'Verifying password...',
        success: () => 'Access granted',
        error: (err) => err.message || 'Failed to verify password',
      }
    );
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
    setPasswordError(null);
    // If we were in password-protected edit mode, restore the protection state
    if (isPasswordProtected === false) {
      setIsPasswordProtected(true);
    }
  };

  const handleSaveDocument = async () => {
    // Show loading toast
    toast.promise(
      async () => {
        try {
          // Validate content sections
          if (!contentSections || contentSections.length === 0) {
            throw new Error('Document must have at least one section');
          }

          // Prepare the data to send
          const data = {
            sections: contentSections,
          };

          // Send the update request
          const response = await fetch(`/api/shared/${token}/update`, {
            method: 'POST',
            cache: 'no-store',
            next: { revalidate: 0 },
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
              Expires: '0',
            },
            body: JSON.stringify(data),
          });

          if (!response.ok) {
            // Try to parse error response
            const errorData = await response.json().catch(() => ({}));
            throw new Error(
              errorData.error ||
                `Failed to save document: ${response.status} ${response.statusText}`
            );
          }

          // Parse the response
          const result = await response.json();

          // Update the document with the latest data
          if (result.document) {
            setDocument(result.document);
          } else {
            console.warn('No document data returned from API');
          }

          // Exit edit mode
          setIsEditMode(false);

          return 'Document saved successfully';
        } catch (error) {
          console.error('Error saving document:', error);
          throw error instanceof Error
            ? error
            : new Error('Failed to save document due to an unknown error');
        }
      },
      {
        loading: 'Saving document...',
        success: (message) => message,
        error: (err) => err.message || 'Failed to save document',
      }
    );
  };

  // If PIN is required, show the PIN form
  if (pinRequired) {
    return (
      <div className="container mx-auto py-8 px-4">
        <PinAccessForm
          documentTitle="Protected Document"
          onSubmit={handlePinSubmit}
          error={pinError || undefined}
        />
      </div>
    );
  }

  // Show password input form if in edit mode and password protected
  if (isEditMode && isPasswordProtected) {
    return (
      <div className="container mx-auto py-8 px-4">
        <PasswordAccessForm
          documentTitle={document?.title || 'Protected Document'}
          onSubmit={handlePasswordSubmit}
          onCancel={handleCancelEdit}
          error={passwordError || undefined}
        />
      </div>
    );
  }

  // Show editor if in edit mode
  if (isEditMode && document) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleCancelEdit}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <h1 className="text-2xl font-bold">{document.title}</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={handleSaveDocument}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <SectionBasedEditor
              initialSections={contentSections}
              onChange={(sections) => setContentSections(sections)}
            />
          </CardContent>
        </Card>

        <div className="mt-4 text-center text-sm text-muted-foreground">
          <p>
            You are editing this document. Your changes will be saved when you
            click "Save Changes".
          </p>
        </div>
      </div>
    );
  }

  // Show the document in view mode if we have it
  if (document) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6 flex items-center justify-between">
          <h1 className="text-2xl font-bold">{document.title}</h1>
          {permission === 'edit' && (
            <Button
              onClick={handleEditRequest}
              className="flex items-center"
              variant="outline"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Document
            </Button>
          )}
        </div>

        <Card>
          <CardContent className="p-6">
            <DocumentPreview document={document} />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Otherwise show the document viewer
  return (
    <div className="container mx-auto py-8 px-4">
      <ErrorBoundary>
        <DirectDocumentViewer
          token={token}
          pin={pin || undefined}
          onError={handleDocumentError}
          onDocumentLoaded={(doc, perm) => {
            setDocument(doc);
            setPermission(perm as 'view' | 'edit' | 'comment');

            // Check if the document is password protected for editing
            // Use type assertion to access potential custom properties
            const docWithCustomProps = doc as Document & {
              password_protected?: boolean;
              is_password_protected?: boolean;
            };

            const isProtected =
              perm === 'edit' &&
              (docWithCustomProps.password_protected ||
                docWithCustomProps.is_password_protected ||
                (doc.metadata &&
                  typeof doc.metadata === 'object' &&
                  'password_protected' in doc.metadata));

            if (isProtected) {
              setIsPasswordProtected(true);
            }

            // Parse document content for editing if needed
            if (doc.content) {
              try {
                // Try to parse as DocumentContent
                const content = doc.content as unknown as DocumentContent;

                if (
                  content &&
                  typeof content === 'object' &&
                  Array.isArray(content.sections)
                ) {
                  // Convert to DocumentSection format with IDs, preserving existing IDs if available
                  const sections = content.sections.map((section) => ({
                    id:
                      section.id || Math.random().toString(36).substring(2, 11),
                    title: section.title || '',
                    content: section.content || '',
                    isSignatureSection: section.isSignatureSection || false,
                    aiSuggestions: section.aiSuggestions || [],
                  }));
                  console.log('Parsed sections:', sections.length);
                  setContentSections(sections);
                } else if (typeof doc.content === 'string') {
                  // Handle string content by creating a properly typed DocumentSection
                  const section: DocumentSection = {
                    id: Math.random().toString(36).substring(2, 11),
                    title: 'Content',
                    content: doc.content,
                    isSignatureSection: false,
                    aiSuggestions: [],
                  };
                  setContentSections([section]);
                } else {
                  // Handle other object content by stringifying it
                  const contentStr = JSON.stringify(doc.content);
                  const section: DocumentSection = {
                    id: Math.random().toString(36).substring(2, 11),
                    title: 'Content',
                    content: contentStr,
                    isSignatureSection: false,
                    aiSuggestions: [],
                  };
                  setContentSections([section]);
                }
              } catch (error) {
                console.error('Error parsing document content:', error);
                // Fallback to string representation
                const contentStr =
                  typeof doc.content === 'string'
                    ? doc.content
                    : JSON.stringify(doc.content);

                // Create a properly typed DocumentSection
                const fallbackSection: DocumentSection = {
                  id: Math.random().toString(36).substring(2, 11),
                  title: 'Content',
                  content: contentStr,
                  isSignatureSection: false,
                  aiSuggestions: [],
                };

                setContentSections([fallbackSection]);
              }
            } else {
              // If no content, create an empty section
              console.warn(
                'No content found in document, creating empty section'
              );

              // Create a properly typed empty DocumentSection
              const emptySection: DocumentSection = {
                id: Math.random().toString(36).substring(2, 11),
                title: '',
                content: '',
                isSignatureSection: false,
                aiSuggestions: [],
              };

              setContentSections([emptySection]);
            }
          }}
        />
      </ErrorBoundary>
    </div>
  );
}

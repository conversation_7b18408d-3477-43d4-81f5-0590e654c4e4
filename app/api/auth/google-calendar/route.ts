import { getGoogleCalendarAuthUrl } from '@/lib/services/googleCalendarService';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: async () => (await cookies()).getAll(),
          setAll: async (cookiesArray) => {
            const cookieStore = await cookies();
            for (const { name, value, options } of cookiesArray) {
              cookieStore.set(name, value, options);
            }
          },
        },
      }
    );

    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Generate a state parameter to prevent CSRF attacks
    const state = Buffer.from(
      JSON.stringify({
        userId: user.id,
        redirectUrl: request.nextUrl.searchParams.get('redirectUrl') || '/',
      })
    ).toString('base64');

    // Generate the authorization URL
    const authUrl = getGoogleCalendarAuthUrl(state);

    // Redirect to the authorization URL
    return NextResponse.redirect(authUrl);
  } catch (error) {
    console.error('Error in Google Calendar auth route:', error);
    return NextResponse.json(
      { error: 'Failed to initiate Google Calendar authorization' },
      { status: 500 }
    );
  }
}

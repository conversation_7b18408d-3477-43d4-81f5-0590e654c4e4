import { EmailService } from '@/lib/services/emailService';
import { supabaseServer } from '@/lib/supabase/server-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const SendEmailsSchema = z.object({
  userName: z.string().min(1, 'User name is required'),
  userEmail: z.string().email('Valid email is required'),
  role: z.enum(['user', 'lawyer'],).or(z.string()).refine(val => val === 'user' || val === 'lawyer', {
    message: 'Role must be user or lawyer',
  }),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('📧 [SEND-EMAILS-API] Received request:', JSON.stringify(body, null, 2));

    // Validate the request body
    const validatedData = SendEmailsSchema.parse(body);

    // Get server client to fetch waitlist stats
    const supabase = await supabaseServer();

    // Get updated waitlist stats for email
    const { data: statsData } = await supabase
      .from('waitlist')
      .select('role, created_at')
      .order('created_at', { ascending: false });

    const stats = {
      totalCount: statsData?.length || 0,
      userCount: statsData?.filter((item: any) => item.role === 'user').length || 0,
      lawyerCount: statsData?.filter((item: any) => item.role === 'lawyer').length || 0,
      userPosition: statsData?.length || 1, // New user is at the end
    };

    console.log('📊 [SEND-EMAILS-API] Using waitlist stats:', JSON.stringify(stats, null, 2));

    // Send waitlist emails using EmailService
    const emailResult = await EmailService.sendWaitlistEmails({
      userName: validatedData.userName,
      userEmail: validatedData.userEmail,
      role: validatedData.role,
      waitlistStats: stats,
    });

    console.log('✅ [SEND-EMAILS-API] Email sending completed:', JSON.stringify(emailResult, null, 2));

    return NextResponse.json(
      {
        success: true,
        message: 'Emails sent successfully',
        emailResult,
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('❌ [SEND-EMAILS-API] Error sending emails:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid request data', 
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to send emails', 
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Waitlist Email Sending Endpoint',
    usage: {
      method: 'POST',
      body: {
        userName: 'string (required)',
        userEmail: 'string (required, valid email)',
        role: 'user | lawyer (required)',
      },
      description: 'Sends waitlist welcome and admin notification emails',
    },
    timestamp: new Date().toISOString(),
  });
}

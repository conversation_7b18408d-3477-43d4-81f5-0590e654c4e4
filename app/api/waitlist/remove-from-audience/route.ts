import { EmailService } from '@/lib/services/emailService';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const RemoveFromAudienceSchema = z.object({
  email: z.string().email('Valid email is required'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('📧 [REMOVE-AUDIENCE-API] Received request:', JSON.stringify(body, null, 2));

    // Validate the request body
    const validatedData = RemoveFromAudienceSchema.parse(body);

    // Remove from waitlist audience using EmailService
    const result = await EmailService.removeFromWaitlistAudience(validatedData.email);

    console.log('✅ [REMOVE-AUDIENCE-API] Removal completed:', JSON.stringify(result, null, 2));

    return NextResponse.json(
      {
        success: true,
        message: 'Successfully removed from email audience',
        result,
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('❌ [REMOVE-AUDIENCE-API] Error removing from audience:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid request data', 
          details: error.errors,
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to remove from email audience', 
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Remove from Waitlist Audience Endpoint',
    usage: {
      method: 'POST',
      body: {
        email: 'string (required, valid email)',
      },
      description: 'Removes an email from the waitlist audience in Resend',
    },
    timestamp: new Date().toISOString(),
  });
}

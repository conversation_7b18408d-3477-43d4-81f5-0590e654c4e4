import { EmailService } from '@/lib/services/emailService';
import { supabaseServer } from '@/lib/supabase/server-client';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const WaitlistSchema = z.object({
  full_name: z
    .string()
    .min(2, { message: 'Full name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  role: z.enum(['user', 'lawyer'], { message: 'Please select a role' }),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate the request body
    const validatedData = WaitlistSchema.parse(body);

    // Get server client
    const supabase = await supabaseServer();

    // Insert into waitlist table
    const { data, error } = await supabase
      .from('waitlist')
      .insert([
        {
          full_name: validatedData.full_name,
          email: validatedData.email,
          role: validatedData.role,
        },
      ])
      .select()
      .single();

    if (error) {
      if (error.code === '23505') {
        // Unique constraint violation (email already exists)
        return NextResponse.json(
          { error: 'This email is already on our waitlist!' },
          { status: 409 }
        );
      }

      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to join waitlist' },
        { status: 500 }
      );
    }

    // Get updated waitlist stats for email
    const { data: statsData } = await supabase
      .from('waitlist')
      .select('role, created_at')
      .order('created_at', { ascending: false });

    const stats = {
      totalCount: statsData?.length || 0,
      userCount:
        statsData?.filter((item: any) => item.role === 'user').length || 0,
      lawyerCount:
        statsData?.filter((item: any) => item.role === 'lawyer').length || 0,
      userPosition: statsData
        ? statsData.findIndex(
            (item: any) => item.created_at === data.created_at
          ) + 1
        : undefined,
    };

    // Send emails (don't block the response on email sending)
    console.log('Attempting to send waitlist emails for:', validatedData.email);
    EmailService.sendWaitlistEmails({
      userName: validatedData.full_name,
      userEmail: validatedData.email,
      role: validatedData.role,
      waitlistStats: stats,
    })
      .then((emailResult) => {
        console.log('Email sending completed:', emailResult);
      })
      .catch((emailError: any) => {
        console.error('Email sending failed (non-blocking):', emailError);
        console.error('Email error stack:', emailError.stack);
      });

    return NextResponse.json(
      {
        message: 'Successfully joined the waitlist!',
        data: {
          id: data.id,
          email: data.email,
          role: data.role,
          created_at: data.created_at,
          position: stats.userPosition,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get server client
    const supabase = await supabaseServer();

    // Get waitlist statistics (for admin use)
    const { data, error } = await supabase
      .from('waitlist')
      .select('role, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch waitlist data' },
        { status: 500 }
      );
    }

    const stats = {
      total: data.length,
      users: data.filter((item: any) => item.role === 'user').length,
      lawyers: data.filter((item: any) => item.role === 'lawyer').length,
      recent: data.slice(0, 10).map((item: any) => ({
        role: item.role,
        created_at: item.created_at,
      })),
    };

    return NextResponse.json({ stats }, { status: 200 });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

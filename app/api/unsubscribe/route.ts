import { EmailService } from '@/lib/services/emailService';
import { supabaseServer } from '@/lib/supabase/server-client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { email, type } = await request.json();

    // Validate input
    if (!email || !type) {
      return NextResponse.json(
        { error: 'Email and type are required' },
        { status: 400 }
      );
    }

    if (!['waitlist', 'notifications', 'all'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid unsubscribe type' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const supabase = await supabaseServer();

    // Handle different unsubscribe types
    switch (type) {
      case 'waitlist':
        // Remove from waitlist audience in Resend
        const waitlistResult =
          await EmailService.removeFromWaitlistAudience(email);
        if (!waitlistResult.success) {
          console.error(
            'Failed to remove from waitlist audience:',
            waitlistResult.error
          );
          // Continue anyway - we'll still update our database
        }

        // Update waitlist record in database to mark as unsubscribed
        const { error: waitlistError } = await supabase
          .from('waitlist')
          .update({
            unsubscribed: true,
            unsubscribed_at: new Date().toISOString(),
          })
          .eq('email', email);

        if (waitlistError) {
          console.error(
            'Failed to update waitlist unsubscribe status:',
            waitlistError
          );
          return NextResponse.json(
            { error: 'Failed to update unsubscribe status' },
            { status: 500 }
          );
        }
        break;

      case 'notifications':
        // Update user preferences to disable notifications
        const { error: notificationError } = await supabase
          .from('user_preferences')
          .upsert(
            {
              email,
              email_notifications: false,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: 'email',
            }
          );

        if (notificationError) {
          console.error(
            'Failed to update notification preferences:',
            notificationError
          );
          return NextResponse.json(
            { error: 'Failed to update notification preferences' },
            { status: 500 }
          );
        }
        break;

      case 'all':
        // Handle unsubscribe from all emails
        // Remove from waitlist
        await EmailService.removeFromWaitlistAudience(email);

        // Update waitlist record
        await supabase
          .from('waitlist')
          .update({
            unsubscribed: true,
            unsubscribed_at: new Date().toISOString(),
          })
          .eq('email', email);

        // Update notification preferences
        await supabase.from('user_preferences').upsert(
          {
            email,
            email_notifications: false,
            marketing_emails: false,
            updated_at: new Date().toISOString(),
          },
          {
            onConflict: 'email',
          }
        );
        break;
    }

    // Log the unsubscribe action
    const { error: logError } = await supabase
      .from('email_unsubscribes')
      .insert({
        email,
        type,
        unsubscribed_at: new Date().toISOString(),
        ip_address:
          request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          request.headers.get('cf-connecting-ip') ||
          'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
      });

    if (logError) {
      console.error('Failed to log unsubscribe action:', logError);
      // Don't fail the request for logging errors
    }

    return NextResponse.json({
      success: true,
      message: `Successfully unsubscribed from ${type} emails`,
      email,
      type,
    });
  } catch (error) {
    console.error('Unsubscribe API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle GET requests for one-click unsubscribe (required by email standards)
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const email = searchParams.get('email');
  const type = searchParams.get('type');

  if (!email || !type) {
    return NextResponse.json(
      { error: 'Email and type parameters are required' },
      { status: 400 }
    );
  }

  // For GET requests, redirect to the unsubscribe page
  const unsubscribeUrl = `/unsubscribe?email=${encodeURIComponent(email)}&type=${type}`;
  return NextResponse.redirect(new URL(unsubscribeUrl, request.url));
}

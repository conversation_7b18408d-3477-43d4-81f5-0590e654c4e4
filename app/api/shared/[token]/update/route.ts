import { Database } from '@/lib/supabase/database-types';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  // Set headers to ensure we return JSON
  const headers = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    Pragma: 'no-cache',
    Expires: '0',
  };
  try {
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get: async (name) => (await cookies()).get(name)?.value,
          set: async (name, value, options) => {
            (await cookies()).set({ name, value, ...options });
          },
          remove: async (name, options) => {
            (await cookies()).set({ name, value: '', ...options });
          },
        },
      }
    );
    const { token } = await params;

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400, headers }
      );
    }

    // Get the document updates from the request body
    const { sections, password } = await request.json();

    if (!sections) {
      return NextResponse.json(
        { error: 'Document content is required' },
        { status: 400, headers }
      );
    }

    // Get the share link details
    const { data: shareLink, error: shareLinkError } = await supabase
      .from('document_share_links')
      .select('*')
      .eq('token', token)
      .eq('is_active', true)
      .single();

    if (shareLinkError) {
      if (shareLinkError.code === 'PGRST116') {
        // No rows returned - link not found
        return NextResponse.json(
          { error: 'This share link is invalid or has been removed' },
          { status: 404, headers }
        );
      }

      // Other database errors
      console.error('Database error fetching share link:', shareLinkError);
      return NextResponse.json(
        { error: 'Unable to verify the share link' },
        { status: 500, headers }
      );
    }

    if (!shareLink) {
      return NextResponse.json(
        { error: 'Invalid share link' },
        { status: 404, headers }
      );
    }

    // Check if the link has expired
    if (shareLink.expires_at && new Date(shareLink.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'This share link has expired' },
        { status: 403, headers }
      );
    }

    // Check if the link allows editing
    if (shareLink.permission !== 'edit') {
      return NextResponse.json(
        { error: 'This link does not allow editing' },
        { status: 403, headers }
      );
    }

    // Check if password protection is required for editing
    if (
      shareLink.access_type === 'password_protected' &&
      shareLink.permission === 'edit'
    ) {
      // If no password is provided, return a 401
      if (!password) {
        return NextResponse.json(
          { error: 'Password is required to edit this document' },
          { status: 401, headers }
        );
      }

      // Verify the password
      const { data: verifyResult, error: verifyError } = await supabase.rpc(
        'verify_password',
        {
          password,
          hash: shareLink.edit_password as string,
        }
      );

      if (verifyError) {
        console.error('Error verifying password:', verifyError);
        return NextResponse.json(
          { error: 'Failed to verify password' },
          { status: 500, headers }
        );
      }

      if (verifyResult !== true) {
        return NextResponse.json(
          { error: 'Incorrect password' },
          { status: 403, headers }
        );
      }
    }

    // Update the document content
    const { data: document, error: documentError } = await supabase
      .from('documents')
      .update({
        content: { sections },
        updated_at: new Date().toISOString(),
      })
      .eq('id', shareLink.document_id)
      .select()
      .single();

    if (documentError) {
      console.error('Error updating document:', documentError);
      return NextResponse.json(
        { error: 'Failed to update document' },
        { status: 500, headers }
      );
    }

    // Track the edit activity
    try {
      await supabase.from('document_activities').insert({
        // Replace 'document_id' with the correct foreign key field if needed, e.g., 'doc_id'
        document_id: shareLink.document_id,
        activity_type: 'edit',
        activity_data: {
          via_share_link: true,
          share_link_id: shareLink.id,
        },
        user_id: '', // Use an empty string or a valid user id if available
      });
    } catch (activityError) {
      // Log but don't fail if tracking fails
      console.error('Error tracking edit activity:', activityError);
    }

    // Return success response
    return NextResponse.json(
      {
        success: true,
        document,
      },
      { headers }
    );
  } catch (error) {
    console.error('Error in update shared document API:', error);
    return NextResponse.json(
      {
        error:
          'We encountered an error while processing your request. Please try again later.',
      },
      { status: 500, headers }
    );
  }
}

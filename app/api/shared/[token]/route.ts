import { Database } from '@/lib/supabase/database-types';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  // Set headers to ensure we return JSON
  const headers = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    Pragma: 'no-cache',
    Expires: '0',
  };
  // Get the PIN from the query parameters if provided
  const providedPin = request.nextUrl.searchParams.get('pin');
  try {
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get: async (name) => (await cookies()).get(name)?.value,
          set: async (name, value, options) => {
            (await cookies()).set({ name, value, ...options });
          },
          remove: async (name, options) => {
            (await cookies()).set({ name, value: '', ...options });
          },
        },
      }
    );
    const { token } = await params;

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400, headers }
      );
    }

    // Get the share link details
    const { data: shareLink, error: shareLinkError } = await supabase
      .from('document_share_links')
      .select('*')
      .eq('token', token)
      .eq('is_active', true)
      .single();

    if (shareLinkError) {
      if (shareLinkError.code === 'PGRST116') {
        // No rows returned - link not found
        return NextResponse.json(
          { error: 'This share link is invalid or has been removed' },
          { status: 404, headers }
        );
      }

      // Other database errors
      console.error('Database error fetching share link:', shareLinkError);
      return NextResponse.json(
        { error: 'Unable to verify the share link' },
        { status: 500, headers }
      );
    }

    if (!shareLink) {
      return NextResponse.json(
        { error: 'Invalid share link' },
        { status: 404, headers }
      );
    }

    // Check if the link has expired
    if (shareLink.expires_at && new Date(shareLink.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'This share link has expired' },
        { status: 403, headers }
      );
    }

    // Check if PIN protection is required
    if (shareLink.access_type === 'pin_protected') {
      // If no PIN is provided, return a 401 with a flag indicating PIN is required
      if (!providedPin) {
        return NextResponse.json(
          {
            error: 'This document requires a PIN to access',
            requiresPin: true,
            documentTitle: 'Protected Document',
          },
          { status: 401, headers }
        );
      }

      // If PIN is provided but incorrect, return a 403
      if (providedPin !== shareLink.access_pin) {
        return NextResponse.json(
          { error: 'Incorrect PIN provided' },
          { status: 403, headers }
        );
      }
    }

    // Check if password protection is required for editing
    const isPasswordProtected =
      shareLink.access_type === 'password_protected' &&
      shareLink.permission === 'edit';

    try {
      // Track access to the share link
      console.log('API route tracking access for share link ID:', shareLink.id);
      const { data: trackResult, error: trackError } = await supabase.rpc(
        'track_share_link_access',
        {
          link_id: shareLink.id,
          provided_pin: providedPin ?? undefined,
        }
      );

      if (trackError) {
        // Log but don't fail if tracking fails
        console.error('Error tracking share link access:', trackError);
        console.error(
          'Track error details:',
          JSON.stringify(trackError, null, 2)
        );

        // Fallback: Try to update the access count directly if the RPC fails
        try {
          const { error: updateError } = await supabase
            .from('document_share_links')
            .update({
              access_count: (shareLink.access_count || 0) + 1,
              last_accessed_at: new Date().toISOString(),
            })
            .eq('id', shareLink.id);

          if (updateError) {
            console.error('Fallback update failed:', updateError);
            console.error(
              'Update error details:',
              JSON.stringify(updateError, null, 2)
            );
          } else {
            console.log('Fallback access tracking successful');
          }
        } catch (fallbackError) {
          console.error('Exception in fallback tracking:', fallbackError);
          if (fallbackError instanceof Error) {
            console.error('Error message:', fallbackError.message);
          }
        }
      } else {
        console.log('Access tracked successfully:', trackResult);
      }
    } catch (trackError) {
      // Log but don't fail if tracking fails
      console.error('Exception tracking share link access:', trackError);
      if (trackError instanceof Error) {
        console.error('Error message:', trackError.message);
        console.error('Error stack:', trackError.stack);
      }
    }

    // Get the document
    const { data: document, error: documentError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', shareLink.document_id)
      .single();

    if (documentError) {
      if (documentError.code === 'PGRST116') {
        // No rows returned - document not found
        return NextResponse.json(
          { error: 'The document has been deleted or is no longer available' },
          { status: 404, headers }
        );
      }

      // Other database errors
      console.error('Database error fetching document:', documentError);
      return NextResponse.json(
        { error: 'Unable to retrieve the document' },
        { status: 500, headers }
      );
    }

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404, headers }
      );
    }

    // Validate document data before returning
    if (!document.id || !document.title) {
      console.error('Invalid document data:', document);
      return NextResponse.json(
        { error: 'The document data is incomplete or corrupted' },
        { status: 500, headers }
      );
    }

    // Return a successful response with the document and permission
    return NextResponse.json(
      {
        document,
        permission: shareLink.permission || 'view', // Default to view if not specified
        isPasswordProtected: isPasswordProtected,
        accessType: shareLink.access_type,
        canEdit: shareLink.permission === 'edit',
      },
      { headers }
    );
  } catch (error) {
    console.error('Error in shared document API:', error);
    return NextResponse.json(
      {
        error:
          'We encountered an error while processing your request. Please try again later.',
      },
      { status: 500, headers }
    );
  }
}

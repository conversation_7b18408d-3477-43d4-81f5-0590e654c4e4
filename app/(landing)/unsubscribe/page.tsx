'use client';

import { But<PERSON> } from '@/components/ui/button';
import { StrippedBg } from '@/components/ui/stripped-bg';
import { BlurInSection } from '@/components/ux/animations/blur-in';
import { LeftDashes } from '@/components/ux/lines/left-dashes';
import { RightDashes } from '@/components/ux/lines/right-dashes';
import { FONT_BIRCOLAGE_GROTESQUE } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { CheckCircle, Loader2, Mail, XCircle } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

type UnsubscribeType = 'waitlist' | 'notifications' | 'all';

interface UnsubscribeState {
  loading: boolean;
  success: boolean;
  error: string | null;
  email: string | null;
  type: UnsubscribeType | null;
}

// Component that uses useSearchParams - needs to be wrapped in Suspense
function UnsubscribeContent() {
  const searchParams = useSearchParams();
  const [state, setState] = useState<UnsubscribeState>({
    loading: false,
    success: false,
    error: null,
    email: null,
    type: null,
  });

  useEffect(() => {
    const email = searchParams.get('email');
    const type = searchParams.get('type') as UnsubscribeType;

    if (!email || !type) {
      setState((prev) => ({
        ...prev,
        error: 'Invalid unsubscribe link. Missing email or type parameter.',
      }));
      return;
    }

    setState((prev) => ({
      ...prev,
      email,
      type,
    }));
  }, [searchParams]);

  const handleUnsubscribe = async () => {
    if (!state.email || !state.type) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: state.email,
          type: state.type,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to unsubscribe');
      }

      setState((prev) => ({
        ...prev,
        loading: false,
        success: true,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      }));
    }
  };

  const getTypeDescription = (type: UnsubscribeType) => {
    switch (type) {
      case 'waitlist':
        return 'waitlist updates and communications';
      case 'notifications':
        return 'document notifications and activity alerts';
      case 'all':
        return 'all email communications';
      default:
        return 'selected email communications';
    }
  };

  if (state.success) {
    return (
      <main className="mx-auto flex min-h-screen w-full max-w-7xl flex-col items-center justify-center relative bg-neutral-100 p-4">
        <StrippedBg />
        <LeftDashes />
        <RightDashes />

        <BlurInSection className="flex flex-col items-center relative justify-center h-fit w-full max-w-lg divide-accent-50/40 p-4">
          <div className="flex w-full flex-col items-center justify-center border-b pb-4 pt-2">
            {/* Success Icon */}
            <div className="relative z-0 flex h-24 w-24 flex-col items-center justify-center overflow-hidden rounded-full border border-green-200 bg-white">
              <div className="absolute inset-0 z-1 bg-gradient-to-b from-transparent to-green-100" />
              <div className="relative z-2 flex h-16 w-16 flex-col items-center justify-center rounded-full border border-green-200 bg-white">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </div>
            {/* Success Message */}
            <div className={cn('space-y-1 text-center mt-4')}>
              <h2
                className={cn(
                  FONT_BIRCOLAGE_GROTESQUE.className,
                  'text-2xl font-semibold text-green-900'
                )}
              >
                Successfully Unsubscribed
              </h2>
              <p className="text-gray-600">
                You have been unsubscribed from{' '}
                {getTypeDescription(state.type!)} for {state.email}.
              </p>
            </div>
          </div>

          <div className="w-full mt-6 space-y-4">
            <p className="text-sm text-gray-600 text-center">
              You will stop receiving these emails within 48 hours.
            </p>
            <div className='flex flex-wrap items-center justify-center gap-4'>

            <Button
              onClick={() => (window.location.href = '/')}
              
              variant="shadow_accent"
              >
              Return to Homepage
            </Button>
              </div>
          </div>
        </BlurInSection>
      </main>
    );
  }

  if (state.error) {
    return (
      <main className="mx-auto flex min-h-screen w-full max-w-7xl flex-col items-center justify-center relative bg-neutral-100 p-4">
        <StrippedBg />
        <LeftDashes />
        <RightDashes />

        <BlurInSection className="flex flex-col items-center relative justify-center h-fit w-full max-w-lg divide-accent-50/40 p-4">
          <div className="flex w-full flex-col items-center justify-center border-b pb-4 pt-2">
            {/* Error Icon */}
            <div className="relative z-0 flex h-24 w-24 flex-col items-center justify-center overflow-hidden rounded-full border border-red-200 bg-white">
              <div className="absolute inset-0 z-1 bg-gradient-to-b from-transparent to-red-100" />
              <div className="relative z-2 flex h-16 w-16 flex-col items-center justify-center rounded-full border border-red-200 bg-white">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </div>
            {/* Error Message */}
            <div className={cn('space-y-1 text-center mt-4')}>
              <h2
                className={cn(
                  FONT_BIRCOLAGE_GROTESQUE.className,
                  'text-2xl font-semibold text-red-900'
                )}
              >
                Unsubscribe Failed
              </h2>
              <p className="text-gray-600">{state.error}</p>
            </div>
          </div>

          <div className="w-full mt-6">
            <Button
              onClick={() => (window.location.href = '/')}
              variant="outline"
              className="w-full"
            >
              Return to Homepage
            </Button>
          </div>
        </BlurInSection>
      </main>
    );
  }

  return (
    <main className="mx-auto flex min-h-screen w-full max-w-7xl flex-col items-center justify-center relative bg-neutral-100 p-4">
      <StrippedBg />
      <LeftDashes />
      <RightDashes />

      <BlurInSection className="flex flex-col items-center relative justify-center h-fit w-full max-w-lg divide-accent-50/40 p-4">
        <div className="flex w-full flex-col items-center justify-center border-b pb-4 pt-2">
          {/* Mail Icon */}
          <div className="relative z-0 flex h-24 w-24 flex-col items-center justify-center overflow-hidden rounded-full border border-gray-200 bg-white">
            <div className="absolute inset-0 z-1 bg-gradient-to-b from-transparent to-gray-100" />
            <div className="relative z-2 flex h-16 w-16 flex-col items-center justify-center rounded-full border border-gray-200 bg-white">
              <Mail className="h-8 w-8" />
            </div>
          </div>

          {/* Message */}
          <div className={cn('space-y-1 text-center mt-4')}>
            <h2
              className={cn(
                FONT_BIRCOLAGE_GROTESQUE.className,
                'text-2xl font-semibold'
              )}
            >
              Unsubscribe from Emails
            </h2>
            <p className="text-gray-600">
              {state.email && state.type ? (
                <>
                  Unsubscribe <strong>{state.email}</strong> from{' '}
                  <strong>{getTypeDescription(state.type)}</strong>
                </>
              ) : (
                'Loading unsubscribe information...'
              )}
            </p>
          </div>
        </div>

        <div className="w-full mt-6 space-y-4">
          <div className="text-sm text-gray-600 text-center">
            <p>
              By clicking "Unsubscribe", you will stop receiving the selected
              type of emails. You can always re-subscribe later if you change
              your mind.
            </p>
          </div>
<div className='flex flex-wrap items-center justify-center gap-4'>

          <Button
            onClick={handleUnsubscribe}
            disabled={state.loading || !state.email || !state.type}
            variant="shadow_red"
          >
            {state.loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Unsubscribing...
              </>
            ) : (
              'Unsubscribe'
            )}
          </Button>

          <Button
            onClick={() => (window.location.href = '/')}
            variant="shadow"
          >
            Cancel
          </Button>
</div>
        </div>
      </BlurInSection>
    </main>
  );
}

// Loading component for Suspense fallback
function UnsubscribeLoading() {
  return (
    <main className="mx-auto flex min-h-screen w-full max-w-7xl flex-col items-center justify-center relative bg-neutral-100 p-4">
      <StrippedBg />
      <LeftDashes />
      <RightDashes />

      <BlurInSection className="flex flex-col items-center relative justify-center h-fit w-full max-w-lg divide-accent-50/40 p-4">
        <div className="flex w-full flex-col items-center justify-center border-b pb-4 pt-2">
          {/* Loading Icon */}
          <div className="relative z-0 flex h-24 w-24 flex-col items-center justify-center overflow-hidden rounded-full border border-gray-200 bg-white">
            <div className="absolute inset-0 z-1 bg-gradient-to-b from-transparent to-gray-100" />
            <div className="relative z-2 flex h-16 w-16 flex-col items-center justify-center rounded-full border border-gray-200 bg-white">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          </div>
          {/* Loading Message */}
          <div className={cn('space-y-1 text-center mt-4')}>
            <h2
              className={cn(
                FONT_BIRCOLAGE_GROTESQUE.className,
                'text-2xl font-semibold'
              )}
            >
              Loading...
            </h2>
            <p className="text-gray-600">Loading unsubscribe information...</p>
          </div>
        </div>
      </BlurInSection>
    </main>
  );
}

// Main page component with Suspense boundary
export default function UnsubscribePage() {
  return (
    <Suspense fallback={<UnsubscribeLoading />}>
      <UnsubscribeContent />
    </Suspense>
  );
}

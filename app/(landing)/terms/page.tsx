import { StrippedBg } from '@/components/ui/stripped-bg';
import { BlurInSection } from '@/components/ux/animations/blur-in';
import { DocCard } from '@/components/ux/comp/doc-card';
import { LeftDashes } from '@/components/ux/lines/left-dashes';
import { RightDashes } from '@/components/ux/lines/right-dashes';
import { FONT_BIRCOLAGE_GROTESQUE, FONT_JETBRAINS_MONO } from '@/lib/constants';
import { cn, constructMetadata } from '@/lib/utils';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';

export const metadata = constructMetadata({
  title: 'Terms of Service | Notamess Forms',
  description:
    'Terms of Service for Notamess Forms - Learn about the terms governing your use of our platform.',
});

export default function TermsOfService() {
  return (
    <main className="p-4 min-h-screen flex flex-col w-full">
      <div className="mx-auto max-w-4xl relative py-20 px-2">
        {/* stripped bg */}
        <StrippedBg />
        {/* Vertical Lines */}
        <div className="pointer-events-none inset-0 select-none">
          <LeftDashes />
          <RightDashes />
        </div>

        <div className="px-4 md:px-8 h-14 flex items-center relative">
          <Link
            href="/"
            className={cn(
              'inline-flex items-center group uppercase font-medium text-sm text-neutral-400  hover:text-accent-300 transition-colors',
              FONT_JETBRAINS_MONO.className
            )}
          >
            <ChevronLeft className="mr-1 h-5 w-5 group-hover:-translate-x-1 transition-all duration-200" />
            Back to Home
          </Link>
        </div>
        <BlurInSection className="">
          <DocCard className="rounded-none">
            <div className="prose prose-neutral max-w-none p-4 md:p-8 text-neutral-700 tracking-normal">
              <h1
                className={cn(
                  FONT_BIRCOLAGE_GROTESQUE.className,
                  'text-4xl font-bold text-accent-300 mb-4'
                )}
              >
                Terms of Service
              </h1>
              <p
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xs tracking-tighter uppercase text-neutral-500 mb-8'
                )}
              >
                Last Updated: April 30, 2025
              </p>

              <p className="mb-6">
                Notamess Forms, a legal software platform designed to simplify
                the creation, management, and sharing of legal documents and
                contracts. These Terms of Service (&quot;Terms&quot;) govern
                your access to and use of our website, mobile applications, and
                services (collectively, the &quot;Services&quot;) provided by
                Notamess Forms (&quot;we,&quot; &quot;us,&quot; or
                &quot;our&quot;). By using the Services, you agree to be bound
                by these Terms. If you do not agree, please do not use the
                Services.
              </p>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                1. Eligibility
              </h2>
              <p className="mb-4">
                You must be at least 18 years old and have the legal capacity to
                enter into contracts to use the Services. By using the Services,
                you represent that you meet these requirements.
              </p>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                2. Account Registration
              </h2>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  <span className="font-semibold">Account Creation:</span> To
                  access certain features, you must create an account by
                  providing accurate and complete information, including your
                  name, email address, and password.
                </li>
                <li>
                  <span className="font-semibold">Account Security:</span> You
                  are responsible for maintaining the confidentiality of your
                  account credentials and for all activities under your account.
                  Notify us immediately at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-accent-300 hover:underline"
                  >
                    <EMAIL>
                  </a>{' '}
                  if you suspect unauthorized use.
                </li>
                <li>
                  <span className="font-semibold">Account Termination:</span> We
                  may suspend or terminate your account if you violate these
                  Terms or engage in activities that harm the Services or other
                  users.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                3. Use of the Services
              </h2>

              <h3
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-l  uppercase tracking-tight font-bold mt-6 mb-3'
                )}
              >
                a. Permitted Use
              </h3>
              <p className="mb-4">
                You may use the Services to create, edit, manage, and share
                legal documents and contracts, request legal advice, and execute
                smart contracts on the blockchain, subject to these Terms.
              </p>

              <h3
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-l  uppercase tracking-tight font-bold mt-6 mb-3'
                )}
              >
                b. Prohibited Conduct
              </h3>
              <p className="mb-2">You agree not to:</p>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>Use the Services for illegal or unauthorized purposes.</li>
                <li>
                  Upload, share, or create content that is defamatory, obscene,
                  or infringes on intellectual property rights.
                </li>
                <li>
                  Attempt to access, modify, or disrupt the Services&apos;
                  infrastructure, including through hacking or introducing
                  malware.
                </li>
                <li>Misrepresent your identity or impersonate others.</li>
                <li>
                  Use the Services to harass, harm, or discriminate against
                  others.
                </li>
              </ul>

              <h3
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-l  uppercase tracking-tight font-bold mt-6 mb-3'
                )}
              >
                c. User Content
              </h3>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  You retain ownership of the documents and data you create or
                  upload (&quot;User Content&quot;). By using the Services, you
                  grant us a non-exclusive, worldwide, royalty-free license to
                  use, store, and process your User Content solely to provide
                  and improve the Services.
                </li>
                <li>
                  You are responsible for ensuring your User Content complies
                  with applicable laws and does not infringe on third-party
                  rights.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                4. Legal Advice
              </h2>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  <span className="font-semibold">Not a Law Firm:</span>{' '}
                  Notamess Forms is a technology platform, not a law firm. While
                  we facilitate access to professional lawyers, any legal advice
                  provided through the Services is offered by third-party legal
                  professionals, not Notamess Forms.
                </li>
                <li>
                  <span className="font-semibold">
                    No Attorney-Client Relationship:
                  </span>{' '}
                  Using the Services does not create an attorney-client
                  relationship between you and Notamess Forms unless explicitly
                  agreed with a legal professional.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                5. Payments and Subscriptions
              </h2>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  <span className="font-semibold">Fees:</span> Certain features
                  require payment. All fees are non-refundable unless otherwise
                  stated.
                </li>
                <li>
                  <span className="font-semibold">Payment Processing:</span>{' '}
                  Payments are processed through third-party providers. You
                  agree to provide accurate payment information and authorize us
                  to charge applicable fees.
                </li>
                <li>
                  <span className="font-semibold">
                    Subscription Cancellation:
                  </span>{' '}
                  You may cancel your subscription at any time through your
                  account settings. No refunds will be provided for partial
                  billing periods.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                6. Intellectual Property
              </h2>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  <span className="font-semibold">Our Content:</span> The
                  Services, including text, software, logos, and designs, are
                  owned by Notamess Forms or our licensors and protected by
                  intellectual property laws. You may not copy, modify, or
                  distribute our content without permission.
                </li>
                <li>
                  <span className="font-semibold">Feedback:</span> If you
                  provide feedback or suggestions, you grant us a perpetual,
                  royalty-free license to use and implement them.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                7. Termination
              </h2>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  <span className="font-semibold">By You:</span> You may stop
                  using the Services at any time and delete your account.
                </li>
                <li>
                  <span className="font-semibold">By Us:</span> We may suspend
                  or terminate your access to the Services for violating these
                  Terms, with or without notice.
                </li>
                <li>
                  <span className="font-semibold">Effect of Termination:</span>{' '}
                  Upon termination, your right to use the Services ceases, but
                  User Content on the blockchain remains immutable.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                8. Disclaimers and Limitation of Liability
              </h2>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  <span className="font-semibold">As-Is Basis:</span> The
                  Services are provided &quot;as is&quot; without warranties of
                  any kind, express or implied, including accuracy or fitness
                  for a particular purpose.
                </li>
                <li>
                  <span className="font-semibold">
                    No Liability for Third Parties:
                  </span>{' '}
                  We are not liable for the actions or advice of third-party
                  legal professionals or service providers.
                </li>
                <li>
                  <span className="font-semibold">
                    Limitation of Liability:
                  </span>{' '}
                  To the fullest extent permitted by law, Notamess Forms shall
                  not be liable for indirect, incidental, or consequential
                  damages arising from your use of the Services. Our total
                  liability shall not exceed the amount you paid us in the
                  preceding 12 months.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                9. Indemnification
              </h2>
              <p className="mb-4">
                You agree to indemnify and hold Notamess Forms harmless from any
                claims, losses, or damages arising from your use of the
                Services, violation of these Terms, or infringement of
                third-party rights.
              </p>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                10. Governing Law and Dispute Resolution
              </h2>
              <ul className="list-disc pl-6 space-y-2 mb-4">
                <li>
                  <span className="font-semibold">Governing Law:</span> These
                  Terms are governed by the laws of the State of California,
                  USA, without regard to conflict of law principles.
                </li>
                <li>
                  <span className="font-semibold">Arbitration:</span> Any
                  disputes arising from these Terms or the Services shall be
                  resolved through binding arbitration in California, USA, under
                  the rules of the American Arbitration Association. You waive
                  the right to participate in class actions.
                </li>
              </ul>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                11. Changes to These Terms
              </h2>
              <p className="mb-4">
                We may update these Terms to reflect changes in our Services or
                legal requirements. We will notify you of significant changes
                via email or through the Services. Your continued use of the
                Services after such changes constitutes acceptance of the
                revised Terms.
              </p>

              <h2
                className={cn(
                  FONT_JETBRAINS_MONO.className,
                  'text-xl uppercase tracking-tight  font-bold text-accent-300 mt-8 mb-4'
                )}
              >
                12. Contact Us
              </h2>
              <p className="mb-4">
                For questions or concerns about these Terms, please contact us
                at:
              </p>
              <div className="mb-4">
                <p className="font-semibold">Notamess Forms</p>
                <p>
                  Email:{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-accent-300 hover:underline"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </DocCard>
        </BlurInSection>
      </div>
    </main>
  );
}

import { WaitlistForm } from '@/components/forms/waitlist-form';
import { StrippedBg } from '@/components/ui/stripped-bg';
import { BlurInSection } from '@/components/ux/animations/blur-in';
import { LeftDashes } from '@/components/ux/lines/left-dashes';
import { RightDashes } from '@/components/ux/lines/right-dashes';
import { FONT_BIRCOLAGE_GROTESQUE, FONT_JETBRAINS_MONO } from '@/lib/constants';
import { cn, constructMetadata } from '@/lib/utils';
import Image from 'next/image';
import Link from 'next/link';
import Logo from '@/public/FormsLogo.svg';

export const metadata = constructMetadata({
  title: 'Join Waitlist | Notamess Forms',
  description:
    'Join the waitlist for Notamess Forms - The future of legal document creation and management.',
});

export default function WaitlistPage() {
  return (
    <main className="p-4 min-h-screen overflow-hidden pt-20">
      <section className="mx-auto flex max-w-6xl h-full relative flex-col items-center justify-center">
        {/* stripped bg */}
        <StrippedBg />
        {/* Vertical Lines */}
        <div className="pointer-events-none inset-0 select-none">
          <LeftDashes />
          <RightDashes />
        </div>

        <BlurInSection className="flex flex-col items-center relative justify-center h-fit w-full max-w-lg mb-20 divide-accent-50/40 p-4">
          <div className="flex flex-col text-center items-center p-2 space-y-4">
            <Link href="/" className="w-16 cursor-pointer">
              <Image alt="logo" src={Logo} />
            </Link>

            <h1
              className={cn(
                FONT_BIRCOLAGE_GROTESQUE.className,
                'text-center text-3xl font-bold text-accent-300'
              )}
            >
              Join the Waitlist
            </h1>

            <p className="text-neutral-500 text-center max-w-md">
              Be among the first to experience the future of legal document
              creation. Get early access to Notamess Forms and revolutionize how
              you handle legal paperwork.
            </p>
          </div>

          <div className="w-full mt-6">
            <WaitlistForm />
          </div>

          <div className="mt-6 text-center">
            <Link
              href="/"
              className={cn(
                FONT_JETBRAINS_MONO.className,
                'text-sm text-neutral-400 hover:text-accent-300 transition-colors'
              )}
            >
              ← Back to Home
            </Link>
          </div>
        </BlurInSection>
      </section>
    </main>
  );
}

import { createServerClient } from '@/lib/supabase/server-client';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') || '/';
  const provider = requestUrl.searchParams.get('provider');

  try {
    if (code) {
      const supabase = await createServerClient();

      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(
          new URL(
            `/auth-error?message=${encodeURIComponent(error.message)}`,
            requestUrl.origin
          )
        );
      }

      // If this is a social login, we need to handle the user profile
      if (provider && data?.session?.user) {
        const user = data.session.user;

        // Get user's full name and avatar from provider data
        const fullName =
          user.user_metadata?.full_name || user.user_metadata?.name || 'User';
        const avatarUrl = user.user_metadata?.avatar_url || null;

        try {
          // Check if user has a profile
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

          if (profileError && profileError.code !== 'PGRST116') {
            // PGRST116 is "no rows returned" which is expected for new users
            console.error('Error checking profile:', profileError);
          }

          if (!profile) {
            try {
              // If no profile exists, create one with a generated username
              // Generate a unique username from the full name
              let username = fullName.toLowerCase().replace(/\s+/g, '');
              username = username.replace(/[^a-z0-9]/g, '');

              // Ensure username is at least 3 characters (constraint requirement)
              if (username.length < 3) {
                username = username + 'user'.substring(0, 3 - username.length);
              }

              // Check if username exists
              const { data: existingUser, error: usernameError } =
                await supabase
                  .from('profiles')
                  .select('username')
                  .eq('username', username);

              if (usernameError) {
                console.error('Error checking username:', usernameError);
                throw usernameError;
              }

              // If username exists, add a number to it
              if (existingUser && existingUser.length > 0) {
                let counter = 1;
                let newUsername = `${username}${counter}`;

                // Keep checking until we find a unique username
                let maxAttempts = 10; // Prevent infinite loops
                let attempts = 0;

                while (attempts < maxAttempts) {
                  const { data: existingUserWithNumber, error: checkError } =
                    await supabase
                      .from('profiles')
                      .select('username')
                      .eq('username', newUsername);

                  if (checkError) {
                    console.error(
                      'Error checking username with number:',
                      checkError
                    );
                    throw checkError;
                  }

                  if (
                    !existingUserWithNumber ||
                    existingUserWithNumber.length === 0
                  ) {
                    username = newUsername;
                    break;
                  }

                  counter++;
                  newUsername = `${username}${counter}`;
                  attempts++;
                }

                if (attempts >= maxAttempts) {
                  // If we couldn't find a unique username after max attempts, use a timestamp
                  username = `${username}${Date.now().toString().slice(-6)}`;
                }
              }

              console.log('Creating new profile with username:', username);

              // Create profile with proper default values for required fields
              const { error: insertError } = await supabase
                .from('profiles')
                .insert({
                  id: user.id,
                  username,
                  full_name: fullName,
                  email: user.email,
                  avatar_url: avatarUrl,
                  updated_at: new Date().toISOString(),
                  is_onboarded: false,
                  user_role: 'user', // Default user role
                  plan: 'Free', // Default plan
                });

              if (insertError) {
                console.error('Error creating profile:', insertError);
                // If there's an error, redirect to an error page
                return NextResponse.redirect(
                  new URL(
                    `/auth-error?message=${encodeURIComponent('Failed to create your account. Please try signing up manually.')}&original_error=${encodeURIComponent(insertError.message)}`,
                    requestUrl.origin
                  )
                );
              }

              // Redirect to onboarding if this is a new user
              return NextResponse.redirect(
                new URL('/onboarding', requestUrl.origin)
              );
            } catch (error) {
              console.error('Error in profile creation process:', error);
              return NextResponse.redirect(
                new URL(
                  `/auth-error?message=${encodeURIComponent('An unexpected error occurred during account creation. Please try signing up manually.')}`,
                  requestUrl.origin
                )
              );
            }
          } else {
            try {
              // If profile exists, update the full name and avatar URL
              const { error: updateError } = await supabase
                .from('profiles')
                .update({
                  full_name: fullName,
                  avatar_url: avatarUrl,
                  updated_at: new Date().toISOString(),
                })
                .eq('id', user.id);

              if (updateError) {
                console.error('Error updating profile:', updateError);
              }
            } catch (error) {
              console.error('Error updating existing profile:', error);
            }
          }
        } catch (error) {
          console.error('Error processing social login:', error);
          return NextResponse.redirect(
            new URL(
              `/auth-error?message=${encodeURIComponent('An error occurred during social login')}`,
              requestUrl.origin
            )
          );
        }
      }
    }

    // For Google OAuth, we'll use a client-side redirect script that can access localStorage
    // This allows us to redirect to the page the user was on before authentication
    return NextResponse.redirect(new URL('/auth/redirect', requestUrl.origin));
  } catch (error) {
    console.error('Unhandled error in OAuth callback:', error);
    return NextResponse.redirect(
      new URL(
        `/auth-error?message=${encodeURIComponent('An unexpected error occurred during authentication')}`,
        requestUrl.origin
      )
    );
  }
}

'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { authService } from '@/lib/supabase/auth/auth-service';
import { AlertTriangle } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

function AuthErrorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    if (!searchParams) return;

    // Get error message from URL
    const message = searchParams.get('message');
    const originalError = searchParams.get('original_error');

    // Set the main error message
    setErrorMessage(message || 'An authentication error occurred');

    // Check if this is a database error saving new user
    if (
      originalError &&
      originalError.includes('Database error saving new user')
    ) {
      setAuthError('Database error saving new user');
    }
  }, [searchParams]);

  const handleRetry = async () => {
    try {
      // Try to sign in with Google again
      await authService.signInWithGoogle();
    } catch (error) {
      console.error('Error retrying Google sign-in:', error);
    }
  };

  const handleGoToLogin = () => {
    router.push('/login');
  };

  // State to track if this is a database error saving new user
  const [authError, setAuthError] = useState<string | null>(null);

  // Check if this is a database error saving new user
  const isDatabaseSavingError =
    authError === 'Database error saving new user' ||
    errorMessage.includes('Database error saving new user');

  // Provide specific guidance for database saving errors
  const renderErrorGuidance = () => {
    if (isDatabaseSavingError) {
      return (
        <div className="mt-4 space-y-2 text-sm">
          <p className="font-medium text-blue-800">
            You need to create an account first
          </p>
          <p>
            This error occurs when you try to sign in with Google using an email
            that hasn't been registered in our system yet.
          </p>

          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
            <p className="font-medium text-amber-800">What happened?</p>
            <p className="mt-1">
              When you sign in with Google, we need to create a user profile for
              you in our database. If this is your first time using our service,
              you need to sign up first.
            </p>
          </div>

          <p className="mt-4 font-medium">How to fix this:</p>
          <ol className="list-decimal pl-5 space-y-1">
            <li>Click the "Create Account" button below</li>
            <li>
              On the sign-up page, you can either:
              <ul className="list-disc pl-5 mt-1">
                <li>Create an account with email and password</li>
                <li>
                  Or click "Sign up with Google" to create an account using your
                  Google credentials
                </li>
              </ul>
            </li>
            <li>
              Once your account is created, you'll be able to sign in with
              Google in the future
            </li>
          </ol>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-neutral-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center gap-2 text-red-500">
            <AlertTriangle className="h-5 w-5" />
            <CardTitle className="text-xl">Authentication Error</CardTitle>
          </div>
          <CardDescription>There was a problem signing you in</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isDatabaseSavingError ? (
            <div className="rounded-md bg-blue-50 p-4 text-sm text-blue-800 border border-blue-200">
              <div className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-info"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 16v-4" />
                  <path d="M12 8h.01" />
                </svg>
                <p className="font-medium">Account Creation Required</p>
              </div>
              <p className="mt-2 pl-6">
                You need to create an account before you can sign in with
                Google.
              </p>
            </div>
          ) : (
            <div className="rounded-md bg-red-50 p-4 text-sm text-red-800">
              {errorMessage}
            </div>
          )}
          <p className="text-sm text-neutral-600">
            This could be due to an issue with your account or a temporary
            problem with our authentication service.
          </p>
          {renderErrorGuidance()}
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          {isDatabaseSavingError ? (
            <>
              <Button
                onClick={() => router.push('/create-account')}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                Create Account
              </Button>
              <Button
                onClick={handleGoToLogin}
                variant="outline"
                className="w-full"
              >
                Go to Login Page
              </Button>
            </>
          ) : (
            <>
              <Button onClick={handleRetry} className="w-full">
                Try Again with Google
              </Button>
              <Button
                onClick={handleGoToLogin}
                variant="outline"
                className="w-full"
              >
                Go to Login Page
              </Button>
            </>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center bg-neutral-50 p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="space-y-1">
              <div className="flex items-center gap-2">
                <CardTitle className="text-xl">Loading...</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p>Please wait while we load the error details...</p>
            </CardContent>
          </Card>
        </div>
      }
    >
      <AuthErrorContent />
    </Suspense>
  );
}

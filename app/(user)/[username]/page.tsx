'use client';

// Onboarding is now handled by the middleware and a dedicated page
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { pageProps } from '@/types';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function DashboardPage({ params }: pageProps) {
  const [username, setUsername] = useState<string>('');
  const { profile } = userStore();
  // We'll keep the useLawyers hook for future use
  const { isLawyer: _ } = useLawyers(); // Rename to _ to avoid unused variable warning
  const router = useRouter();

  useEffect(() => {
    const fetchParams = async () => {
      const unwrappedParams = await params;
      setUsername(unwrappedParams.username);
    };

    fetchParams();
  }, [params]);

  // Check if the current username matches the profile username
  useEffect(() => {
    if (profile && username && profile.username !== username) {
      // If there's a mismatch, redirect to the correct URL
      router.replace(`/${profile.username}`);
    }
  }, [profile, username, router]);

  // We no longer redirect lawyer users to their dashboard
  // They can access both the standard dashboard and the lawyer dashboard

  return (
    <main className="p-3 sm:p-4 md:p-6 max-w-[1920px] mx-auto">
      {/* Onboarding is now handled by a dedicated page */}

      <div className="flex flex-col gap-2 mb-4 md:mb-6">
        <h1 className="text-xl sm:text-2xl font-bold">
          Welcome back, {username}
        </h1>
        <p className="text-sm sm:text-base text-neutral-500">
          Here&apos;s what&apos;s happening with your legal documents and
          projects
        </p>
      </div>
    </main>
  );
}

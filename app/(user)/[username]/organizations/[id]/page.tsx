'use client';

import { TeamCard } from '@/components/enterprise/TeamCard';
import { OrganizationMembersList } from '@/components/organizations/OrganizationMembersList';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useOrganizations, useTeams } from '@/lib/hooks';
import { OrganizationWithDetails, Team } from '@/lib/types/database-modules';
import {
  ArrowLeft,
  BarChart,
  Building2,
  FileText,
  Pencil,
  Plus,
  Settings,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function OrganizationDetailsPage() {
  const { username, id } = useParams();
  const { getOrganization } = useOrganizations();
  const { getOrganizationTeams } = useTeams();

  const [organization, setOrganization] =
    useState<OrganizationWithDetails | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    async function loadData() {
      setLoading(true);
      try {
        const orgId = Array.isArray(id) ? id[0] : (id as string);
        if (!orgId) {
          toast.error('Organization ID is missing');
          return;
        }

        // Use toast.promise for better user feedback
        const orgPromise = getOrganization(orgId);

        toast.promise(orgPromise, {
          loading: 'Loading organization details...',
          success: 'Organization details loaded',
          error: (err) =>
            `Failed to load organization: ${err.message || 'Unknown error'}`,
        });

        const org = await orgPromise;
        setOrganization(org);

        if (org) {
          // Use toast.promise for teams loading
          const teamsPromise = getOrganizationTeams(orgId);

          toast.promise(teamsPromise, {
            loading: 'Loading teams...',
            success: 'Teams loaded successfully',
            error: (err) =>
              `Failed to load teams: ${err.message || 'Unknown error'}`,
          });

          const orgTeams = await teamsPromise;
          setTeams(orgTeams);
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('Error loading organization data:', errorMessage);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [id, getOrganization, getOrganizationTeams]);

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-2 mb-6">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="h-8 w-48 bg-muted rounded-md animate-pulse"></div>
        </div>

        <div className="flex space-x-2 mb-6">
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className="h-10 w-24 bg-muted rounded-md animate-pulse"
            ></div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="h-5 w-5 bg-primary/20 rounded-full mr-2"></div>
                  <div className="h-6 w-32 bg-muted rounded-md animate-pulse"></div>
                </div>
                <div className="h-4 w-48 bg-muted rounded-md animate-pulse mt-1"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div
                      key={i}
                      className="h-16 w-full bg-muted rounded-md animate-pulse"
                    ></div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="h-5 w-5 bg-primary/20 rounded-full mr-2"></div>
                  <div className="h-6 w-40 bg-muted rounded-md animate-pulse"></div>
                </div>
                <div className="h-4 w-56 bg-muted rounded-md animate-pulse mt-1"></div>
              </CardHeader>
              <CardContent>
                <div className="h-32 w-full bg-muted rounded-md animate-pulse"></div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="h-5 w-5 bg-primary/20 rounded-full mr-2"></div>
                  <div className="h-6 w-32 bg-muted rounded-md animate-pulse"></div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex justify-between">
                      <div className="h-4 w-20 bg-muted rounded-md animate-pulse"></div>
                      <div className="h-4 w-16 bg-muted rounded-md animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="h-5 w-5 bg-primary/20 rounded-full mr-2"></div>
                  <div className="h-6 w-32 bg-muted rounded-md animate-pulse"></div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    {[1, 2].map((i) => (
                      <div key={i} className="space-y-2">
                        <div className="h-4 w-16 bg-muted rounded-md animate-pulse"></div>
                        <div className="h-8 w-full bg-muted rounded-md animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-center">
                    <div className="h-9 w-40 bg-muted rounded-md animate-pulse"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link href={`/${username}/organizations`}>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Organization Not Found</h1>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Building2 className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">Organization Not Found</h3>
            <p className="text-sm text-muted-foreground max-w-md mb-4">
              The organization you're looking for doesn't exist or you don't
              have access to it.
            </p>
            <Link href={`/${username}/organizations`}>
              <Button>Go Back to Organizations</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link href={`/${username}/organizations`}>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{organization.name}</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="teams">Teams</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
      </Tabs>

      <TabsContent value="overview" className="mt-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5 text-primary" />
                  Teams
                </CardTitle>
                <CardDescription>
                  Manage your organization's teams
                </CardDescription>
              </CardHeader>
              <CardContent>
                {teams.length === 0 ? (
                  <div className="border-dashed border-2 rounded-lg p-6 text-center">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Teams</h3>
                    <p className="text-sm text-muted-foreground max-w-md mx-auto mb-4">
                      Create teams to organize your members and manage
                      permissions. Teams can collaborate on forms and templates.
                    </p>
                    <Link
                      href={`/${username}/organizations/${organization.id}/teams/new`}
                    >
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Your First Team
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {teams.slice(0, 3).map((team) => (
                      <TeamCard key={team.id} team={team} />
                    ))}

                    {teams.length > 3 && (
                      <div className="text-center mt-4">
                        <Link
                          href={`/${username}/organizations/${organization.id}/teams`}
                        >
                          <Button variant="outline">
                            View All Teams ({teams.length})
                          </Button>
                        </Link>
                      </div>
                    )}

                    <div className="flex justify-end mt-4">
                      <Link
                        href={`/${username}/organizations/${organization.id}/teams/new`}
                      >
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Create Team
                        </Button>
                      </Link>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  Recent Documents
                </CardTitle>
                <CardDescription>
                  Documents created by organization members
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border-dashed border-2 rounded-lg p-6 text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Recent Documents
                  </h3>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto mb-4">
                    Documents created by organization members will appear here.
                  </p>
                  <Link href={`/${username}/documents/new`}>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Document
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building2 className="mr-2 h-5 w-5 text-primary" />
                  Organization Info
                </CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-sm text-muted-foreground">
                      Subscription
                    </dt>
                    <dd className="font-medium capitalize">
                      {organization.subscription_level}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm text-muted-foreground">Members</dt>
                    <dd className="font-medium">
                      {organization.members?.length || 0}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm text-muted-foreground">Teams</dt>
                    <dd className="font-medium">{teams.length}</dd>
                  </div>
                  <div>
                    <dt className="text-sm text-muted-foreground">Created</dt>
                    <dd className="font-medium">
                      {new Date(organization.created_at).toLocaleDateString()}
                    </dd>
                  </div>
                </dl>

                <div className="mt-4">
                  <Link
                    href={`/${username}/organizations/${organization.id}/settings`}
                  >
                    <Button variant="outline" className="w-full">
                      <Settings className="h-4 w-4 mr-2" />
                      Organization Settings
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart className="mr-2 h-5 w-5 text-primary" />
                  Usage Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-muted-foreground">
                        Documents
                      </span>
                      <div className="flex items-center">
                        <FileText className="mr-1.5 h-3.5 w-3.5 text-muted-foreground" />
                        <span className="font-medium">0</span>
                      </div>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-muted-foreground">
                        Templates
                      </span>
                      <div className="flex items-center">
                        <FileText className="mr-1.5 h-3.5 w-3.5 text-muted-foreground" />
                        <span className="font-medium">0</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-center pt-2">
                    <Link
                      href={`/${username}/organizations/${organization.id}/analytics`}
                    >
                      <Button variant="outline" size="sm">
                        <BarChart className="h-4 w-4 mr-2" />
                        View Detailed Analytics
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="members" className="mt-0">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5 text-primary" />
              Organization Members
            </CardTitle>
            <CardDescription>
              Manage members and their roles within the organization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OrganizationMembersList
              organizationId={organization.id}
              initialMembers={organization.members || []}
            />
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="teams" className="mt-0">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Teams</h2>
          <Link
            href={`/${username}/organizations/${organization.id}/teams/new`}
          >
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Team
            </Button>
          </Link>
        </div>

        {teams.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8 text-center">
              <Users className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Teams</h3>
              <p className="text-sm text-muted-foreground max-w-md mb-4">
                Create teams to organize your members and manage permissions.
                Teams can collaborate on forms and templates.
              </p>
              <Link
                href={`/${username}/organizations/${organization.id}/teams/new`}
              >
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Team
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {teams.map((team) => (
              <TeamCard key={team.id} team={team} />
            ))}
          </div>
        )}
      </TabsContent>

      <TabsContent value="settings" className="mt-0">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Organization Settings</h2>
          <Link href={`/${username}/organizations/${organization.id}/settings`}>
            <Button>
              <Settings className="h-4 w-4 mr-2" />
              All Settings
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Organization Details</CardTitle>
              <CardDescription>
                View and update your organization information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium">Organization Name</h3>
                  <p>{organization.name}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Description</h3>
                  <p>{organization.description || 'No description provided'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Subscription Level</h3>
                  <p className="capitalize">
                    {organization.subscription_level}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Contact Email</h3>
                  <p>{organization.contact_email || 'Not set'}</p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Link
                href={`/${username}/organizations/${organization.id}/settings`}
                className="w-full"
              >
                <Button variant="outline" className="w-full">
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit Organization Details
                </Button>
              </Link>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Branding</CardTitle>
              <CardDescription>
                Customize your organization's appearance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium">Logo</h3>
                  <div className="mt-2 flex items-center justify-center h-24 w-24 rounded-md border border-dashed">
                    {organization.logo_url ? (
                      <img
                        src={organization.logo_url}
                        alt="Organization logo"
                        className="h-full w-full object-contain p-2"
                      />
                    ) : (
                      <Building2 className="h-8 w-8 text-muted-foreground" />
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium">Primary Color</h3>
                    <div
                      className="mt-2 h-8 w-full rounded-md border"
                      style={{
                        backgroundColor:
                          organization.primary_color || '#ffffff',
                      }}
                    />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">Secondary Color</h3>
                    <div
                      className="mt-2 h-8 w-full rounded-md border"
                      style={{
                        backgroundColor:
                          organization.secondary_color || '#ffffff',
                      }}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Link
                href={`/${username}/organizations/${organization.id}/settings/branding`}
                className="w-full"
              >
                <Button variant="outline" className="w-full">
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit Branding
                </Button>
              </Link>
            </CardFooter>
          </Card>
        </div>
      </TabsContent>
    </div>
  );
}

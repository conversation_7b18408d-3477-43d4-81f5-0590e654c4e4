'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useOrganizations, useTeams } from '@/lib/hooks/use-supabase';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Loader2, Users } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

const createTeamSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  description: z.string().optional(),
});

type CreateTeamFormValues = z.infer<typeof createTeamSchema>;

export default function CreateTeamPage() {
  const router = useRouter();
  const { username, id } = useParams();
  const { getOrganization } = useOrganizations();
  const { createTeam } = useTeams();
  const [isCreating, setIsCreating] = useState(false);
  const [organizationName, setOrganizationName] = useState('');

  const form = useForm<CreateTeamFormValues>({
    resolver: zodResolver(createTeamSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Fetch organization name
  useEffect(() => {
    async function fetchOrganizationName() {
      try {
        const orgId = Array.isArray(id) ? id[0] : id;

        if (!orgId) {
          throw new Error('Invalid organization ID');
        }

        const org = await getOrganization(orgId);

        if (org) {
          setOrganizationName(org.name);
        }
      } catch (error) {
        console.error('Error fetching organization name:', error);
        toast.error('Failed to load organization');
      }
    }

    fetchOrganizationName();
  }, [id, getOrganization]);

  async function onSubmit(values: CreateTeamFormValues) {
    setIsCreating(true);

    try {
      const orgId = Array.isArray(id) ? id[0] : id;

      if (!orgId) {
        throw new Error('Invalid organization ID');
      }

      // Create a promise for the team creation process
      const createTeamPromise = createTeam({
        name: values.name,
        description: values.description || '',
        organization_id: orgId,
      });

      // Use toast.promise to handle loading, success, and error states
      toast.promise(createTeamPromise, {
        loading: 'Creating team...',
        success: 'Team created successfully',
        error: (err) =>
          `Failed to create team: ${err.message || 'Unknown error'}`,
      });

      // Wait for the team creation to complete
      const newTeam = await createTeamPromise;

      if (newTeam) {
        // Reset the form
        form.reset();

        // Navigate back to the organization page
        router.push(`/${username}/organizations/${orgId}`);
      }
    } catch (error) {
      console.error('Error creating team:', error);
      // Error is already handled by toast.promise
    } finally {
      setIsCreating(false);
    }
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link
          href={`/${username}/organizations/${Array.isArray(id) ? id[0] : id}`}
        >
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">
          Create Team in {organizationName}
        </h1>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Create a New Team
          </CardTitle>
          <CardDescription>
            Create a new team to organize members and collaborate on documents.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Team Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter team name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter team description"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>
                      Briefly describe the team's purpose
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    router.push(
                      `/${username}/organizations/${Array.isArray(id) ? id[0] : id}`
                    )
                  }
                  disabled={isCreating}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Team'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}

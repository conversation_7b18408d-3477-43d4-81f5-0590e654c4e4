'use client';

import { ConsultationDashboard } from '@/components/lawyer/ConsultationDashboard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { userStore } from '@/lib/store/user';
import { Briefcase, Calendar, FileText, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function ClientDashboardPage() {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Legal Services Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your legal consultations and document reviews
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={() => {
              router.push(`/${username}/lawyer/consultations`);
            }}
          >
            <Calendar className="h-4 w-4 mr-2" />
            Book Consultation
          </Button>

          <Button
            variant="outline"
            onClick={() => {
              router.push(`/${username}/lawyer/find`);
            }}
          >
            <Briefcase className="h-4 w-4 mr-2" />
            Find Lawyers
          </Button>
        </div>
      </div>

      <ConsultationDashboard />
    </div>
  );
}

'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { EnhancedConsultationBooking } from '@/components/lawyer/EnhancedConsultationBooking';
import { Button } from '@/components/ui/button';
import { userStore } from '@/lib/store/user';
import { ArrowLeft, Gavel } from 'lucide-react';
import { toast } from 'sonner';

export default function BookConsultationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { profile } = userStore();
  const username = profile?.username || '';

  // Get query parameters
  const lawyerId =
    searchParams.get('lawyer_id') || searchParams.get('lawyer') || undefined;
  const documentId = searchParams.get('document_id') || undefined;
  const clientId = searchParams.get('client_id') || undefined;
  const returnUrl =
    searchParams.get('return_url') || `/${username}/lawyer/consultations`;

  // Check if user is allowed to book consultations
  useEffect(() => {
    if (profile?.user_role === 'lawyer') {
      toast.error('Lawyers cannot book consultations with other lawyers');
      router.push(`/${username}/lawyer`);
    }
  }, [profile, router, username]);

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Gavel className="h-5 w-5 text-muted-foreground" />
            <h1 className="text-2xl font-bold">Book a Consultation</h1>
          </div>
          <p className="text-muted-foreground mt-1">
            Schedule a consultation with a legal professional
          </p>
        </div>
        <Button variant="outline" onClick={() => router.push(returnUrl)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      </div>

      <EnhancedConsultationBooking
        lawyerId={lawyerId}
        clientId={clientId}
        documentId={documentId}
      />
    </div>
  );
}

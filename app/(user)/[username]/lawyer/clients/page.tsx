'use client';

import { ClientManagement } from '@/components/lawyer/ClientManagement';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { ArrowLeft, Users } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

export default function ClientManagementPage() {
  const params = useParams();
  const username = params.username as string;
  const router = useRouter();
  const { profile } = userStore();
  const { isLawyer } = useLawyers();

  useEffect(() => {
    if (profile && profile.user_role !== 'lawyer') {
      toast.error('Access denied', {
        description: 'You need to be a lawyer to access this page',
      });
      router.push(`/${username}`);
      return;
    }
  }, [profile, username, router]);

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Client Management</h1>
          <p className="text-muted-foreground">
            Manage your clients and their consultations
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/${username}/lawyer/dashboard`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>

      {!isLawyer ? (
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-center">
              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-muted-foreground mb-4">
                You need to have a lawyer account to access this page.
              </p>
              <Button onClick={() => router.push(`/${username}`)}>
                Return to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <ClientManagement />
      )}
    </div>
  );
}

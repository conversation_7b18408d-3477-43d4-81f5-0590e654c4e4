'use client';

import { ReportSettingsForm } from '@/components/reports/ReportSettingsForm';
import { SavedReportsList } from '@/components/reports/SavedReportsList';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLawyerReports } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import {
  ArrowLeft,
  BarChart,
  Calendar,
  Clock,
  FileText,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export default function ReportsPage() {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;

  const {
    reportSettings,
    savedReports,
    loading,
    fetchSavedReports,
    updateReportSettings,
    deleteSavedReport,
  } = useLawyerReports();

  // Handle export report
  const handleExportReport = async (report: any) => {
    try {
      // Create a blob with the report data
      const blob = new Blob([JSON.stringify(report.report_data, null, 2)], {
        type: 'application/json',
      });

      // Create a download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${report.report_name.replace(/\s+/g, '-').toLowerCase()}.json`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Report exported successfully');
      return true;
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('Failed to export report');
      return false;
    }
  };

  // Report types
  const reportTypes = [
    {
      id: 'performance',
      name: 'Performance Report',
      description: 'Analyze your consultation performance and trends',
      icon: BarChart,
      color: 'text-blue-500',
      path: `/${username}/lawyer/reports/performance`,
    },
    {
      id: 'clients',
      name: 'Client Report',
      description: 'View detailed client engagement metrics',
      icon: Users,
      color: 'text-green-500',
      path: `/${username}/lawyer/reports/performance?tab=clients`,
    },
    {
      id: 'time_slots',
      name: 'Time Slot Analysis',
      description: 'Identify your most popular consultation times',
      icon: Clock,
      color: 'text-amber-500',
      path: `/${username}/lawyer/reports/performance?tab=time_slots`,
    },
    {
      id: 'monthly',
      name: 'Monthly Summary',
      description: 'Get a monthly breakdown of your consultations',
      icon: Calendar,
      color: 'text-purple-500',
      path: `/${username}/lawyer/reports/performance?tab=monthly`,
    },
  ];

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Reports & Analytics</h1>
          <p className="text-muted-foreground">
            Generate reports and analyze your consultation data
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/${username}/lawyer/dashboard`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>

      <Tabs defaultValue="reports">
        <TabsList className="mb-6">
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="saved">Saved Reports</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="reports">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {reportTypes.map((report) => (
              <Card
                key={report.id}
                className="hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => router.push(report.path)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>{report.name}</CardTitle>
                    <report.icon className={`h-5 w-5 ${report.color}`} />
                  </div>
                  <CardDescription>{report.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button>Generate Report</Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="saved">
          <SavedReportsList
            reports={savedReports}
            loading={loading}
            onDelete={deleteSavedReport}
            onExport={handleExportReport}
          />
        </TabsContent>

        <TabsContent value="settings">
          <ReportSettingsForm
            settings={reportSettings}
            loading={loading}
            onUpdateSettings={updateReportSettings}
          />
        </TabsContent>
      </Tabs>

      <div className="mt-8 bg-muted p-6 rounded-lg">
        <div className="flex items-start gap-4">
          <div className="bg-primary/10 p-3 rounded-full">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">
              About Reports & Analytics
            </h3>
            <p className="text-muted-foreground mb-4">
              Use reports and analytics to gain insights into your consultation
              performance and client engagement.
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>
                Generate performance reports to analyze your consultation
                metrics
              </li>
              <li>Identify your most popular consultation times and types</li>
              <li>Track client engagement and retention</li>
              <li>Save reports for future reference</li>
              <li>Set up automated report delivery to your email</li>
              <li>
                Export reports in various formats for sharing or archiving
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

import { LawyerProfileForm } from '@/components/lawyer/LawyerProfileForm';
import { LawyerProfilePreview } from '@/components/lawyer/LawyerProfilePreview';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { ArrowLeft, Edit, Eye, Info } from 'lucide-react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function LawyerProfilePage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const username = params.username as string;
  const router = useRouter();
  const { profile } = userStore();

  // Get the mode from URL or default to preview
  const defaultMode = searchParams.get('mode') === 'edit' ? 'edit' : 'preview';
  const [activeTab, setActiveTab] = useState<'preview' | 'edit'>(defaultMode);

  const { loading, lawyerProfile, fetchLawyerProfile } = useLawyers();

  useEffect(() => {
    if (profile && profile.user_role !== 'lawyer') {
      toast.error('Access denied', {
        description: 'You need to be a lawyer to access this page',
      });
      router.push(`/${username}`);
    } else if (profile) {
      // Only fetch if we have a profile
      fetchLawyerProfile().catch((error) => {
        console.error('Error fetching lawyer profile:', error);
        // Don't show error to user, just log it
      });
    }
  }, [profile, username, router, fetchLawyerProfile]);

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Lawyer Profile</h1>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center h-40">
              <div className="animate-pulse flex space-x-4">
                <div className="flex-1 space-y-6 py-1">
                  <div className="h-2 bg-slate-200 rounded"></div>
                  <div className="space-y-3">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="h-2 bg-slate-200 rounded col-span-2"></div>
                      <div className="h-2 bg-slate-200 rounded col-span-1"></div>
                    </div>
                    <div className="h-2 bg-slate-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Function to switch tabs and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value as 'preview' | 'edit');
    // Update URL without refreshing the page
    const url = new URL(window.location.href);
    url.searchParams.set('mode', value);
    router.replace(url.toString(), { scroll: false });
  };

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/${username}/lawyer/dashboard`)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">Lawyer Profile</h1>
          </div>
          <p className="text-muted-foreground">
            Manage your professional profile and settings
          </p>
        </div>
      </div>

      <Alert variant="default" className="mb-6 border-blue-200 bg-blue-50">
        <Info className="h-4 w-4 text-blue-500" />
        <AlertTitle>Profile Avatar</AlertTitle>
        <AlertDescription>
          Your lawyer profile uses the same avatar as your main profile. To
          change your avatar, go to your profile settings.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Lawyer Profile</CardTitle>
            <Tabs value={activeTab} onValueChange={handleTabChange}>
              <TabsList>
                <TabsTrigger value="preview">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="edit">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          {!lawyerProfile ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">
                Your lawyer profile could not be loaded. Please try again later.
              </p>
              <Button onClick={() => fetchLawyerProfile()}>Retry</Button>
            </div>
          ) : (
            <div>
              {activeTab === 'preview' ? (
                <LawyerProfilePreview
                  lawyerProfile={lawyerProfile}
                  onEditClick={() => handleTabChange('edit')}
                />
              ) : (
                <LawyerProfileForm
                  lawyerProfile={lawyerProfile}
                  onProfileUpdated={fetchLawyerProfile}
                  onPreviewClick={() => handleTabChange('preview')}
                />
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { CalendarBooking } from '@/components/lawyer/CalendarBooking';
import { DocumentReviewSubmission } from '@/components/lawyer/DocumentReviewSubmission';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useBookingsRealtime, useLawyerMessages } from '@/lib/hooks';
import {
  LawyerConsultation as BaseConsultation,
  LawyerDocumentReview as BaseDocumentReview,
} from '@/lib/types/database-modules';
import { format } from 'date-fns';
import {
  Calendar,
  Clock,
  FileCheck,
  FileText,
  Plus,
  Star,
  Video,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

// Extended interfaces with joined data
interface LawyerConsultation extends BaseConsultation {
  lawyer?: {
    id: string;
    full_name: string;
    email?: string;
    avatar_url?: string;
    specialization?: string[];
  };
  document?: {
    id: string;
    title: string;
  };
}

interface LawyerDocumentReview extends BaseDocumentReview {
  lawyer?: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
  document?: {
    id: string;
    title: string;
  };
}

export default function ConsultationsPage() {
  const params = useParams();
  const username = params.username as string;
  const router = useRouter();

  // Use our new realtime hook
  const { consultations, loading, cancelConsultation } = useBookingsRealtime();

  const {
    documentReviews,
    loading: reviewsLoading,
    fetchDocumentReviews,
  } = useLawyerMessages(username);

  const [activeTab, setActiveTab] = useState('upcoming');
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const [isReviewSubmissionOpen, setIsReviewSubmissionOpen] = useState(false);
  const [selectedLawyerId, setSelectedLawyerId] = useState<
    string | undefined
  >();

  // Filter consultations by status
  const scheduledConsultations = consultations.filter(
    (c) => c.status === 'scheduled' || c.status === 'confirmed'
  );

  const pastConsultations = consultations.filter(
    (c) => c.status === 'completed'
  );

  const cancelledConsultations = consultations.filter(
    (c) => c.status === 'cancelled'
  );

  // Handle consultation cancellation
  const handleCancelConsultation = async (consultationId: string) => {
    if (confirm('Are you sure you want to cancel this consultation?')) {
      try {
        const result = await cancelConsultation(consultationId);
        if (result) {
          toast.success('Consultation cancelled successfully');
        } else {
          toast.error('Failed to cancel consultation');
        }
      } catch (error) {
        console.error('Error cancelling consultation:', error);
        toast.error('Failed to cancel consultation');
      }
    }
  };

  // Format consultation date
  const formatConsultationDate = (dateString: string | null) => {
    if (!dateString) return 'No date specified';
    return format(new Date(dateString), 'MMMM d, yyyy');
  };

  // Format consultation time
  const formatConsultationTime = (dateString: string | null) => {
    if (!dateString) return 'No time specified';
    return format(new Date(dateString), 'h:mm a');
  };

  // Render consultation card
  const renderConsultationCard = (consultation: LawyerConsultation) => {
    return (
      <Card
        key={consultation.id}
        className="mb-4 hover:shadow-md transition-shadow"
      >
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {formatConsultationDate(consultation.consultation_date)}
                </span>
                <Clock className="h-4 w-4 text-muted-foreground ml-2" />
                <span className="text-sm">
                  {formatConsultationTime(consultation.consultation_date)}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Badge
                  variant={
                    consultation.status === 'scheduled' ||
                    consultation.status === 'confirmed'
                      ? 'default'
                      : consultation.status === 'completed'
                        ? 'secondary'
                        : 'destructive'
                  }
                  className={
                    consultation.status === 'completed'
                      ? 'bg-green-100 text-green-800 hover:bg-green-100'
                      : undefined
                  }
                >
                  {consultation.status}
                </Badge>
                <span className="text-sm">
                  {consultation.duration_minutes} minutes
                </span>
              </div>

              {consultation.lawyer && (
                <div className="flex items-center gap-2 mt-2">
                  <UserAvatar
                    fallbackText={consultation.lawyer?.full_name || 'Unknown'}
                    avatarUrl={consultation.lawyer?.avatar_url}
                    size="sm"
                  />
                  <div>
                    <p className="text-sm font-medium">
                      {consultation.lawyer.full_name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {consultation.lawyer.specialization?.[0] || 'Lawyer'}
                    </p>
                  </div>
                </div>
              )}

              {consultation.document_id && (
                <div className="flex items-center gap-2 mt-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Document attached</span>
                </div>
              )}
            </div>

            <div className="flex flex-col gap-2">
              {(consultation.status === 'scheduled' ||
                consultation.status === 'confirmed') && (
                <>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() =>
                      router.push(
                        `/${username}/lawyer/consultations/${consultation.id}`
                      )
                    }
                  >
                    <Video className="h-4 w-4 mr-2" />
                    Join Meeting
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCancelConsultation(consultation.id)}
                  >
                    Cancel
                  </Button>
                </>
              )}
              {consultation.status === 'completed' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    router.push(
                      `/${username}/lawyer/consultations/${consultation.id}`
                    )
                  }
                >
                  View Details
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render document review card
  const renderDocumentReviewCard = (review: LawyerDocumentReview) => {
    return (
      <Card key={review.id} className="mb-4 hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FileCheck className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Document Review</span>
                <span className="text-xs text-muted-foreground">
                  Submitted on{' '}
                  {review.created_at
                    ? format(new Date(review.created_at), 'MMM d, yyyy')
                    : 'Unknown date'}
                </span>
              </div>

              <h3 className="text-lg font-semibold">
                {review.document?.title || 'Unknown Document'}
              </h3>

              <div className="flex items-center gap-2">
                <UserAvatar
                  fallbackText={review.lawyer?.full_name || 'Unknown'}
                  avatarUrl={review.lawyer?.avatar_url}
                  size="sm"
                />
                <span className="text-sm">
                  Lawyer: {review.lawyer?.full_name || 'Unknown Lawyer'}
                </span>
              </div>

              {review.review_notes && (
                <p className="text-sm text-muted-foreground">
                  {review.review_notes.length > 100
                    ? `${review.review_notes.substring(0, 100)}...`
                    : review.review_notes}
                </p>
              )}

              {review.rating && (
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm">{review.rating}/5</span>
                </div>
              )}
            </div>

            <div className="flex flex-col gap-2 md:items-end">
              <Badge
                variant="outline"
                className={
                  review.status === 'pending'
                    ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
                    : review.status === 'in_progress'
                      ? 'bg-blue-100 text-blue-800 hover:bg-blue-100'
                      : review.status === 'completed'
                        ? 'bg-green-100 text-green-800 hover:bg-green-100'
                        : 'bg-red-100 text-red-800 hover:bg-red-100'
                }
              >
                {review.status.charAt(0).toUpperCase() +
                  review.status.slice(1).replace('_', ' ')}
              </Badge>

              <div className="flex gap-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Navigate to document with review ID as query param
                    router.push(
                      `/${username}/documents/${review.document_id}?review=${review.id}`
                    );
                  }}
                >
                  View Document
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">My Legal Consultations</h1>
          <p className="text-muted-foreground">
            Manage your consultations with lawyers
          </p>
        </div>

        <div className="flex gap-2">
          <Button onClick={() => router.push(`/${username}/lawyer/book`)}>
            <Calendar className="h-4 w-4 mr-2" />
            Book Consultation
          </Button>

          <Button
            variant="outline"
            onClick={() => {
              setSelectedLawyerId(undefined);
              setIsReviewSubmissionOpen(true);
            }}
          >
            <FileCheck className="h-4 w-4 mr-2" />
            Request Document Review
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="past">Past</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
          <TabsTrigger value="reviews">Document Reviews</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-[150px] w-full rounded-md" />
              <Skeleton className="h-[150px] w-full rounded-md" />
            </div>
          ) : scheduledConsultations.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Upcoming Consultations
                  </h3>
                  <p className="text-muted-foreground mb-4 max-w-md">
                    You don't have any scheduled consultations. Book a
                    consultation with a lawyer to get legal advice.
                  </p>
                  <Button
                    onClick={() => router.push(`/${username}/lawyer/book`)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Book a Consultation
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            scheduledConsultations.map(renderConsultationCard)
          )}
        </TabsContent>

        <TabsContent value="past">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-[150px] w-full rounded-md" />
              <Skeleton className="h-[150px] w-full rounded-md" />
            </div>
          ) : pastConsultations.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Past Consultations
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    You haven't completed any consultations yet.
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            pastConsultations.map(renderConsultationCard)
          )}
        </TabsContent>

        <TabsContent value="cancelled">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-[150px] w-full rounded-md" />
              <Skeleton className="h-[150px] w-full rounded-md" />
            </div>
          ) : cancelledConsultations.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Cancelled Consultations
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    You don't have any cancelled consultations.
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            cancelledConsultations.map(renderConsultationCard)
          )}
        </TabsContent>

        <TabsContent value="reviews">
          {reviewsLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-[150px] w-full rounded-md" />
              <Skeleton className="h-[150px] w-full rounded-md" />
            </div>
          ) : documentReviews.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Document Reviews
                  </h3>
                  <p className="text-muted-foreground mb-4 max-w-md">
                    You haven't submitted any documents for review yet. Submit a
                    document to get professional legal feedback.
                  </p>
                  <Button
                    onClick={() => {
                      setSelectedLawyerId(undefined);
                      setIsReviewSubmissionOpen(true);
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Submit Document for Review
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            documentReviews.map(renderDocumentReviewCard)
          )}
        </TabsContent>
      </Tabs>

      {/* Calendar Booking Dialog */}
      <CalendarBooking
        isOpen={isBookingOpen}
        onClose={() => setIsBookingOpen(false)}
        onSuccess={() => {
          setActiveTab('upcoming');
        }}
        defaultLawyerId={selectedLawyerId}
      />

      {/* Document Review Submission Dialog */}
      <DocumentReviewSubmission
        isOpen={isReviewSubmissionOpen}
        onClose={() => setIsReviewSubmissionOpen(false)}
        onSuccess={() => {
          fetchDocumentReviews(false);
          setActiveTab('reviews');
        }}
        defaultLawyerId={selectedLawyerId}
      />
    </div>
  );
}

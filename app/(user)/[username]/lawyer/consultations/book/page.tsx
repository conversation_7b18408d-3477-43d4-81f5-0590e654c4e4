'use client';

import { EnhancedConsultationBooking } from '@/components/lawyer/EnhancedConsultationBooking';
import { Button } from '@/components/ui/button';
import { userStore } from '@/lib/store/user';
import { ArrowLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function BookConsultationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { profile } = userStore();
  const username = profile?.username;

  const [lawyerId, setLawyerId] = useState<string | undefined>(undefined);
  const [clientId, setClientId] = useState<string | undefined>(undefined);
  const [documentId, setDocumentId] = useState<string | undefined>(undefined);

  useEffect(() => {
    // Get query parameters
    const lawyerParam = searchParams.get('lawyer');
    const clientParam = searchParams.get('client');
    const documentParam = searchParams.get('document');

    if (lawyerParam) setLawyerId(lawyerParam);
    if (clientParam) setClientId(clientParam);
    if (documentParam) setDocumentId(documentParam);
  }, [searchParams]);

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Book a Consultation</h1>
          <p className="text-muted-foreground">
            Schedule a consultation with a legal professional
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/${username}/lawyer/consultations`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Consultations
        </Button>
      </div>

      <EnhancedConsultationBooking
        lawyerId={lawyerId}
        clientId={clientId}
        documentId={documentId}
      />
    </div>
  );
}

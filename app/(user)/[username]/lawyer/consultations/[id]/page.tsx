'use client';

import { ConsultationCalendarSync } from '@/components/lawyer/ConsultationCalendarSync';
import { ConsultationMessaging } from '@/components/lawyer/ConsultationMessaging';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { format } from 'date-fns';
import {
  ArrowLeft,
  Calendar,
  Check,
  Clock,
  FileText,
  Loader2,
  MessageSquare,
  User,
  Video,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Define ConsultationStatus type based on the values used in the code
type ConsultationStatus =
  | 'scheduled'
  | 'completed'
  | 'cancelled'
  | 'rescheduled';
// Define UpdateConsultationStatus type for the updateConsultation function
type UpdateConsultationStatus = 'scheduled' | 'completed' | 'cancelled';

export default function ConsultationDetailPage() {
  const params = useParams();
  const username = params.username as string;
  const consultationId = params.id as string;
  const router = useRouter();
  const { profile } = userStore();

  const { updateConsultation } = useLawyers();

  // Define a type for consultation with joined data
  interface ConsultationWithJoins {
    id: string;
    lawyer_id: string;
    user_id: string;
    document_id: string | null;
    consultation_date: string | null;
    duration_minutes: number | null;
    status: string; // Will be cast to ConsultationStatus when needed
    notes: string | null;
    consultation_type: string | null;
    meeting_link: string | null;
    created_at: string;
    updated_at: string;
    feedback_provided?: boolean | null;

    // Joined data
    lawyer?: {
      id: string;
      full_name: string;
      email?: string;
      avatar_url?: string;
      specialization?: string[];
    };
    client?: {
      id: string;
      full_name: string;
      email?: string;
      avatar_url?: string;
    };
    document?: {
      id: string;
      title: string;
    };
  }

  const [consultation, setConsultation] =
    useState<ConsultationWithJoins | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('details');

  // Determine if the current user is a lawyer
  const isLawyer = profile?.user_role === 'lawyer';

  // Fetch the specific consultation by ID directly
  const { getConsultationById } = useLawyers();

  useEffect(() => {
    const fetchConsultationData = async () => {
      setLoading(true);
      try {
        // Directly fetch the consultation by ID
        const consultationData = await getConsultationById(consultationId);

        if (consultationData) {
          console.log(
            'Consultation data fetched successfully:',
            consultationData
          );

          // Cast the data to match our interface
          const typedConsultation: ConsultationWithJoins = {
            ...consultationData,
            // Use type assertion to handle the lawyer property
            lawyer: consultationData.lawyer
              ? (consultationData.lawyer as any)
              : undefined,
          };

          setConsultation(typedConsultation);

          // Verify user has access to this consultation
          const userHasAccess =
            (isLawyer && consultationData.lawyer_id === profile?.id) ||
            (!isLawyer && consultationData.user_id === profile?.id);

          if (!userHasAccess) {
            console.log('User does not have access to this consultation');
            toast.error('You do not have access to this consultation');
            router.push(`/${username}/lawyer/consultations`);
          }
        } else {
          console.log('Consultation not found with ID:', consultationId);
          toast.error('Consultation not found');
          router.push(`/${username}/lawyer/consultations`);
        }
      } catch (error) {
        console.error('Error fetching consultation:', error);
        toast.error('Error loading consultation');
        router.push(`/${username}/lawyer/consultations`);
      } finally {
        setLoading(false);
      }
    };

    if (consultationId && profile?.id) {
      fetchConsultationData();
    }
  }, [
    consultationId,
    profile?.id,
    getConsultationById,
    isLawyer,
    router,
    username,
  ]);

  // Handle status update
  const handleStatusUpdate = async (status: ConsultationStatus) => {
    if (!consultation) return;

    // Ensure we only pass valid status values to updateConsultation
    if (
      status !== 'scheduled' &&
      status !== 'completed' &&
      status !== 'cancelled'
    ) {
      toast.error(`Cannot update to status: ${status}`);
      return;
    }

    setIsUpdating(true);
    try {
      // Cast to UpdateConsultationStatus to satisfy TypeScript
      const validStatus = status as UpdateConsultationStatus;
      const success = await updateConsultation(consultation.id, validStatus);

      if (success) {
        toast.success(`Consultation marked as ${status}`);

        // Update local state with type safety
        setConsultation((prev) => {
          if (!prev) return null;
          return {
            ...prev,
            status: status,
          };
        });
      } else {
        toast.error('Failed to update consultation status');
      }
    } catch (error) {
      console.error('Error updating consultation status:', error);
      toast.error('Failed to update consultation status');
    } finally {
      setIsUpdating(false);
    }
  };

  // Format consultation date
  const formatConsultationDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMMM d, yyyy');
  };

  // Format consultation time
  const formatConsultationTime = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'h:mm a');
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status as ConsultationStatus) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'rescheduled':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Render loading state
  if (loading || !consultation) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push(`/${username}/lawyer/consultations`)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
          </div>

          <div className="md:col-span-2">
            <Card className="h-[500px]">
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[400px] w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/${username}/lawyer/consultations`)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">Consultation Details</h1>
          </div>
          <p className="text-muted-foreground">
            {isLawyer ? 'Client' : 'Lawyer'} consultation on{' '}
            {formatConsultationDate(consultation.consultation_date)}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <span
            className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
              consultation.status
            )}`}
          >
            {consultation.status.charAt(0).toUpperCase() +
              consultation.status.slice(1)}
          </span>

          <ConsultationCalendarSync
            consultationId={consultationId}
            isNewConsultation={false}
          />

          {consultation.status === 'scheduled' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStatusUpdate('completed')}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Check className="h-4 w-4 mr-2" />
                )}
                Mark Completed
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStatusUpdate('cancelled')}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <X className="h-4 w-4 mr-2" />
                )}
                Cancel
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Consultation Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">
                  {isLawyer ? 'Client' : 'Lawyer'}
                </h3>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {isLawyer
                      ? consultation.client?.full_name || 'Unknown Client'
                      : consultation.lawyer?.full_name || 'Unknown Lawyer'}
                  </span>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">
                  Date & Time
                </h3>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatConsultationDate(consultation.consultation_date)}
                  </span>
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatConsultationTime(consultation.consultation_date)} (
                    {consultation.duration_minutes} minutes)
                  </span>
                </div>
              </div>

              {consultation.document && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">
                    Document
                  </h3>
                  <Link
                    href={`/${username}/documents/${consultation.document.id}`}
                    className="flex items-center gap-2 text-primary hover:underline"
                  >
                    <FileText className="h-4 w-4" />
                    <span>{consultation.document.title}</span>
                  </Link>
                </div>
              )}

              {consultation.consultation_type && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">
                    Consultation Type
                  </h3>
                  <div className="flex items-center gap-2">
                    {consultation.consultation_type === 'video' ? (
                      <Video className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    )}
                    <span>
                      {consultation.consultation_type.charAt(0).toUpperCase() +
                        consultation.consultation_type.slice(1)}
                    </span>
                  </div>
                </div>
              )}

              {consultation.meeting_link && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">
                    Meeting Link
                  </h3>
                  <a
                    href={consultation.meeting_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline"
                  >
                    Join Meeting
                  </a>
                </div>
              )}

              {consultation.notes && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">
                    Notes
                  </h3>
                  <p className="text-sm">{consultation.notes}</p>
                </div>
              )}

              {consultation.status === 'scheduled' && (
                <Button
                  variant="default"
                  className="w-full mt-4"
                  onClick={() => {
                    // Open video call or chat based on consultation type
                    if (consultation.meeting_link) {
                      window.open(consultation.meeting_link, '_blank');
                    } else {
                      setActiveTab('messages');
                    }
                  }}
                >
                  {consultation.consultation_type === 'video' ? (
                    <>
                      <Video className="h-4 w-4 mr-2" />
                      Join Video Call
                    </>
                  ) : (
                    <>
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Open Chat
                    </>
                  )}
                </Button>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <TabsList className="mb-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="messages">Messages</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="flex-1">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>Consultation Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p>
                      This consultation is currently{' '}
                      <span className="font-medium">
                        {consultation.status.toLowerCase()}
                      </span>
                      .
                    </p>

                    {consultation.status === 'scheduled' && (
                      <div className="bg-blue-50 p-4 rounded-md">
                        <h3 className="font-medium text-blue-800 mb-2">
                          Upcoming Consultation
                        </h3>
                        <p className="text-blue-700 text-sm">
                          Your consultation is scheduled for{' '}
                          {formatConsultationDate(
                            consultation.consultation_date
                          )}{' '}
                          at{' '}
                          {formatConsultationTime(
                            consultation.consultation_date
                          )}
                          .
                        </p>
                        <p className="text-blue-700 text-sm mt-2">
                          {consultation.consultation_type === 'video'
                            ? "You can join the video call using the link on the left when it's time."
                            : 'You can use the messaging tab to communicate.'}
                        </p>
                      </div>
                    )}

                    {consultation.status === 'completed' && (
                      <div className="bg-green-50 p-4 rounded-md">
                        <h3 className="font-medium text-green-800 mb-2">
                          Completed Consultation
                        </h3>
                        <p className="text-green-700 text-sm">
                          This consultation was completed on{' '}
                          {formatConsultationDate(consultation.updated_at)}.
                        </p>
                        <p className="text-green-700 text-sm mt-2">
                          You can still view the messages and any shared
                          documents.
                        </p>
                      </div>
                    )}

                    {consultation.status === 'cancelled' && (
                      <div className="bg-red-50 p-4 rounded-md">
                        <h3 className="font-medium text-red-800 mb-2">
                          Cancelled Consultation
                        </h3>
                        <p className="text-red-700 text-sm">
                          This consultation was cancelled on{' '}
                          {formatConsultationDate(consultation.updated_at)}.
                        </p>
                      </div>
                    )}

                    <div className="border-t pt-4 mt-6">
                      <h3 className="font-medium mb-2">
                        About {isLawyer ? 'Client' : 'Lawyer'} Consultations
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Consultations allow you to discuss legal matters
                        directly with {isLawyer ? 'clients' : 'lawyers'}. You
                        can share documents, ask questions, and get professional
                        advice.
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Use the messaging tab to communicate before, during, and
                        after your scheduled consultation time.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="messages" className="flex-1">
              <ConsultationMessaging
                consultationId={consultationId}
                className="h-[600px]"
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

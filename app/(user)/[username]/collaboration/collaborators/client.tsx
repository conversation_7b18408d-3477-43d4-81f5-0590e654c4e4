'use client';

import { Bad<PERSON> } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { userStore } from '@/lib/store/user';
import { supabase } from '@/lib/supabase/client';
import { cn } from '@/lib/utils';
import {
  ChevronDown,
  FileText,
  Filter,
  Gavel,
  MoreHorizontal,
  Search,
  UserPlus,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Collaborator {
  id: string;
  userId: string;
  projectId: string;
  fullName: string;
  email: string;
  role: string;
  avatarUrl: string;
  createdAt: string;
  isLawyer?: boolean;
  specialization?: string[];
}

interface Project {
  id: string;
  name: string;
  description: string | null;
  owner_id: string;
  status: string;
  start_date: string;
  end_date: string | null;
  created_at: string;
  updated_at: string;
}

type ProjectMemberRole = 'admin' | 'member' | 'viewer';

export function CollaboratorsClient({ username }: { username: string }) {
  const { profile } = userStore();
  const [projects, setProjects] = useState<Project[]>([]);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [filteredCollaborators, setFilteredCollaborators] = useState<
    Collaborator[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [roleFilter, setRoleFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [projectDocuments, setProjectDocuments] = useState<any[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [lawyersLoading, setLawyersLoading] = useState(false);
  const MIN_LOADING_TIME = 500; // ms
  const [error, setError] = useState<string | null>(null);
  // We would use this in a real implementation with a proper invite modal
  // const [isInviteOpen, setIsInviteOpen] = useState(false);

  // Fetch projects on component mount
  useEffect(() => {
    const fetchProjects = async () => {
      if (!profile) {
        console.log('No user profile found');
        setIsLoading(false);
        setError('User profile not found');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log('Fetching projects for user:', profile.id);

        // Get projects where user is a member
        const { data: memberProjects, error: memberError } = await supabase
          .from('project_members')
          .select('project_id')
          .eq('user_id', profile.id);

        if (memberError) {
          console.error('Error fetching project memberships:', memberError);

          // Check for infinite recursion error
          if (
            memberError.message &&
            memberError.message.includes('infinite recursion')
          ) {
            setError(
              'Database policy error detected. Please contact an administrator to fix the issue. ' +
                'Error: infinite recursion in project_members policy.'
            );

            // Show a more helpful message to the developer in the console
            console.error(
              'DEVELOPER NOTE: This is caused by a circular reference in the RLS policies. ' +
                'Apply the fix by calling the /api/db/fix-policies endpoint.'
            );
          } else {
            setError('Failed to fetch project memberships');
          }

          setIsLoading(false);
          return;
        }

        if (!memberProjects || memberProjects.length === 0) {
          console.log('User is not a member of any projects');
          setProjects([]);
          setIsLoading(false);
          return;
        }

        // Get the project IDs
        const projectIds = memberProjects.map((p) => p.project_id);
        console.log('User is a member of projects:', projectIds);

        // Fetch the projects
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('*')
          .in('id', projectIds);

        if (projectsError) {
          console.error('Error fetching projects:', projectsError);
          setError('Failed to fetch projects');
          setIsLoading(false);
          return;
        }

        console.log('Fetched projects:', projectsData);

        // Convert the data to match our Project interface
        const formattedProjects = (projectsData || []).map((project: any) => ({
          id: project.id,
          name: project.name,
          description: project.description,
          owner_id: project.owner_id,
          status: project.status,
          start_date:
            project.start_date || project.due_date || new Date().toISOString(),
          end_date: project.end_date || project.due_date || null,
          created_at: project.created_at,
          updated_at: project.updated_at,
        }));

        setProjects(formattedProjects);

        // Set the first project as selected if none is selected
        if (projectsData && projectsData.length > 0 && !selectedProject) {
          setSelectedProject(projectsData[0].id);
        }
      } catch (error) {
        console.error('Error in fetchProjects:', error);
        setError('An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, [profile]);

  // Fetch collaborators when selected project changes
  useEffect(() => {
    if (selectedProject) {
      console.log(
        'Selected project changed, fetching collaborators for:',
        selectedProject
      );
      fetchCollaborators(selectedProject);
      fetchProjectDocuments(selectedProject);
    } else {
      setIsLoading(false);
      // Clear documents when no project is selected
      setProjectDocuments([]);
    }
  }, [selectedProject]);

  // Fetch project documents
  const fetchProjectDocuments = async (projectId: string) => {
    // Set a separate loading state for documents
    setDocumentsLoading(true);
    const startTime = Date.now();
    try {
      const { data, error } = await supabase
        .from('project_documents')
        .select(
          `
          id,
          project_id,
          document_id,
          documents:document_id (id, title, content, owner_id, created_at)
        `
        )
        .eq('project_id', projectId);

      if (error) throw error;

      console.log('Project documents:', data);
      setProjectDocuments(data || []);
    } catch (error) {
      console.error('Error fetching project documents:', error);
      toast.error('Failed to load project documents');
      setProjectDocuments([]);
    } finally {
      // Ensure we show loading state for at least MIN_LOADING_TIME
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, MIN_LOADING_TIME - elapsedTime);

      setTimeout(() => {
        setDocumentsLoading(false);
      }, remainingTime);
    }
  };

  useEffect(() => {
    // Filter collaborators based on search query, role filter, and active tab
    let filtered = [...collaborators];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (collaborator) =>
          collaborator.fullName.toLowerCase().includes(query) ||
          collaborator.email.toLowerCase().includes(query)
      );
    }

    // Apply role filter
    if (roleFilter) {
      filtered = filtered.filter(
        (collaborator) => collaborator.role === roleFilter
      );
    }

    // Apply tab filter
    if (activeTab === 'admin') {
      filtered = filtered.filter(
        (collaborator) => collaborator.role === 'admin'
      );
    } else if (activeTab === 'member') {
      filtered = filtered.filter(
        (collaborator) => collaborator.role === 'member'
      );
    } else if (activeTab === 'viewer') {
      filtered = filtered.filter(
        (collaborator) => collaborator.role === 'viewer'
      );
    }

    setFilteredCollaborators(filtered);
  }, [collaborators, searchQuery, roleFilter, activeTab]);

  const fetchCollaborators = async (projectId?: string) => {
    const targetProject = projectId || selectedProject;
    if (!targetProject) return;

    // Always set loading to true first to ensure consistent UI
    setIsLoading(true);
    setLawyersLoading(true);
    setError(null); // Clear any previous errors

    // Use a minimum loading time to prevent flickering
    const startTime = Date.now();

    try {
      console.log('Fetching collaborators for project:', targetProject);

      // Fetch project members with profile information
      const { data, error } = await supabase
        .from('project_members')
        .select(
          `
          id,
          user_id,
          project_id,
          role,
          created_at,
          profiles:user_id (id, full_name, email, avatar_url)
        `
        )
        .eq('project_id', targetProject);

      if (error) {
        console.error('Error fetching collaborators:', error);
        setError('Failed to fetch collaborators');
        setCollaborators([]);
        return;
      }

      console.log('Collaborators data:', data);

      if (!data || data.length === 0) {
        console.log('No collaborators found for this project');
        setCollaborators([]);
        return;
      }

      // Format collaborators data
      const formattedCollaborators = await Promise.all(
        data.map(async (item: any) => {
          // Make sure we have valid profile data
          const fullName = item.profiles?.full_name || 'Unknown User';
          const email = item.profiles?.email || '';
          const avatarUrl =
            item.profiles?.avatar_url ||
            `https://ui-avatars.com/api/?name=${encodeURIComponent(fullName)}&background=0D8ABC&color=fff`;

          // Check if this user is a lawyer
          let isLawyer = false;
          let specialization: string[] = [];

          try {
            const { data: lawyerData, error: lawyerError } = await supabase
              .from('lawyers')
              .select('id, specialization')
              .eq('user_id', item.user_id)
              .single();

            if (!lawyerError && lawyerData) {
              isLawyer = true;
              specialization = lawyerData.specialization || [];
            }
          } catch (err) {
            // Not a lawyer, continue
          }

          return {
            id: item.id,
            userId: item.user_id,
            projectId: item.project_id,
            fullName,
            email,
            role: item.role,
            avatarUrl,
            createdAt: item.created_at,
            isLawyer,
            specialization,
          };
        })
      );

      // We don't need to filter out lawyer collaborators anymore
      // as we're using the isLawyer property directly in the UI

      console.log('Formatted collaborators:', formattedCollaborators);
      setCollaborators(formattedCollaborators);
    } catch (error) {
      console.error('Error fetching collaborators:', error);
      setError('An unexpected error occurred');
      setCollaborators([]);
    } finally {
      // Ensure we show loading state for at least MIN_LOADING_TIME
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, MIN_LOADING_TIME - elapsedTime);

      setTimeout(() => {
        setIsLoading(false);
        setLawyersLoading(false);
      }, remainingTime);
    }
  };

  const handleAddCollaborator = async (
    email: string,
    role: ProjectMemberRole
  ) => {
    if (!selectedProject) return;

    try {
      // Check if user exists
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

      if (userError) {
        toast.error('User not found', {
          description: 'Please check the email address and try again',
        });
        return;
      }

      // Check if user is already a collaborator
      const { data: existingMember, error: memberError } = await supabase
        .from('project_members')
        .select('*')
        .eq('project_id', selectedProject)
        .eq('user_id', userData.id);

      if (memberError) throw memberError;

      if (existingMember && existingMember.length > 0) {
        toast.info('User is already a collaborator', {
          description: `${email} is already a collaborator on this project`,
        });
        return;
      }

      // Add user as collaborator
      const { error } = await supabase.from('project_members').insert({
        project_id: selectedProject,
        user_id: userData.id,
        role,
      } as any); // Use any to bypass type checking

      if (error) throw error;

      toast.success('Collaborator added', {
        description: `${email} has been added as a ${role}`,
      });

      // Refresh collaborators
      fetchCollaborators(selectedProject);
    } catch (error) {
      console.error('Error adding collaborator:', error);
      toast.error('Failed to add collaborator');
    }
  };

  const handleUpdateRole = async (
    collaboratorId: string,
    newRole: ProjectMemberRole
  ) => {
    try {
      const { error } = await supabase
        .from('project_members')
        .update({ role: newRole } as any) // Use any to bypass type checking
        .eq('id', collaboratorId);

      if (error) throw error;

      toast.success('Role updated', {
        description: `Collaborator role has been updated to ${newRole}`,
      });

      // Update local state
      setCollaborators(
        collaborators.map((collaborator) =>
          collaborator.id === collaboratorId
            ? { ...collaborator, role: newRole }
            : collaborator
        )
      );
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role');
    }
  };

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    try {
      const { error } = await supabase
        .from('project_members')
        .delete()
        .eq('id', collaboratorId);

      if (error) throw error;

      toast.success('Collaborator removed');

      // Update local state
      setCollaborators(
        collaborators.filter(
          (collaborator) => collaborator.id !== collaboratorId
        )
      );
    } catch (error) {
      console.error('Error removing collaborator:', error);
      toast.error('Failed to remove collaborator');
    }
  };

  // We'll handle loading in our renderContent function now
  // No need for a separate loading check here

  // We'll use a deterministic rendering approach with an IIFE
  const renderContent = () => {
    // 1. Error state takes precedence
    if (error) {
      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <div className="text-red-500 mb-4">Error</div>
            <p className="text-neutral-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </CardContent>
        </Card>
      );
    }

    // 2. Loading state - always show when loading is true
    if (isLoading) {
      return (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse flex items-center gap-4">
                  <div className="h-10 w-10 rounded-full bg-gray-200"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                    <div className="h-3 w-1/4 bg-gray-200 rounded"></div>
                  </div>
                  <div className="h-8 w-20 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      );
    }

    // 3. Empty state - no collaborators at all
    if (collaborators.length === 0) {
      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Users className="size-12 text-neutral-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">No Collaborators</h3>
            <p className="text-neutral-500 text-center max-w-md mb-6">
              You haven&apos;t added any collaborators to your projects yet.
            </p>
            <Button
              onClick={() => {
                // Simple implementation - in a real app, this would open a modal
                const email = prompt('Enter email address:');
                const role = prompt('Enter role (admin, member, viewer):');
                if (
                  email &&
                  role &&
                  ['admin', 'member', 'viewer'].includes(role)
                ) {
                  handleAddCollaborator(email, role as ProjectMemberRole);
                }
              }}
            >
              Invite Collaborator
            </Button>
          </CardContent>
        </Card>
      );
    }

    // 4. Filtered empty state - collaborators exist but none match filters
    if (filteredCollaborators.length === 0) {
      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Search className="size-12 text-neutral-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">
              No Matching Collaborators
            </h3>
            <p className="text-neutral-500 text-center max-w-md mb-6">
              No collaborators match your current filters.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setRoleFilter(null);
                setActiveTab('all');
              }}
            >
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      );
    }

    // 5. Content state - show collaborators
    return (
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Joined</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCollaborators.map((collaborator) => (
              <TableRow key={collaborator.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <UserAvatar
                      size="sm"
                      avatarUrl={collaborator.avatarUrl}
                      fallbackText={collaborator.fullName || collaborator.email}
                    />
                    <span>{collaborator.fullName || 'Unknown User'}</span>
                  </div>
                </TableCell>
                <TableCell>{collaborator.email}</TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={cn(
                      'capitalize',
                      collaborator.role === 'admin' &&
                        'border-blue-200 text-blue-700',
                      collaborator.role === 'member' &&
                        'border-green-200 text-green-700',
                      collaborator.role === 'viewer' &&
                        'border-orange-200 text-orange-700'
                    )}
                  >
                    {collaborator.role}
                  </Badge>
                </TableCell>
                <TableCell>
                  {new Date(collaborator.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => {
                          // Simple implementation - in a real app, this would open a modal
                          const role = prompt(
                            'Enter new role (admin, member, viewer):'
                          );
                          if (
                            role &&
                            ['admin', 'member', 'viewer'].includes(role)
                          ) {
                            handleUpdateRole(
                              collaborator.id,
                              role as ProjectMemberRole
                            );
                          }
                        }}
                      >
                        Change Role
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          handleRemoveCollaborator(collaborator.id)
                        }
                        className="text-red-600"
                      >
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    );
  };

  if (projects.length === 0) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Users className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Collaborators</h1>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Users className="size-10 text-neutral-300 mb-4" />
            <h2 className="text-xl font-semibold mb-2">No Projects Found</h2>
            <p className="text-neutral-500 text-center max-w-md mb-6">
              You need to create or join a project before you can manage
              collaborators.
            </p>
            <Button
              onClick={() =>
                (window.location.href = `/${username}/collaboration/projects`)
              }
            >
              Go to Projects
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <main className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Users className="size-5 text-neutral-500" />
        <h1 className="text-2xl font-bold">Collaborators</h1>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full md:w-auto">
                {selectedProject
                  ? projects.find((p) => p.id === selectedProject)?.name ||
                    'Select Project'
                  : 'Select Project'}
                <ChevronDown className="ml-2 size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Projects</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {projects.map((project) => (
                <DropdownMenuItem
                  key={project.id}
                  onClick={() => setSelectedProject(project.id)}
                >
                  {project.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <div className="relative w-full md:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search collaborators..."
              className="pl-8 w-full md:w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1">
                <Filter className="size-4" />
                <span>Filter</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Role</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setRoleFilter(null)}>
                All Roles
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setRoleFilter('admin')}>
                Admin
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setRoleFilter('member')}>
                Member
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setRoleFilter('viewer')}>
                Viewer
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            className="gap-1"
            onClick={() => {
              // Simple implementation - in a real app, this would open a modal
              const email = prompt('Enter email address:');
              const role = prompt('Enter role (admin, member, viewer):');
              if (
                email &&
                role &&
                ['admin', 'member', 'viewer'].includes(role)
              ) {
                handleAddCollaborator(email, role as ProjectMemberRole);
              }
            }}
          >
            <UserPlus className="size-4" />
            <span>Add Collaborator</span>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Project Collaborators</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4">
            <TabsList>
              <TabsTrigger value="all">
                All ({collaborators.length})
              </TabsTrigger>
              <TabsTrigger value="admin">
                Admins ({collaborators.filter((c) => c.role === 'admin').length}
                )
              </TabsTrigger>
              <TabsTrigger value="member">
                Members (
                {collaborators.filter((c) => c.role === 'member').length})
              </TabsTrigger>
              <TabsTrigger value="viewer">
                Viewers (
                {collaborators.filter((c) => c.role === 'viewer').length})
              </TabsTrigger>
              <TabsTrigger value="lawyers">
                Lawyers ({collaborators.filter((c) => c.isLawyer).length})
              </TabsTrigger>
              <TabsTrigger value="documents">
                Documents ({projectDocuments.length})
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <TabsContent value="all">
            {/* Use our deterministic rendering approach */}
            {renderContent()}
          </TabsContent>

          <TabsContent value="admin">{renderContent()}</TabsContent>

          <TabsContent value="member">{renderContent()}</TabsContent>

          <TabsContent value="viewer">{renderContent()}</TabsContent>

          <TabsContent value="lawyers">
            <Card>
              {lawyersLoading ? (
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div
                        key={i}
                        className="animate-pulse flex items-center gap-4"
                      >
                        <div className="h-10 w-10 rounded-full bg-gray-200"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                          <div className="h-3 w-1/4 bg-gray-200 rounded"></div>
                        </div>
                        <div className="h-8 w-20 bg-gray-200 rounded"></div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              ) : collaborators.filter((c) => c.isLawyer).length === 0 ? (
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <Gavel className="size-12 text-neutral-300 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Lawyers</h3>
                  <p className="text-neutral-500 text-center max-w-md mb-6">
                    There are no lawyers collaborating on this project yet.
                  </p>
                  <Button
                    onClick={() => {
                      // Simple implementation - in a real app, this would open a modal
                      const email = prompt('Enter lawyer email address:');
                      if (email) {
                        handleAddCollaborator(email, 'member');
                      }
                    }}
                  >
                    Add Lawyer
                  </Button>
                </CardContent>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Specialization</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {collaborators
                      .filter((c) => c.isLawyer)
                      .map((lawyer) => (
                        <TableRow key={lawyer.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <UserAvatar
                                size="sm"
                                avatarUrl={lawyer.avatarUrl}
                                fallbackText={lawyer.fullName || lawyer.email}
                              />
                              <span>{lawyer.fullName || 'Unknown User'}</span>
                            </div>
                          </TableCell>
                          <TableCell>{lawyer.email}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {lawyer.specialization?.map((spec, i) => (
                                <Badge
                                  key={i}
                                  variant="outline"
                                  className="capitalize"
                                >
                                  {spec}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={cn(
                                'capitalize',
                                lawyer.role === 'admin' &&
                                  'border-blue-200 text-blue-700',
                                lawyer.role === 'member' &&
                                  'border-green-200 text-green-700',
                                lawyer.role === 'viewer' &&
                                  'border-orange-200 text-orange-700'
                              )}
                            >
                              {lawyer.role}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="size-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => {
                                    // Simple implementation - in a real app, this would open a modal
                                    const role = prompt(
                                      'Enter new role (admin, member, viewer):'
                                    );
                                    if (
                                      role &&
                                      ['admin', 'member', 'viewer'].includes(
                                        role
                                      )
                                    ) {
                                      handleUpdateRole(
                                        lawyer.id,
                                        role as ProjectMemberRole
                                      );
                                    }
                                  }}
                                >
                                  Change Role
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleRemoveCollaborator(lawyer.id)
                                  }
                                  className="text-red-600"
                                >
                                  Remove
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              )}
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              {documentsLoading ? (
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div
                        key={i}
                        className="animate-pulse flex items-center gap-4"
                      >
                        <div className="h-10 w-10 rounded bg-gray-200"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                          <div className="h-3 w-1/4 bg-gray-200 rounded"></div>
                        </div>
                        <div className="h-8 w-20 bg-gray-200 rounded"></div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              ) : projectDocuments.length === 0 ? (
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <FileText className="size-12 text-neutral-300 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Documents</h3>
                  <p className="text-neutral-500 text-center max-w-md mb-6">
                    There are no documents attached to this project yet.
                  </p>
                  <Button
                    onClick={() => {
                      // In a real app, this would open a document selector
                      toast.info('Document selection would open here');
                    }}
                  >
                    Add Documents
                  </Button>
                </CardContent>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Added On</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {projectDocuments.map((doc) => (
                      <TableRow key={doc.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <FileText className="size-4 text-neutral-500" />
                            <span>
                              {doc.documents?.title || 'Untitled Document'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(
                            doc.documents?.created_at
                          ).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => {
                                  // In a real app, this would open the document
                                  window.open(
                                    `/documents/${doc.documents?.id}`,
                                    '_blank'
                                  );
                                }}
                              >
                                View Document
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  // Remove document from project
                                  if (
                                    confirm(
                                      'Remove this document from the project?'
                                    )
                                  ) {
                                    supabase
                                      .from('project_documents')
                                      .delete()
                                      .eq('id', doc.id)
                                      .then(({ error }) => {
                                        if (error) {
                                          toast.error(
                                            'Failed to remove document'
                                          );
                                        } else {
                                          toast.success(
                                            'Document removed from project'
                                          );
                                          fetchProjectDocuments(
                                            selectedProject!
                                          );
                                        }
                                      });
                                  }
                                }}
                                className="text-red-600"
                              >
                                Remove
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </Card>
          </TabsContent>
        </CardContent>
      </Card>
    </main>
  );
}

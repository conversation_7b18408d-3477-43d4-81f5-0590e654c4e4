'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  useCollaborationRealtime,
  useDocuments,
  useLawyers,
} from '@/lib/hooks';

import {
  Calendar,
  ClipboardCheck,
  FileText,
  FolderKanban,
  Gavel,
  Loader2,
  MessageSquare,
  Plus,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function CollaborationHubPage() {
  const params = useParams();
  const username = params.username as string;

  const { projects, loading } = useCollaborationRealtime();
  const { lawyers, loading: lawyersLoading, fetchAllLawyers } = useLawyers();
  const {
    documents,
    loading: documentsLoading,
    fetchDocuments,
  } = useDocuments();
  const router = useRouter();

  useEffect(() => {
    // Fetch lawyers
    fetchAllLawyers().catch((error) => {
      console.error('Error fetching lawyers:', error);
    });

    // Fetch documents
    fetchDocuments().catch((error) => {
      console.error('Error fetching documents:', error);
    });
  }, [fetchAllLawyers, fetchDocuments]);

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <h1 className="text-2xl font-bold">Collaboration Hub</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <FolderKanban className="size-5 text-neutral-500" />
              <span>Projects</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <p className="text-neutral-500 mb-4">
                Create and manage collaboration projects with your team.
              </p>
              <div className="flex gap-2">
                <Button
                  onClick={() =>
                    router.push(`/${username}/collaboration/projects`)
                  }
                  variant="outline"
                >
                  View All Projects
                </Button>
                <Button
                  onClick={() =>
                    router.push(`/${username}/collaboration/projects/new`)
                  }
                >
                  <Plus className="size-4 mr-2" />
                  New Project
                </Button>
              </div>
            </div>

            <div className="border-t pt-4">
              <h3 className="font-medium mb-2">Recent Projects</h3>
              {loading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="size-5 text-neutral-500 animate-spin" />
                </div>
              ) : projects.length === 0 ? (
                <p className="text-neutral-500 text-sm py-2">
                  No projects found
                </p>
              ) : (
                <div className="space-y-2">
                  {projects.slice(0, 3).map((project) => (
                    <Link
                      key={project.id}
                      href={`/${username}/collaboration/projects/${project.id}`}
                      className="flex items-center justify-between p-2 rounded-md hover:bg-neutral-100 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <FolderKanban className="size-4 text-neutral-500" />
                        <span className="font-medium">{project.title}</span>
                      </div>
                      <div className="text-xs text-neutral-500">
                        {new Date(
                          project.updated_at || project.created_at
                        ).toLocaleDateString()}
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <Users className="size-5 text-neutral-500" />
              <span>Collaborators</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-neutral-500 mb-4">
              Manage team members and external collaborators for your projects.
            </p>
            <Button
              onClick={() =>
                router.push(`/${username}/collaboration/collaborators`)
              }
              variant="outline"
            >
              View Collaborators
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Legal Collaboration</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <Gavel className="size-5 text-neutral-500" />
                <span>Lawyer Collaboration</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-500 mb-4">
                Collaborate with legal professionals on your documents and
                projects.
              </p>

              <div className="flex gap-2 mb-4">
                <Button
                  onClick={() => router.push(`/${username}/lawyer`)}
                  variant="outline"
                >
                  Find Lawyers
                </Button>
                <Button
                  onClick={() =>
                    router.push(`/${username}/lawyer/consultations`)
                  }
                >
                  View Consultations
                </Button>
              </div>

              <div className="flex gap-2 mb-4">
                <Button
                  onClick={() =>
                    router.push(`/${username}/lawyer/client-dashboard`)
                  }
                  variant="secondary"
                >
                  <Calendar className="size-4 mr-2" />
                  Client Dashboard
                </Button>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">Available Lawyers</h3>
                {lawyersLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="size-5 text-neutral-500 animate-spin" />
                  </div>
                ) : lawyers.length === 0 ? (
                  <p className="text-neutral-500 text-sm py-2">
                    No lawyers found
                  </p>
                ) : (
                  <div className="space-y-2">
                    {lawyers.slice(0, 3).map((lawyer) => (
                      <Link
                        key={lawyer.id}
                        href={`/${username}/lawyer/profile/${lawyer.id}`}
                        className="flex items-center justify-between p-2 rounded-md hover:bg-neutral-100 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <Gavel className="size-4 text-neutral-500" />
                          <span className="font-medium">
                            {lawyer.full_name}
                          </span>
                        </div>
                        <div className="text-xs text-neutral-500">
                          {lawyer.specialization?.join(', ') ||
                            'General Practice'}
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <FileText className="size-5 text-neutral-500" />
                <span>Document Collaboration</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-neutral-500 mb-4">
                Share and collaborate on legal documents with your team and
                lawyers.
              </p>

              <div className="flex gap-2 mb-4">
                <Button
                  onClick={() => router.push(`/${username}/documents`)}
                  variant="outline"
                >
                  My Documents
                </Button>
                <Button
                  onClick={() =>
                    router.push(`/${username}/collaboration/team-documents`)
                  }
                >
                  Team Documents
                </Button>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">Recent Documents</h3>
                {documentsLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="size-5 text-neutral-500 animate-spin" />
                  </div>
                ) : documents.length === 0 ? (
                  <p className="text-neutral-500 text-sm py-2">
                    No documents found
                  </p>
                ) : (
                  <div className="space-y-2">
                    {documents.slice(0, 3).map((doc) => (
                      <Link
                        key={doc.id}
                        href={`/${username}/documents/${doc.id}`}
                        className="flex items-center justify-between p-2 rounded-md hover:bg-neutral-100 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="size-4 text-neutral-500" />
                          <span className="font-medium">{doc.title}</span>
                        </div>
                        <div className="text-xs text-neutral-500">
                          {doc.document_type || 'Document'}
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <h2 className="text-xl font-semibold mb-4">Tools & Resources</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <MessageSquare className="size-8 text-neutral-500 mb-2" />
              <h3 className="font-medium mb-1">Team Chat</h3>
              <p className="text-neutral-500 text-sm mb-4">
                Communicate with your team in real-time
              </p>
              <Button
                onClick={() =>
                  router.push(`/${username}/collaboration/messages`)
                }
                variant="outline"
                className="w-full"
              >
                Open Chat
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <Calendar className="size-8 text-neutral-500 mb-2" />
              <h3 className="font-medium mb-1">Project Calendar</h3>
              <p className="text-neutral-500 text-sm mb-4">
                View and manage project deadlines
              </p>
              <Button
                onClick={() =>
                  router.push(`/${username}/collaboration/calendar`)
                }
                variant="outline"
                className="w-full"
              >
                View Calendar
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <ClipboardCheck className="size-8 text-neutral-500 mb-2" />
              <h3 className="font-medium mb-1">Task Tracking</h3>
              <p className="text-neutral-500 text-sm mb-4">
                Manage and track project tasks
              </p>
              <Button
                onClick={() => router.push(`/${username}/collaboration/tasks`)}
                variant="outline"
                className="w-full"
              >
                View Tasks
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

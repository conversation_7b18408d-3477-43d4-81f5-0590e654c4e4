import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { pageProps } from '@/types';
import {
  Grid,
  List,
  Network,
  Plus,
  Search,
  Share2,
  UserPlus,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import ClientTeams from './client-teams';

export default async function TeamsPage({ params }: pageProps) {
  const username = (await params).username;

  return (
    <main className="p-6">
      <section className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2">
              <Users className="size-5 text-neutral-500" />
              <h1 className="text-2xl font-bold">My Teams</h1>
            </div>
            <p className="text-neutral-500 mt-1">
              Manage your collaboration teams and workgroups
            </p>
          </div>
          <Link href={`/${username}/organizations`}>
            <Button className="gap-1">
              <Plus className="size-4" />
              <span>Create Team</span>
            </Button>
          </Link>
        </div>
      </section>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative w-full md:max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 size-4" />
          <Input className="pl-10" placeholder="Search teams..." />
        </div>

        <div className="flex items-center gap-2 ml-auto">
          <Tabs defaultValue="all">
            <TabsList>
              <TabsTrigger value="all">All Teams</TabsTrigger>
              <TabsTrigger value="owned">Owned by me</TabsTrigger>
              <TabsTrigger value="member">Member</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex">
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <Grid className="size-4" />
            </Button>
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <List className="size-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Use the ClientTeams component to handle all team data fetching and display */}
      <ClientTeams username={username} />

      <section className="mt-10">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">Invitations</h2>
          <Button variant="outline">
            <Share2 className="size-4 mr-1" />
            <span>Send Invites</span>
          </Button>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center py-8">
              <div className="size-14 rounded-full bg-neutral-100 mx-auto mb-3 flex items-center justify-center">
                <Network className="size-7 text-neutral-500" />
              </div>
              <h3 className="font-medium text-lg mb-1">
                No pending invitations
              </h3>
              <p className="text-neutral-500 max-w-md mx-auto mb-5">
                When you invite someone to join a team, their invitation will
                appear here
              </p>
              <Button>
                <UserPlus className="size-4 mr-1" />
                <span>Invite New Members</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>
    </main>
  );
}

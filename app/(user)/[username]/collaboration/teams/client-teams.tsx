'use client';

import { useEffect, useState } from 'react';
import { useTeams } from '@/lib/hooks';
import { TeamWithMembers } from '@/lib/types/database-modules';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { UserAvatar } from '@/components/ui/user-avatar';
import {
  Bell,
  Calendar,
  FileText,
  LayoutGrid,
  MoreHorizontal,
  Pen,
  Plus,
  Share2,
  Shield,
  Trash2,
  UserCog,
  UserPlus,
  Users,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Link from 'next/link';

// Extended TeamWithMembers interface with additional properties
interface ExtendedTeamWithMembers extends TeamWithMembers {
  totalDocuments: number;
  activeProjects: number;
  type: string;
  lastActive?: string;
  created?: string;
}

// Helper function to format dates
function formatDate(dateString: string | undefined): string {
  if (!dateString) return 'Unknown';
  
  const date = new Date(dateString);
  
  // Check if date is valid
  if (isNaN(date.getTime())) return 'Unknown';
  
  // If it's today
  const today = new Date();
  if (date.toDateString() === today.toDateString()) {
    return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  }
  
  // If it's yesterday
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  if (date.toDateString() === yesterday.toDateString()) {
    return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  }
  
  // If it's within the last 7 days
  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);
  if (date > lastWeek) {
    return date.toLocaleDateString([], { weekday: 'long' }) + 
      ` at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  }
  
  // Otherwise, show the full date
  return date.toLocaleDateString([], { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
}

export default function ClientTeams({ username }: { username: string }) {
  const [teams, setTeams] = useState<ExtendedTeamWithMembers[]>([]);
  const [loading, setLoading] = useState(true);
  const { getUserTeams, loading: teamsLoading } = useTeams();

  useEffect(() => {
    async function fetchTeams() {
      try {
        const teamsData = await getUserTeams();
        
        // Convert TeamWithMembers to ExtendedTeamWithMembers
        const extendedTeams = teamsData.map(team => ({
          ...team,
          totalDocuments: 0, // Placeholder
          activeProjects: 0, // Placeholder
          type: typeof team.permissions === 'object' && team.permissions
            ? (team.permissions as any).type || 'Team'
            : 'Team',
          lastActive: team.updated_at,
        }));
        
        setTeams(extendedTeams as ExtendedTeamWithMembers[]);
      } catch (error) {
        console.error('Error fetching teams:', error);
      } finally {
        setLoading(false);
      }
    }
    
    fetchTeams();
  }, [getUserTeams]);

  const getTeamTypeColor = (type: string) => {
    switch (type) {
      case 'Department':
        return 'bg-blue-100 text-blue-800';
      case 'Project':
        return 'bg-green-100 text-green-800';
      case 'Committee':
        return 'bg-purple-100 text-purple-800';
      case 'Working Group':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };

  if (loading || teamsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-neutral-500">Loading teams...</p>
        </div>
      </div>
    );
  }

  if (teams.length === 0) {
    return (
      <Card className="col-span-full p-8 text-center">
        <div className="flex flex-col items-center justify-center gap-4">
          <div className="rounded-full bg-neutral-100 p-4">
            <Users className="size-8 text-neutral-500" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-1">No teams found</h3>
            <p className="text-neutral-500 mb-4">
              You are not a member of any teams yet. Create a team to get started.
            </p>
            <Link href={`/${username}/organizations`}>
              <Button>
                <Plus className="size-4 mr-1" />
                <span>Create Team</span>
              </Button>
            </Link>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {teams.map((team) => (
        <Card key={team.id} className="overflow-hidden">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-start">
              <div>
                <Badge
                  className={getTeamTypeColor(team.type)}
                  variant="secondary"
                >
                  {team.type}
                </Badge>
                <CardTitle className="mt-2 text-lg">{team.name}</CardTitle>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreHorizontal className="size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem className="gap-2">
                    <Pen className="size-4" />
                    <span>Edit Team</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <UserPlus className="size-4" />
                    <span>Add Members</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Share2 className="size-4" />
                    <span>Share</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="gap-2">
                    <Bell className="size-4" />
                    <span>Notification Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="gap-2 text-red-600">
                    <Trash2 className="size-4" />
                    <span>Delete Team</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent className="pb-3">
            <p className="text-sm text-neutral-500 mb-4">
              {team.description}
            </p>

            <div className="flex -space-x-2 mb-4">
              {team.members.slice(0, 5).map((member) => (
                <div key={member.id} className="relative tooltip-wrapper">
                  <UserAvatar
                    size="sm"
                    avatarUrl={member.avatar_url}
                    fallbackText={member.full_name}
                    className="border-2 border-white"
                  />
                  <span className="tooltip">{member.full_name}</span>
                </div>
              ))}
              {team.members.length > 5 && (
                <div className="size-8 rounded-full bg-neutral-100 flex items-center justify-center text-xs font-medium border-2 border-white">
                  +{team.members.length - 5}
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-1.5">
                <FileText className="size-4 text-neutral-500" />
                <span>{team.totalDocuments} documents</span>
              </div>
              <div className="flex items-center gap-1.5">
                <LayoutGrid className="size-4 text-neutral-500" />
                <span>{team.activeProjects} active projects</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-3 text-sm text-neutral-500 flex justify-between border-t">
            <div className="flex items-center gap-1">
              <Calendar className="size-4" />
              <span>
                Created {new Date(team.created_at).toLocaleDateString()}
              </span>
            </div>
            <div>
              Last active{' '}
              {team.lastActive ? formatDate(team.lastActive) : 'Unknown'}
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

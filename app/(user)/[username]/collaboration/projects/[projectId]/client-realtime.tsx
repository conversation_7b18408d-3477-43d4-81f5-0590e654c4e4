'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useCollaborationRealtime } from '@/lib/hooks';
import {
  ArrowLeft,
  Calendar,
  CheckCircle2,
  Circle,
  Clock,
  FileText,
  MessageSquare,
  PencilLine,
  Plus,
  Share2,
  Trash2,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

export function ProjectDetailClientRealtime({
  username,
  projectId,
}: {
  username: string;
  projectId: string;
}) {
  const router = useRouter();
  const {
    currentProject,
    members,
    tasks,
    comments,
    documents,
    loading,
    error,
    addTask,
    // Remove unused variables to fix warnings
  } = useCollaborationRealtime(projectId);

  const [activeTab, setActiveTab] = useState('overview');
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState('');

  // Calculate project statistics
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(
    (task) => task.status === 'completed'
  ).length;
  const progress =
    totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  // Handle project deletion
  const handleDeleteProject = async () => {
    if (
      confirm(
        'Are you sure you want to delete this project? This action cannot be undone.'
      )
    ) {
      try {
        // Implement project deletion logic
        toast.success('Project deleted successfully');
        router.push(`/${username}/collaboration/projects`);
      } catch (error) {
        console.error('Error deleting project:', error);
        toast.error('Failed to delete project');
      }
    }
  };

  // Handle adding a new task
  const handleAddTask = async () => {
    if (!newTaskTitle.trim()) {
      toast.error('Task title is required');
      return;
    }

    setIsAddingTask(true);
    try {
      await addTask(projectId, newTaskTitle);
      setNewTaskTitle('');
      toast.success('Task added successfully');
    } catch (error) {
      console.error('Error adding task:', error);
      toast.error('Failed to add task');
    } finally {
      setIsAddingTask(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link
            href={`/${username}/collaboration/projects`}
            className="flex items-center gap-1 text-neutral-600 hover:text-neutral-900"
          >
            <ArrowLeft className="size-4" />
            <span>Back to Projects</span>
          </Link>
        </div>
        <div className="space-y-4">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
          <div className="flex gap-2 justify-end">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
          <Skeleton className="h-[400px] w-full rounded-md" />
        </div>
      </div>
    );
  }

  if (error || !currentProject) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link
            href={`/${username}/collaboration/projects`}
            className="flex items-center gap-1 text-neutral-600 hover:text-neutral-900"
          >
            <ArrowLeft className="size-4" />
            <span>Back to Projects</span>
          </Link>
        </div>
        <Card>
          <CardContent className="p-8 flex flex-col items-center justify-center">
            <div className="text-red-500 mb-4 text-xl">Error</div>
            <p className="text-neutral-600 mb-4">
              {error?.message || 'Project not found'}
            </p>
            <Button asChild>
              <Link href={`/${username}/collaboration/projects`}>
                Return to Projects
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link
          href={`/${username}/collaboration/projects`}
          className="flex items-center gap-1 text-neutral-600 hover:text-neutral-900"
        >
          <ArrowLeft className="size-4" />
          <span>Back to Projects</span>
        </Link>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold">{currentProject.title}</h1>
          <p className="text-neutral-600">{currentProject.description}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="gap-1">
            <Share2 className="size-4" />
            <span>Share</span>
          </Button>
          <Button variant="outline" className="gap-1">
            <PencilLine className="size-4" />
            <span>Edit</span>
          </Button>
          <Button
            variant="shadow_red"
            className="gap-1"
            onClick={handleDeleteProject}
          >
            <Trash2 className="size-4" />
            <span>Delete</span>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
        </TabsList>

        <div className="space-y-6">
          <TabsContent value="overview">
            <Card>
              <CardHeader>
                <CardTitle>Project Overview</CardTitle>
                <CardDescription>
                  Key information about the project
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-2">Progress</h3>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">
                      {completedTasks} of {totalTasks} tasks completed
                    </span>
                    <span className="text-sm font-medium">{progress}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-2">
                      Project Details
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Status</span>
                        <span className="font-medium">
                          {currentProject.status || 'Active'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Created</span>
                        <span className="font-medium">
                          {new Date(
                            currentProject.created_at
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-neutral-600">Last Updated</span>
                        <span className="font-medium">
                          {new Date(
                            currentProject.updated_at ||
                              currentProject.created_at
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Team</h3>
                    <div className="flex flex-wrap gap-2">
                      {members.slice(0, 5).map((member) => (
                        <UserAvatar
                          key={member.id}
                          fallbackText="Member"
                          className="h-8 w-8"
                        />
                      ))}
                      {members.length > 5 && (
                        <div className="h-8 w-8 rounded-full bg-neutral-100 flex items-center justify-center text-xs font-medium">
                          +{members.length - 5}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">Recent Activity</h3>
                  <div className="space-y-3">
                    {tasks.slice(0, 3).map((task) => (
                      <div
                        key={task.id}
                        className="flex items-start gap-2 p-2 rounded-md hover:bg-neutral-50"
                      >
                        {task.status === 'completed' ? (
                          <CheckCircle2 className="size-4 text-green-500 mt-0.5" />
                        ) : (
                          <Circle className="size-4 text-neutral-300 mt-0.5" />
                        )}
                        <div>
                          <div className="font-medium">{task.title}</div>
                          <div className="text-xs text-neutral-500">
                            {task.due_date
                              ? `Due ${new Date(task.due_date).toLocaleDateString()}`
                              : 'No due date'}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tasks">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Tasks</CardTitle>
                  <CardDescription>
                    Manage and track project tasks
                  </CardDescription>
                </div>
                <Button className="gap-1">
                  <Plus className="size-4" />
                  <span>Add Task</span>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {tasks.length === 0 ? (
                    <div className="text-center py-8">
                      <div className="text-neutral-400 mb-2">No tasks yet</div>
                      <p className="text-sm text-neutral-500 mb-4">
                        Add your first task to get started
                      </p>
                      <div className="flex items-center gap-2 max-w-md mx-auto">
                        <input
                          type="text"
                          placeholder="Enter task title"
                          className="flex-1 px-3 py-2 border rounded-md"
                          value={newTaskTitle}
                          onChange={(e) => setNewTaskTitle(e.target.value)}
                        />
                        <Button
                          onClick={handleAddTask}
                          disabled={isAddingTask || !newTaskTitle.trim()}
                        >
                          {isAddingTask ? 'Adding...' : 'Add'}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="divide-y">
                      {tasks.map((task) => (
                        <div
                          key={task.id}
                          className="py-3 flex items-start justify-between gap-4"
                        >
                          <div className="flex items-start gap-3">
                            <div
                              className={`size-5 rounded-full flex items-center justify-center mt-0.5 ${
                                task.status === 'completed'
                                  ? 'bg-green-100 text-green-600'
                                  : task.status === 'in_progress'
                                    ? 'bg-blue-100 text-blue-600'
                                    : 'bg-neutral-100 text-neutral-600'
                              }`}
                            >
                              {task.status === 'completed' ? (
                                <CheckCircle2 className="size-3.5" />
                              ) : (
                                <Circle className="size-3.5" />
                              )}
                            </div>
                            <div>
                              <div className="font-medium">{task.title}</div>
                              {task.description && (
                                <p className="text-sm text-neutral-600 mt-1">
                                  {task.description}
                                </p>
                              )}
                              <div className="flex items-center gap-4 mt-2">
                                {task.due_date && (
                                  <div className="flex items-center gap-1 text-xs text-neutral-500">
                                    <Calendar className="size-3" />
                                    <span>
                                      {new Date(
                                        task.due_date
                                      ).toLocaleDateString()}
                                    </span>
                                  </div>
                                )}
                                <div className="flex items-center gap-1 text-xs text-neutral-500">
                                  <Clock className="size-3" />
                                  <span>
                                    {new Date(
                                      task.created_at
                                    ).toLocaleDateString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {task.assigned_to && (
                              <UserAvatar
                                fallbackText="Assignee"
                                className="h-6 w-6"
                              />
                            )}
                            <div
                              className={`text-xs px-2 py-1 rounded-full ${
                                task.priority === 'high'
                                  ? 'bg-red-100 text-red-700'
                                  : task.priority === 'medium'
                                    ? 'bg-yellow-100 text-yellow-700'
                                    : 'bg-green-100 text-green-700'
                              }`}
                            >
                              {task.priority}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Team Members</CardTitle>
                  <CardDescription>
                    People working on this project
                  </CardDescription>
                </div>
                <Button className="gap-1">
                  <Plus className="size-4" />
                  <span>Add Member</span>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {members.length === 0 ? (
                    <div className="text-center py-8">
                      <Users className="size-12 text-neutral-300 mx-auto mb-2" />
                      <div className="text-neutral-400 mb-2">
                        No team members
                      </div>
                      <p className="text-sm text-neutral-500 mb-4">
                        Add team members to collaborate on this project
                      </p>
                    </div>
                  ) : (
                    <div className="divide-y">
                      {members.map((member) => (
                        <div
                          key={member.id}
                          className="py-3 flex items-center justify-between"
                        >
                          <div className="flex items-center gap-3">
                            <UserAvatar
                              fallbackText="Member"
                              className="h-8 w-8"
                            />
                            <div>
                              <div className="font-medium">Unknown User</div>
                              <div className="text-sm text-neutral-500">
                                No email
                              </div>
                            </div>
                          </div>
                          <div
                            className={`text-xs px-2 py-1 rounded-full ${
                              member.role === 'owner'
                                ? 'bg-purple-100 text-purple-700'
                                : member.role === 'admin'
                                  ? 'bg-blue-100 text-blue-700'
                                  : 'bg-neutral-100 text-neutral-700'
                            }`}
                          >
                            {member.role}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Documents</CardTitle>
                  <CardDescription>
                    Files and documents related to this project
                  </CardDescription>
                </div>
                <Button className="gap-1">
                  <Plus className="size-4" />
                  <span>Add Document</span>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {documents.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="size-12 text-neutral-300 mx-auto mb-2" />
                      <div className="text-neutral-400 mb-2">No documents</div>
                      <p className="text-sm text-neutral-500 mb-4">
                        Add documents to share with the team
                      </p>
                    </div>
                  ) : (
                    <div className="divide-y">
                      {documents.map((doc) => (
                        <div
                          key={doc.id}
                          className="py-3 flex items-center justify-between"
                        >
                          <div className="flex items-center gap-3">
                            <div className="size-8 rounded bg-neutral-100 flex items-center justify-center">
                              <FileText className="size-4 text-neutral-500" />
                            </div>
                            <div>
                              <div className="font-medium">
                                {doc.title || 'Untitled Document'}
                              </div>
                              <div className="text-xs text-neutral-500">
                                Added{' '}
                                {new Date(doc.created_at).toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="comments">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Comments</CardTitle>
                  <CardDescription>
                    Team discussions about this project
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {comments.length === 0 ? (
                    <div className="text-center py-8">
                      <MessageSquare className="size-12 text-neutral-300 mx-auto mb-2" />
                      <div className="text-neutral-400 mb-2">No comments</div>
                      <p className="text-sm text-neutral-500 mb-4">
                        Start a discussion with your team
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {comments.map((comment) => (
                        <div
                          key={comment.id}
                          className="flex gap-3 p-3 rounded-lg bg-neutral-50"
                        >
                          <UserAvatar fallbackText="User" className="h-8 w-8" />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div className="font-medium">Unknown User</div>
                              <div className="text-xs text-neutral-500">
                                {new Date(comment.created_at).toLocaleString()}
                              </div>
                            </div>
                            <p className="mt-1">{comment.content}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="mt-4 pt-4 border-t">
                    <div className="flex gap-3">
                      <UserAvatar fallbackText="You" className="h-8 w-8" />
                      <div className="flex-1">
                        <textarea
                          className="w-full p-2 border rounded-md min-h-[100px]"
                          placeholder="Add a comment..."
                        ></textarea>
                        <div className="flex justify-end mt-2">
                          <Button>Post Comment</Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}

'use client';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useCollaborationRealtime } from '@/lib/hooks';
import { FolderKanban, Plus, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export function ProjectsClient({ username }: { username: string }) {
  const { projects, loading, error } = useCollaborationRealtime();
  const router = useRouter();

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <FolderKanban className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Projects</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="p-0">
                <Skeleton className="h-40 rounded-none" />
              </CardHeader>
              <CardContent className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-4" />
                <div className="flex justify-between mb-2">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-1/4" />
                </div>
                <Skeleton className="h-2 w-full mb-4" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <main className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <FolderKanban className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Projects</h1>
        </div>
        <Button
          onClick={() => router.push(`/${username}/collaboration/projects/new`)}
          className="gap-1"
        >
          <Plus className="size-4" />
          <span>New Project</span>
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error.message}
        </div>
      )}

      {projects.length === 0 ? (
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle>No Projects Yet</CardTitle>
            <CardDescription>
              Create your first collaboration project to get started
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <FolderKanban className="size-12 text-neutral-300 mb-4" />
            <p className="text-neutral-500 text-center mb-6 max-w-md">
              Projects help you organize your work and collaborate with others.
              Create a project to get started.
            </p>
            <Button
              onClick={() =>
                router.push(`/${username}/collaboration/projects/new`)
              }
            >
              Create a Project
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {projects.map((project) => (
            <Link
              href={`/${username}/collaboration/projects/${project.id}`}
              key={project.id}
            >
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">{project.title}</CardTitle>
                  <p className="text-sm text-neutral-500 line-clamp-2">
                    {project.description || 'No description provided'}
                  </p>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progress</span>
                    <span>{calculateProgress(project)}%</span>
                  </div>
                  <Progress
                    value={calculateProgress(project)}
                    className="h-1.5 mb-4"
                  />

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                      <Users className="size-3.5 text-neutral-500" />
                      <span className="text-xs text-neutral-500">1 member</span>
                    </div>
                    <div className="text-xs text-neutral-500">
                      {formatDate(project.updated_at || project.created_at)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </main>
  );
}

// Helper function to calculate project progress
function calculateProgress(project: any): number {
  // If the project has a progress property, use it
  if (project.progress !== undefined) {
    return project.progress;
  }

  // Otherwise, return a default value
  return 0;
}

// Helper function to format dates
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)} weeks ago`;
  } else {
    return date.toLocaleDateString();
  }
}

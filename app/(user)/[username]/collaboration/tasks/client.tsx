'use client';

import { Member, Task } from '@/components/collaboration/TaskList';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useCollaboration } from '@/lib/hooks';
import {
  ChevronDown,
  ClipboardCheck,
  Clock,
  Filter,
  MoreHorizontal,
  Plus,
  User,
} from 'lucide-react';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export function TasksClient({ username }: { username: string }) {
  const {
    projects,
    loading: projectsLoading,
    fetchTasks: fetchProjectTasks,
    updateTask,
    deleteTask,
    fetchMembers,
  } = useCollaboration();

  // Function to create a new task
  const createNewTask = async (
    projectId: string,
    title: string,
    description: string,
    status: string,
    priority: string,
    assignedTo?: string,
    dueDate?: string
  ) => {
    try {
      if (updateTask) {
        // Create a new task with default values
        const newTask = {
          id: '', // Will be generated by the database
          title,
          description,
          status,
          priority,
          assignedTo,
          dueDate,
        };

        // Use the updateTask function to create the task
        return await updateTask(projectId, newTask);
      }
      return false;
    } catch (error) {
      console.error('Error creating task:', error);
      return false;
    }
  };

  const [tasks, setTasks] = useState<Task[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [priorityFilter, setPriorityFilter] = useState<string | null>(null);
  // We'll use this state for future implementation of assignee filtering
  const [assignedToFilter, _setAssignedToFilter] = useState<string | null>(
    null
  );

  useEffect(() => {
    if (projects && projects.length > 0 && !selectedProject) {
      setSelectedProject(projects[0].id);
    }
  }, [projects, selectedProject]);

  useEffect(() => {
    if (selectedProject) {
      fetchTasks(selectedProject);
    } else {
      setIsLoading(false);
    }
  }, [selectedProject]);

  const fetchTasks = async (projectId: string) => {
    setIsLoading(true);
    try {
      console.log('Fetching tasks for project:', projectId);
      const tasksData = await fetchProjectTasks(projectId);
      console.log('Tasks data:', tasksData);

      // Map the database tasks to our Task interface
      const formattedTasks = tasksData.map((task) => ({
        id: task.id,
        title: task.title,
        description: task.description || undefined,
        status: (task.status === 'completed' ? 'done' : task.status) as
          | 'todo'
          | 'in_progress'
          | 'review'
          | 'done',
        priority: task.priority as 'low' | 'medium' | 'high' | 'urgent',
        assignedTo: task.assigned_to || undefined,
        dueDate: task.due_date || undefined,
        createdBy: task.created_by,
        createdAt: task.created_at,
      }));
      console.log('Formatted tasks:', formattedTasks);
      setTasks(formattedTasks);

      // Fetch project members using the hook
      console.log('Fetching members for project:', projectId);
      try {
        const membersData = await fetchMembers(projectId);
        console.log('Members data:', membersData);

        // Format members data
        const formattedMembers = membersData.map((member) => ({
          id: member.id,
          userId: member.user_id,
          fullName: member.user?.full_name || 'Unknown User',
          email: member.user?.email || '',
          role: (member.role === 'editor'
            ? 'member'
            : member.role === 'owner'
              ? 'owner'
              : member.role === 'admin'
                ? 'admin'
                : 'viewer') as 'owner' | 'admin' | 'member' | 'viewer',
          createdAt: member.created_at,
          avatarUrl: member.user?.avatar_url || null,
        }));

        console.log('Formatted members:', formattedMembers);
        setMembers(formattedMembers);
      } catch (memberError) {
        console.error('Error processing members:', memberError);
        // Set default empty members array
        setMembers([]);
      }
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast.error('Failed to load tasks');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTask = async (taskData: any) => {
    if (!selectedProject) return;

    try {
      await createNewTask(
        selectedProject,
        taskData.title,
        taskData.description,
        taskData.status,
        taskData.priority,
        taskData.assignedTo,
        taskData.dueDate
      );

      // Refresh tasks
      fetchTasks(selectedProject);
      toast.success('Task created successfully');
    } catch (error) {
      console.error('Error creating task:', error);
      toast.error('Failed to create task');
    }
  };

  const handleUpdateTaskStatus = async (
    taskId: string,
    newStatus: 'todo' | 'in_progress' | 'review' | 'done'
  ) => {
    try {
      console.log(`Updating task ${taskId} status to ${newStatus}`);

      // Convert 'done' to 'completed' for the database
      const dbStatus = newStatus === 'done' ? 'completed' : newStatus;
      console.log(`Database status will be: ${dbStatus}`);

      // Use the updateTask hook
      const success = await updateTask(taskId, { status: dbStatus });

      if (!success) {
        throw new Error('Failed to update task');
      }

      console.log('Task updated successfully in database');

      // Update local state
      setTasks(
        tasks.map((task) =>
          task.id === taskId ? { ...task, status: newStatus } : task
        )
      );

      toast.success('Task status updated');
    } catch (error) {
      console.error('Error updating task status:', error);
      toast.error('Failed to update task status');
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      console.log(`Deleting task ${taskId}`);

      // Use the deleteTask hook
      const success = await deleteTask(taskId);

      if (!success) {
        throw new Error('Failed to delete task');
      }

      console.log('Task deleted successfully from database');

      // Update local state
      setTasks(tasks.filter((task) => task.id !== taskId));
      toast.success('Task deleted');
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error('Failed to delete task');
    }
  };

  // Filter tasks based on search query and filters
  const filteredTasks = tasks.filter((task) => {
    const matchesSearch =
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (task.description &&
        task.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = !statusFilter || task.status === statusFilter;
    const matchesPriority = !priorityFilter || task.priority === priorityFilter;
    const matchesAssignedTo =
      !assignedToFilter || task.assignedTo === assignedToFilter;

    return (
      matchesSearch && matchesStatus && matchesPriority && matchesAssignedTo
    );
  });

  // Group tasks by status
  const tasksByStatus = {
    todo: filteredTasks.filter((task) => task.status === 'todo'),
    in_progress: filteredTasks.filter((task) => task.status === 'in_progress'),
    review: filteredTasks.filter((task) => task.status === 'review'),
    done: filteredTasks.filter((task) => task.status === 'done'),
  };

  if (projectsLoading || isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <ClipboardCheck className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Task Tracking</h1>
        </div>
        <Skeleton className="h-[500px] w-full rounded-lg" />
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <ClipboardCheck className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Task Tracking</h1>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <ClipboardCheck className="size-10 text-neutral-300 mb-4" />
            <h2 className="text-xl font-semibold mb-2">No Projects Found</h2>
            <p className="text-neutral-500 text-center max-w-md mb-6">
              You need to create or join a project before you can start tracking
              tasks.
            </p>
            <Button>Create a Project</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <main className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <ClipboardCheck className="size-5 text-neutral-500" />
        <h1 className="text-2xl font-bold">Task Tracking</h1>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full md:w-auto">
                {selectedProject
                  ? projects.find((p) => p.id === selectedProject)?.name ||
                    'Select Project'
                  : 'Select Project'}
                <ChevronDown className="ml-2 size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Projects</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {projects.map((project) => (
                <DropdownMenuItem
                  key={project.id}
                  onClick={() => setSelectedProject(project.id)}
                >
                  {project.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Input
            placeholder="Search tasks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full md:w-auto md:min-w-[250px]"
          />
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1">
                <Filter className="size-4" />
                <span>Filter</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Status</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setStatusFilter(null)}>
                All
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('todo')}>
                To Do
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('in_progress')}>
                In Progress
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('review')}>
                Review
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setStatusFilter('done')}>
                Done
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuLabel>Priority</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setPriorityFilter(null)}>
                All
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setPriorityFilter('low')}>
                Low
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setPriorityFilter('medium')}>
                Medium
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setPriorityFilter('high')}>
                High
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setPriorityFilter('urgent')}>
                Urgent
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            className="gap-1"
            onClick={() =>
              handleCreateTask({
                title: 'New Task',
                description: '',
                status: 'todo',
                priority: 'medium',
              })
            }
          >
            <Plus className="size-4" />
            <span>Add Task</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* To Do Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>To Do</span>
            <div className="bg-neutral-200 text-neutral-700 text-xs px-2 py-0.5 rounded-full">
              {tasksByStatus.todo.length}
            </div>
          </h3>
          <div className="bg-neutral-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.todo.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.todo.map((task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={
                      members.find((m) => m.userId === task.assignedTo) || null
                    }
                    onStatusChange={handleUpdateTaskStatus}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* In Progress Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>In Progress</span>
            <div className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full">
              {tasksByStatus.in_progress.length}
            </div>
          </h3>
          <div className="bg-blue-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.in_progress.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.in_progress.map((task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={
                      members.find((m) => m.userId === task.assignedTo) || null
                    }
                    onStatusChange={handleUpdateTaskStatus}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Review Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>Review</span>
            <div className="bg-amber-100 text-amber-700 text-xs px-2 py-0.5 rounded-full">
              {tasksByStatus.review.length}
            </div>
          </h3>
          <div className="bg-amber-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.review.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.review.map((task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={
                      members.find((m) => m.userId === task.assignedTo) || null
                    }
                    onStatusChange={handleUpdateTaskStatus}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Done Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>Done</span>
            <div className="bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded-full">
              {tasksByStatus.done.length}
            </div>
          </h3>
          <div className="bg-green-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.done.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.done.map((task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={
                      members.find((m) => m.userId === task.assignedTo) || null
                    }
                    onStatusChange={handleUpdateTaskStatus}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}

interface TaskCardProps {
  task: Task;
  member: Member | null;
  onStatusChange: (
    taskId: string,
    status: 'todo' | 'in_progress' | 'review' | 'done'
  ) => void;
  onDelete: () => void;
}

function TaskCard({ task, member, onStatusChange, onDelete }: TaskCardProps) {
  return (
    <div className="bg-white rounded-md border p-3 shadow-sm">
      <div className="flex justify-between items-start">
        <div className="space-y-1">
          <h4 className="font-medium text-sm">{task.title}</h4>
          {task.description && (
            <p className="text-xs text-neutral-500 line-clamp-2">
              {task.description}
            </p>
          )}
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onStatusChange(task.id, 'todo')}>
              Move to To Do
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onStatusChange(task.id, 'in_progress')}
            >
              Move to In Progress
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onStatusChange(task.id, 'review')}>
              Move to Review
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onStatusChange(task.id, 'done')}>
              Move to Done
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={onDelete} className="text-red-600">
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="mt-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          {member ? (
            <div className="flex items-center gap-1">
              <UserAvatar
                size="sm"
                avatarUrl={member.avatarUrl}
                fallbackText={member.fullName || member.email}
              />
              <span className="text-xs text-neutral-500">
                {member.fullName || member.email.split('@')[0]}
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-1">
              <User className="size-3 text-neutral-400" />
              <span className="text-xs text-neutral-400">Unassigned</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-1">
          {task.priority === 'high' || task.priority === 'urgent' ? (
            <div className="text-xs px-1.5 py-0.5 rounded bg-red-100 text-red-700">
              {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
            </div>
          ) : task.priority === 'medium' ? (
            <div className="text-xs px-1.5 py-0.5 rounded bg-amber-100 text-amber-700">
              Medium
            </div>
          ) : (
            <div className="text-xs px-1.5 py-0.5 rounded bg-green-100 text-green-700">
              Low
            </div>
          )}
        </div>
      </div>

      {task.dueDate && (
        <div className="mt-2 flex items-center text-xs text-neutral-500">
          <Clock className="size-3 mr-1" />
          <span>Due {new Date(task.dueDate).toLocaleDateString()}</span>
        </div>
      )}
    </div>
  );
}

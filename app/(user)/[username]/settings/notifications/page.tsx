'use client';

import { PushNotificationSettings } from '@/components/settings/PushNotificationSettings';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSettings } from '@/lib/hooks';
import { userService } from '@/lib/services/userService';
import { Bell, Mail, MessageSquare, Phone, Smartphone } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface NotificationPreference {
  id: string;
  label: string;
  description: string;
  enabled: boolean;
  channels: {
    email: boolean;
    app: boolean;
    sms: boolean;
  };
}

export default function NotificationsSettingsPage() {
  const { settings, loading, fetchSettings, updateNotificationSettings } =
    useSettings();

  // Load user settings on component mount
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // Update notification preferences based on user settings
  useEffect(() => {
    if (settings && settings.email_notifications) {
      setNotificationPreferences((prev) =>
        prev.map((pref) => {
          if (pref.id === 'document_updates') {
            return {
              ...pref,
              enabled: settings.email_notifications?.document_updates || false,
            };
          } else if (pref.id === 'signatures') {
            return {
              ...pref,
              enabled: settings.email_notifications?.signatures || false,
            };
          } else if (pref.id === 'consultations') {
            return {
              ...pref,
              enabled: settings.email_notifications?.consultations || false,
            };
          } else if (pref.id === 'payments') {
            return {
              ...pref,
              enabled: settings.email_notifications?.payments || false,
            };
          } else if (pref.id === 'marketing') {
            return {
              ...pref,
              enabled: settings.email_notifications?.marketing || false,
            };
          }
          return pref;
        })
      );
    }
  }, [settings]);

  // Notification preferences data
  const [notificationPreferences, setNotificationPreferences] = useState<
    NotificationPreference[]
  >([
    {
      id: 'document_updates',
      label: 'Document Updates',
      description:
        'Get notified when your documents are updated or commented on',
      enabled: true,
      channels: {
        email: true,
        app: true,
        sms: false,
      },
    },
    {
      id: 'signatures',
      label: 'Signatures Required',
      description: 'Get notified when your signature is required on a document',
      enabled: true,
      channels: {
        email: true,
        app: true,
        sms: true,
      },
    },
    {
      id: 'consultations',
      label: 'Consultation Alerts',
      description: 'Receive reminders about upcoming lawyer consultations',
      enabled: true,
      channels: {
        email: true,
        app: true,
        sms: false,
      },
    },
    {
      id: 'payments',
      label: 'Payment Reminders',
      description:
        'Get notified about upcoming payments and subscription changes',
      enabled: true,
      channels: {
        email: true,
        app: false,
        sms: false,
      },
    },
    {
      id: 'marketing',
      label: 'Marketing Updates',
      description: 'Receive news, offers, and feature updates',
      enabled: true,
      channels: {
        email: true,
        app: true,
        sms: false,
      },
    },
  ]);

  // Toggle notification type
  const toggleNotificationType = async (id: string, enabled: boolean) => {
    // Update local state
    setNotificationPreferences(
      notificationPreferences.map((pref) =>
        pref.id === id ? { ...pref, enabled } : pref
      )
    );

    // Create update object for this specific notification type
    const updates = {
      email_notifications: {
        [id]: enabled,
      },
    };

    // Create the operation promise
    const updatePromise = updateNotificationSettings(updates);

    // Use toast.promise for better user feedback
    toast.promise(updatePromise, {
      loading: `${enabled ? 'Enabling' : 'Disabling'} ${id.replace('_', ' ')} notifications...`,
      success: `${enabled ? 'Enabled' : 'Disabled'} ${id.replace('_', ' ')} notifications`,
      error: 'Failed to update notification setting',
    });

    try {
      // Wait for the operation to complete
      const result = await updatePromise;

      if (!result) {
        throw new Error('Failed to update notification setting');
      }
    } catch (error) {
      console.error('Error updating notification setting:', error);

      // Revert local state on error
      setNotificationPreferences(
        notificationPreferences.map((pref) =>
          pref.id === id ? { ...pref, enabled: !enabled } : pref
        )
      );
    }
  };

  // Toggle notification channel
  const toggleNotificationChannel = async (
    id: string,
    channel: 'email' | 'app' | 'sms',
    value: boolean
  ) => {
    // Update local state
    setNotificationPreferences(
      notificationPreferences.map((pref) =>
        pref.id === id
          ? { ...pref, channels: { ...pref.channels, [channel]: value } }
          : pref
      )
    );

    // Create update object for notification channels
    // We'll update the email_notifications object since that's what our database supports
    // In a real implementation, we would have a more complex structure for different channels
    const updates = {
      email_notifications: {
        // We're using the same setting for all channels for simplicity
        [id]: value,
      },
    };

    // Create the operation promise
    const updatePromise = updateNotificationSettings(updates);

    // Use toast.promise for better user feedback
    toast.promise(updatePromise, {
      loading: `${value ? 'Enabling' : 'Disabling'} ${channel} notifications for ${id.replace('_', ' ')}...`,
      success: `${value ? 'Enabled' : 'Disabled'} ${channel} notifications for ${id.replace('_', ' ')}`,
      error: 'Failed to update notification channel',
    });

    try {
      // Wait for the operation to complete
      const result = await updatePromise;

      if (!result) {
        throw new Error('Failed to update notification channel');
      }
    } catch (error) {
      console.error('Error updating notification channel:', error);

      // Revert local state on error
      setNotificationPreferences(
        notificationPreferences.map((pref) =>
          pref.id === id
            ? { ...pref, channels: { ...pref.channels, [channel]: !value } }
            : pref
        )
      );
    }
  };

  // Save all notification preferences
  const [isSaving, setIsSaving] = useState(false);
  const savePreferences = async () => {
    setIsSaving(true);

    // Create the update object
    const updates = {
      notifications_enabled: true,
      email_notifications: {
        document_updates:
          notificationPreferences.find((p) => p.id === 'document_updates')
            ?.enabled || false,
        signatures:
          notificationPreferences.find((p) => p.id === 'signatures')?.enabled ||
          false,
        consultations:
          notificationPreferences.find((p) => p.id === 'consultations')
            ?.enabled || false,
        payments:
          notificationPreferences.find((p) => p.id === 'payments')?.enabled ||
          false,
        marketing:
          notificationPreferences.find((p) => p.id === 'marketing')?.enabled ||
          false,
      },
    };

    // Create the operation promise
    const updatePromise = updateNotificationSettings(updates);

    // Use toast.promise for better user feedback
    toast.promise(updatePromise, {
      loading: 'Saving notification preferences...',
      success: 'Notification preferences saved successfully',
      error: 'Failed to save notification preferences',
    });

    try {
      // Wait for the operation to complete
      const result = await updatePromise;

      if (!result) {
        throw new Error('Failed to save notification preferences');
      }
    } catch (error) {
      console.error('Error saving notification preferences:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Send a test notification
  const [isSendingTest, setIsSendingTest] = useState(false);
  const sendTestNotification = async (type: 'email' | 'app' | 'sms') => {
    setIsSendingTest(true);

    // Create the operation promise
    const sendPromise = userService.sendTestNotification(type);

    // Use toast.promise for better user feedback
    toast.promise(sendPromise, {
      loading: `Sending test ${type} notification...`,
      success: `Test ${type} notification sent successfully`,
      error: `Failed to send test ${type} notification`,
    });

    try {
      // Wait for the operation to complete
      await sendPromise;
    } catch (error) {
      console.error('Exception in sendTestNotification:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      } else {
        console.error('Unknown error type:', typeof error);
      }
    } finally {
      setIsSendingTest(false);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">
          Notification Settings
        </h1>
        <p className="text-muted-foreground">
          Manage how you receive notifications and alerts
        </p>
        {loading && (
          <div className="mt-2 text-sm text-muted-foreground">
            Loading your preferences...
          </div>
        )}
      </div>

      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Types</CardTitle>
          <CardDescription>
            Enable or disable different types of notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {notificationPreferences.map((pref) => (
            <div key={pref.id} className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor={pref.id} className="font-medium">
                  {pref.label}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {pref.description}
                </p>
              </div>
              <Switch
                id={pref.id}
                checked={pref.enabled}
                onCheckedChange={(checked) =>
                  toggleNotificationType(pref.id, checked)
                }
              />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Notification Channels */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Channels</CardTitle>
          <CardDescription>
            Choose how you want to receive each type of notification
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            <div className="rounded-md border">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="h-10 px-4 text-left font-medium">
                      Notification Type
                    </th>
                    <th className="h-10 px-2 text-center font-medium">
                      <div className="flex flex-col items-center">
                        <Mail className="h-4 w-4 mb-1" />
                        <span className="text-xs">Email</span>
                      </div>
                    </th>
                    <th className="h-10 px-2 text-center font-medium">
                      <div className="flex flex-col items-center">
                        <Bell className="h-4 w-4 mb-1" />
                        <span className="text-xs">In-App</span>
                      </div>
                    </th>
                    <th className="h-10 px-2 text-center font-medium">
                      <div className="flex flex-col items-center">
                        <Phone className="h-4 w-4 mb-1" />
                        <span className="text-xs">SMS</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {notificationPreferences.map((pref) => (
                    <tr key={pref.id} className="border-b">
                      <td className="p-4 align-middle font-medium">
                        {pref.label}
                      </td>
                      <td className="p-2 align-middle text-center">
                        <Checkbox
                          disabled={!pref.enabled}
                          checked={pref.enabled && pref.channels.email}
                          onCheckedChange={(checked) =>
                            toggleNotificationChannel(
                              pref.id,
                              'email',
                              checked as boolean
                            )
                          }
                        />
                      </td>
                      <td className="p-2 align-middle text-center">
                        <Checkbox
                          disabled={!pref.enabled}
                          checked={pref.enabled && pref.channels.app}
                          onCheckedChange={(checked) =>
                            toggleNotificationChannel(
                              pref.id,
                              'app',
                              checked as boolean
                            )
                          }
                        />
                      </td>
                      <td className="p-2 align-middle text-center">
                        <Checkbox
                          disabled={!pref.enabled}
                          checked={pref.enabled && pref.channels.sms}
                          onCheckedChange={(checked) =>
                            toggleNotificationChannel(
                              pref.id,
                              'sms',
                              checked as boolean
                            )
                          }
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <Button onClick={savePreferences} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Preferences'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Push Notifications */}
      <PushNotificationSettings />

      {/* Notification Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Preview</CardTitle>
          <CardDescription>
            Preview how your notifications will look
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="email">
            <TabsList className="mb-4">
              <TabsTrigger value="email">Email</TabsTrigger>
              <TabsTrigger value="app">In-App</TabsTrigger>
              <TabsTrigger value="sms">SMS</TabsTrigger>
              <TabsTrigger value="push">Push</TabsTrigger>
            </TabsList>

            <TabsContent value="email" className="space-y-4">
              <div className="rounded-md border p-4">
                <div className="font-medium">Document Signature Required</div>
                <p className="text-sm mt-2">
                  Hello, your signature is required on &quot;Contract
                  #123&quot;. Please review and sign at your earliest
                  convenience.
                </p>
                <div className="mt-4 bg-primary text-primary-foreground font-medium text-sm rounded-md py-1 px-3 inline-block">
                  Review Document
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => sendTestNotification('email')}
                disabled={isSendingTest}
              >
                {isSendingTest ? 'Sending...' : 'Send Test Email'}
              </Button>
            </TabsContent>

            <TabsContent value="app" className="space-y-4">
              <div className="rounded-md border p-4 bg-card">
                <div className="flex items-start">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                    <MessageSquare className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">
                      Comment on your document
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      John Smith commented on &quot;Employment Agreement&quot;
                    </p>
                    <p className="text-xs mt-3">2 minutes ago</p>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => sendTestNotification('app')}
                disabled={isSendingTest}
              >
                {isSendingTest ? 'Sending...' : 'Send Test In-App Notification'}
              </Button>
            </TabsContent>

            <TabsContent value="sms" className="space-y-4">
              <div className="rounded-md border p-4 font-mono text-sm">
                <p>
                  NotAMess Forms: Your consultation with Jane Doe is scheduled
                  for tomorrow at 2:00 PM. Reply YES to confirm.
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => sendTestNotification('sms')}
                disabled={isSendingTest}
              >
                {isSendingTest ? 'Sending...' : 'Send Test SMS'}
              </Button>
            </TabsContent>

            <TabsContent value="push" className="space-y-4">
              <div className="rounded-md border p-4 bg-card">
                <div className="flex items-start">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                    <Smartphone className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">Forms NotAMess</p>
                        <p className="text-sm font-medium mt-1">
                          Consultation Reminder
                        </p>
                      </div>
                      <p className="text-xs text-muted-foreground">now</p>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Your consultation with John Smith is in 30 minutes. Click
                      to join.
                    </p>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => sendTestNotification('app')}
                disabled={isSendingTest}
              >
                {isSendingTest ? 'Sending...' : 'Send Test Push Notification'}
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

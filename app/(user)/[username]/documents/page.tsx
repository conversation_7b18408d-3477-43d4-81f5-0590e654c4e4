'use client';

import {
  AdvancedSearchPanel,
  DocumentEmptyState,
  DocumentLoadingState,
  DocumentShareSheet,
  OrganizerSheet,
} from '@/components/documents';
import { lazy, Suspense } from 'react';

import { Button } from '@/components/ui/button';
import { useDocuments } from '@/lib/hooks';
// Import types from database-modules
import { DocumentStatus, DocumentType } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import {
  FileTextIcon,
  FolderIcon,
  LayoutGrid,
  List,
  PlusIcon,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Create wrapper components for lazy loading
const DocumentCardGridLazy = lazy(() =>
  import('@/components/documents/ui/DocumentCardGrid').then((module) => ({
    default: module.DocumentCardGrid,
  }))
);

const DocumentTableLazy = lazy(() =>
  import('@/components/documents/ui/DocumentTable').then((module) => ({
    default: module.DocumentTable,
  }))
);

export default function DocumentsPage() {
  const { username } = useParams();
  const router = useRouter();
  const {
    documentSummaries: documents,
    loading,
    error,
    fetchDocumentSummaries,
  } = useDocuments();

  // Use document operations from the hook
  const { deleteDocument } = useDocuments();
  // Advanced search filters
  const [searchFilters, setSearchFilters] = useState<{
    query: string;
    status: DocumentStatus | 'all';
    type: DocumentType | 'all';
    dateRange: {
      from: Date | undefined;
      to: Date | undefined;
    };
    tags: string[];
    sortBy: string;
    sortOrder: 'asc' | 'desc';
    showTemplates: boolean;
  }>({
    query: '',
    status: 'all',
    type: 'all',
    dateRange: { from: undefined, to: undefined },
    tags: [],
    sortBy: 'updated_at',
    sortOrder: 'desc',
    showTemplates: true,
  });

  // For backward compatibility with existing code
  const searchQuery = searchFilters.query;
  const statusFilter = searchFilters.status;
  const typeFilter = searchFilters.type;
  const sortOrder = searchFilters.sortOrder;

  const [isOrganizerOpen, setIsOrganizerOpen] = useState(false);
  const [isShareSheetOpen, setIsShareSheetOpen] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [selectedDocumentTitle, setSelectedDocumentTitle] =
    useState<string>('');
  const [viewMode, setViewMode] = useState<'card' | 'table'>('card');

  // Use useEffect to fetch document summaries when component mounts
  // Always fetch on mount, regardless of state
  useEffect(() => {
    fetchDocumentSummaries();
  }, [fetchDocumentSummaries]);

  const handleDeleteDocument = async (id: string) => {
    // Use Sonner toast with cancel and delete actions
    toast('Delete Document', {
      description:
        'Are you sure you want to delete this document? This action cannot be undone.',
      duration: 10000, // 10 seconds
      action: {
        label: 'Delete',
        onClick: async () => {
          try {
            await deleteDocument.mutate({ id });
            // Refresh document list after deletion
            fetchDocumentSummaries();
            toast.success('Document deleted successfully');
          } catch (error) {
            console.error('Error deleting document:', error);
            toast.error('Failed to delete document');
          }
        },
      },
      cancel: {
        label: 'Cancel',
        onClick: () => {
          // Do nothing, just close the toast
        },
      },
    });
  };

  const handleBatchDelete = (ids: string[]) => {
    // Use Sonner toast with cancel and delete actions
    toast('Delete Documents', {
      description: `Are you sure you want to delete ${ids.length} ${ids.length === 1 ? 'document' : 'documents'}? This action cannot be undone.`,
      duration: 10000, // 10 seconds
      action: {
        label: 'Delete',
        onClick: async () => {
          try {
            // Delete each document in sequence
            const results = await Promise.all(
              ids.map((id) => deleteDocument.mutate({ id }))
            );
            const successCount = results.filter(Boolean).length;

            // Refresh document list after deletion
            fetchDocumentSummaries();

            if (successCount === ids.length) {
              toast.success(
                `${successCount} ${successCount === 1 ? 'document' : 'documents'} deleted successfully`
              );
            } else {
              toast.warning(
                `${successCount} of ${ids.length} documents deleted. Some deletions failed.`
              );
            }
          } catch (error) {
            console.error('Error deleting documents:', error);
            toast.error('Failed to delete documents');
          }
        },
      },
      cancel: {
        label: 'Cancel',
        onClick: () => {
          // Do nothing, just close the toast
        },
      },
    });
  };

  const handleShareDocument = async (id: string) => {
    try {
      // Find the document to share
      const documentToShare = documents.find((doc) => doc.id === id);
      if (documentToShare) {
        setSelectedDocumentId(id);
        setSelectedDocumentTitle(documentToShare.title || 'Untitled Document');
        setIsShareSheetOpen(true);
      } else {
        toast.error('Document not found');
      }
    } catch (error) {
      console.error('Error sharing document:', error);
      toast.error('Failed to share document');
    }
  };

  const getDocumentTypeIcon = (type: string | null) => {
    switch (type) {
      case 'contract':
        return <FileTextIcon className="h-4 w-4 text-blue-500" />;
      case 'form':
        return <FileTextIcon className="h-4 w-4 text-green-500" />;
      case 'letter':
        return <FileTextIcon className="h-4 w-4 text-amber-500" />;
      case 'agreement':
        return <FileTextIcon className="h-4 w-4 text-purple-500" />;
      case 'report':
        return <FileTextIcon className="h-4 w-4 text-red-500" />;
      default:
        return <FileTextIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-700 border border-gray-300';
      case 'published':
        return 'bg-green-100 text-green-700 border border-green-300';
      case 'archived':
        return 'bg-amber-100 text-amber-700 border border-amber-300';
      case 'template':
        return 'bg-blue-100 text-blue-700 border border-blue-300';
      default:
        return 'bg-gray-100 text-gray-700 border border-gray-300';
    }
  };

  const filteredDocuments = documents
    .filter((doc) => {
      // Text search
      const matchesSearch = searchFilters.query
        ? (doc.title || '')
            .toLowerCase()
            .includes(searchFilters.query.toLowerCase()) ||
          (doc.description
            ?.toLowerCase()
            .includes(searchFilters.query.toLowerCase()) ??
            false)
        : true;

      // Status filter
      const matchesStatus =
        searchFilters.status === 'all'
          ? true
          : doc.status === searchFilters.status;

      // Type filter
      const matchesType =
        searchFilters.type === 'all'
          ? true
          : doc.document_type === searchFilters.type;

      // Template filter
      const matchesTemplate = searchFilters.showTemplates
        ? true
        : doc.status !== 'template';

      // Date range filter
      const docDate = new Date(doc.updated_at || doc.created_at || new Date());
      const matchesDateFrom = searchFilters.dateRange.from
        ? docDate >= searchFilters.dateRange.from
        : true;
      const matchesDateTo = searchFilters.dateRange.to
        ? docDate <= searchFilters.dateRange.to
        : true;

      // Tags filter (if we have tags in the future)
      const matchesTags =
        searchFilters.tags.length > 0
          ? Array.isArray(doc.tags) &&
            doc.tags.some((tag) => searchFilters.tags.includes(tag as string))
          : true;

      return (
        matchesSearch &&
        matchesStatus &&
        matchesType &&
        matchesTemplate &&
        matchesDateFrom &&
        matchesDateTo &&
        matchesTags
      );
    })
    .sort((a, b) => {
      // Dynamic sorting based on selected field
      const getSortValue = (doc: any) => {
        switch (searchFilters.sortBy) {
          case 'title':
            return (doc.title || '').toLowerCase();
          case 'created_at':
            return new Date(doc.created_at || new Date()).getTime();
          case 'document_type':
            return (doc.document_type || '').toLowerCase();
          case 'status':
            return (doc.status || '').toLowerCase();
          case 'updated_at':
          default:
            return new Date(doc.updated_at || new Date()).getTime();
        }
      };

      const valueA = getSortValue(a);
      const valueB = getSortValue(b);

      // Handle string vs number comparison
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return searchFilters.sortOrder === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      }

      return searchFilters.sortOrder === 'asc'
        ? (valueA as number) - (valueB as number)
        : (valueB as number) - (valueA as number);
    });

  const uniqueStatuses = Array.from(
    new Set(documents.map((doc) => doc.status))
  );

  const uniqueTypes = Array.from(
    new Set(documents.map((doc) => doc.document_type))
  );

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Documents</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setIsOrganizerOpen(true)}>
            <FolderIcon className="mr-2 h-4 w-4" />
            Organize
          </Button>
          <Button
            onClick={() => router.push(`/${username}/documents/templates`)}
          >
            <FileTextIcon className="mr-2 h-4 w-4" />
            Templates
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <AdvancedSearchPanel
          filters={searchFilters}
          onFiltersChange={setSearchFilters}
          availableTags={[]} // We'll add tags in the future
          availableTypes={uniqueTypes.filter(
            (type): type is string => type !== null
          )}
          availableStatuses={uniqueStatuses.filter(
            (status): status is string => status !== null
          )}
        />
      </div>

      <div className="flex justify-between items-center mb-8">
        <Button
          onClick={() => router.push(`/${username}/documents/new`)}
          size={'sm'}
          className={cn('rounded-md')}
        >
          <PlusIcon className="mr-2 h-4 w-4" />
          New Document
        </Button>
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'card' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setViewMode('card')}
            title="Card View"
          >
            <LayoutGrid size={16} />
          </Button>
          <Button
            variant={viewMode === 'table' ? 'default' : 'outline'}
            size="icon"
            onClick={() => setViewMode('table')}
            title="Table View"
          >
            <List size={16} />
          </Button>
        </div>
      </div>

      {/* Content Rendering - Deterministic approach */}
      {(() => {
        // 1. Error state takes precedence
        if (error) {
          return (
            <div className="p-6 bg-red-50 rounded-lg text-red-800">
              <p>Error loading documents: {error.message}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => fetchDocumentSummaries()}
              >
                Retry
              </Button>
            </div>
          );
        }

        // 2. Loading state - always show when loading is true
        if (loading) {
          return <DocumentLoadingState viewMode={viewMode} />;
        }

        // 3. Empty state - no documents at all
        if (documents.length === 0) {
          return (
            <DocumentEmptyState
              onCreateDocument={() => router.push(`/${username}/documents/new`)}
            />
          );
        }

        // 4. Filtered empty state - documents exist but none match filters
        if (filteredDocuments.length === 0) {
          return (
            <div className="text-center py-12 border rounded-lg bg-muted/20">
              <FileTextIcon className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">
                No matching documents
              </h3>
              <p className="mt-2 text-muted-foreground">
                Try adjusting your search or filters
              </p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  setSearchFilters({
                    query: '',
                    status: 'all',
                    type: 'all',
                    dateRange: { from: undefined, to: undefined },
                    tags: [],
                    sortBy: 'updated_at',
                    sortOrder: 'desc',
                    showTemplates: true,
                  });
                }}
              >
                Clear filters
              </Button>
            </div>
          );
        }

        // 5. Content state - show documents in selected view mode
        return viewMode === 'card' ? (
          <Suspense fallback={<DocumentLoadingState viewMode="card" />}>
            <DocumentCardGridLazy
              documents={filteredDocuments}
              username={username as string}
              onDelete={handleDeleteDocument}
              onShare={handleShareDocument}
              onBatchDelete={handleBatchDelete}
              getDocumentTypeIcon={getDocumentTypeIcon}
              getStatusColor={getStatusColor}
            />
          </Suspense>
        ) : (
          <Suspense fallback={<DocumentLoadingState viewMode="table" />}>
            <DocumentTableLazy
              documents={filteredDocuments}
              username={username as string}
              onDelete={handleDeleteDocument}
              onShare={handleShareDocument}
              onBatchDelete={handleBatchDelete}
              getDocumentTypeIcon={getDocumentTypeIcon}
              getStatusColor={getStatusColor}
            />
          </Suspense>
        );
      })()}

      <OrganizerSheet
        isOpen={isOrganizerOpen}
        onClose={() => setIsOrganizerOpen(false)}
      />
      <DocumentShareSheet
        isOpen={isShareSheetOpen}
        onClose={() => setIsShareSheetOpen(false)}
        documentId={selectedDocumentId}
        documentTitle={selectedDocumentTitle}
      />
    </div>
  );
}

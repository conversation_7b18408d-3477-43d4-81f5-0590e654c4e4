'use client';

import { DocumentSection, SectionBasedEditor } from '@/components/documents';
import { SignatureSection } from '@/components/documents/signature';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import * as Textarea from '@/components/ui/textarea';
import { useDocuments } from '@/lib/hooks';
import { supabaseClient } from '@/lib/supabase/client';

import {
  Document,
  DocumentStatus,
  DocumentType,
  DocumentUpdate,
  <PERSON><PERSON>,
} from '@/lib/types/database-modules';
import {
  ArrowLeftIcon,
  FileIcon,
  InfoIcon,
  SaveIcon,
  TrashIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useEffect, useState } from 'react';
import { toast } from 'sonner';

type DocumentEditClientProps = {
  params: Promise<{
    id: string;
    username: string;
  }>;
};

export default function DocumentEditClient({
  params,
}: DocumentEditClientProps) {
  const router = useRouter();
  const { getById, updateDocument, deleteDocument, addDocumentActivity } =
    useDocuments();
  const docParams = use(params);

  // Define document content interface that matches the Json type
  interface DocumentContent {
    sections: Array<{
      id: string;
      title: string;
      content: string;
    }>;
    signature?: string | null;
  }

  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [contentSections, setContentSections] = useState<DocumentSection[]>([]);
  const [signatureData, setSignatureData] = useState<string | null>(null);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [documentType, setDocumentType] = useState<DocumentType>(
    DocumentType.FORM
  );
  const [status, setStatus] = useState<DocumentStatus>(DocumentStatus.DRAFT);
  const [isTemplate, setIsTemplate] = useState(false);

  useEffect(() => {
    const fetchDocument = async () => {
      setLoading(true);
      try {
        console.log('Fetching document with ID:', docParams.id);

        const doc = await getById(docParams.id as string);

        if (!doc) {
          console.error('Document not found or returned null');
          setError(new Error('Document not found'));
          setLoading(false);
          return;
        }

        console.log('Document fetched successfully:', doc.id);
        setDocument(doc);

        // Initialize form state
        setTitle(doc.title);
        setDescription(doc.description || '');
        setDocumentType(doc.document_type as DocumentType);
        setStatus(doc.status as DocumentStatus);
        setIsTemplate(doc.is_template);

        // Initialize content sections
        if (doc.content && typeof doc.content === 'object') {
          // Cast the content to our DocumentContent type
          const content = doc.content as unknown as DocumentContent;
          console.log('Document content:', JSON.stringify(content, null, 2));

          if (content.sections && Array.isArray(content.sections)) {
            // Convert to DocumentSection format with IDs, preserving existing IDs if available
            const sections = content.sections.map((section: any) => ({
              id: section.id || Math.random().toString(36).substring(2, 11),
              title: section.title || '',
              content: section.content || '',
              isSignatureSection: section.isSignatureSection || false,
              aiSuggestions: section.aiSuggestions || [],
            }));
            console.log('Parsed sections:', sections.length);
            setContentSections(sections);
          } else {
            console.warn('No sections array found in document content');
            setContentSections([
              {
                id: Math.random().toString(36).substring(2, 11),
                title: '',
                content: '',
              },
            ]);
          }

          // Load signature if available
          if (content.signature) {
            setSignatureData(content.signature);
            console.log('Signature data loaded');
          }
        } else if (doc.content && typeof doc.content === 'string') {
          // Handle string content (legacy format or plain text)
          console.log(
            'Document content is a string, creating a single section'
          );
          setContentSections([
            {
              id: Math.random().toString(36).substring(2, 11),
              title: 'Content',
              content: doc.content,
            },
          ]);
        } else {
          console.warn('No content found in document, creating empty section');
          setContentSections([
            {
              id: Math.random().toString(36).substring(2, 11),
              title: '',
              content: '',
            },
          ]);
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching document:', err);
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to fetch document';
        console.error(`Document fetch error details: ${errorMessage}`);
        setError(
          err instanceof Error ? err : new Error('Failed to fetch document')
        );
        // Set default empty section to avoid null reference errors
        setContentSections([
          {
            id: Math.random().toString(36).substring(2, 11),
            title: '',
            content: '',
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();
  }, [docParams.id]);

  const handleSave = async () => {
    if (!document) return;

    // Validate required fields
    if (!title.trim()) {
      toast.error('Title is required');
      return;
    }

    // Ensure sections are properly formatted for JSON serialization
    const formattedSections = contentSections.map((section) => {
      // Log each section for debugging
      console.log('Processing section:', section);

      return {
        id: section.id,
        title: section.title || '',
        content: section.content || '',
        // Only include other properties if they exist and are not undefined
        ...(section.isSignatureSection !== undefined
          ? { isSignatureSection: section.isSignatureSection }
          : {}),
        ...(section.aiSuggestions?.length
          ? { aiSuggestions: section.aiSuggestions }
          : {}),
      };
    });

    // Convert content to a format that matches the Json type
    const documentContent = {
      sections: formattedSections,
      signature: signatureData,
    };

    // Log the final document content for debugging
    console.log(
      'Final document content:',
      JSON.stringify(documentContent, null, 2)
    );

    // Create a simple update object with basic properties
    const updates: DocumentUpdate = {
      title,
      description,
      document_type: documentType,
      status,
      is_template: isTemplate,
      // For content, use a simple object structure without complex nesting
      content: {
        sections: formattedSections.map((section) => ({
          id: section.id,
          title: section.title,
          content: section.content,
        })),
        signature: signatureData,
      } as Json,
    };

    console.log(
      'Saving document with updates:',
      JSON.stringify(updates, null, 2)
    );

    // Track document edit activity
    try {
      await addDocumentActivity(document.id, 'edit', {
        title: title,
        status: status,
        is_template: isTemplate,
      });
    } catch (activityError) {
      console.error('Error tracking edit activity:', activityError);
      // Continue with save even if activity tracking fails
    }

    try {
      // Log the update data for debugging
      console.log(
        'Sending update with data:',
        JSON.stringify(
          {
            id: document.id,
            ...updates,
          },
          null,
          2
        )
      );

      // Show loading toast
      const toastId = toast.loading('Saving document...');

      // First, get the latest version number for this document
      const { data: versionData, error: versionError } = await supabaseClient
        .from('document_versions')
        .select('version')
        .eq('document_id', document.id)
        .order('version', { ascending: false })
        .limit(1)
        .single();

      // Determine the next version number
      let nextVersion = 1; // Default to 1 if no versions exist
      if (!versionError && versionData) {
        nextVersion = versionData.version + 1;
        console.log(
          `Found existing version: ${versionData.version}, incrementing to ${nextVersion}`
        );
      } else if (versionError) {
        if (versionError.code === 'PGRST116') {
          // PGRST116 means no rows returned, which is fine for a new document
          console.log('No existing versions found, using version 1');
        } else {
          console.error('Error fetching latest version:', versionError);
        }
      }

      // Start a transaction to update both the document and create a new version
      const { data, error } = await supabaseClient
        .from('documents')
        .update({
          title: updates.title,
          description: updates.description,
          document_type: updates.document_type,
          status: updates.status,
          is_template: updates.is_template,
          content: updates.content,
          version: nextVersion, // Update the document's version number
        })
        .eq('id', document.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Create a new version record with the incremented version number
      const { error: versionCreateError } = await supabaseClient
        .from('document_versions')
        .insert({
          document_id: document.id,
          version: nextVersion,
          content: updates.content as Json, // Cast to Json type
          created_by: document.owner_id, // Use the document owner ID
          change_summary: 'Document updated',
        });

      if (versionCreateError) {
        console.error('Error creating document version:', versionCreateError);
        // Don't throw here, as the document has already been updated
        // Just log the error and show a warning
        toast.warning(
          'Document updated but version history may be incomplete',
          {
            id: toastId,
          }
        );
      } else {
        // Update toast to success
        toast.success('Document Updated', { id: toastId });
      }

      // Use the returned data as the updated document
      const updatedDocument = data as Document;

      if (updatedDocument) {
        console.log('Document updated successfully:', updatedDocument);
        console.log('Navigating to view page');
        router.push(`/${docParams.username}/documents/${updatedDocument.id}`);
      } else {
        // This shouldn't happen with our improved error handling, but just in case
        console.error('No document returned but no error thrown');
        toast.error('Something went wrong while updating the document');
      }
    } catch (err: unknown) {
      // Create a detailed error message
      let errorMessage = 'Failed to update document';
      if (err instanceof Error) {
        errorMessage = `${errorMessage}: ${err.message}`;
        console.error('Error stack:', err.stack);
      } else {
        console.error('Unknown error type:', typeof err);
        errorMessage = `${errorMessage}: ${JSON.stringify(err)}`;
      }

      console.error('Error in handleSave:', errorMessage);

      // Set the error state for the component
      setError(new Error(errorMessage));

      // Show a more detailed error toast
      toast.error('Document update failed', {
        description: errorMessage,
        duration: 5000,
      });
    }
  };

  const handleDeleteDocument = () => {
    if (!document) return;

    // Use Sonner toast with cancel and delete actions
    toast('Delete Document', {
      description:
        'Are you sure you want to delete this document? This action cannot be undone.',
      duration: 10000, // 10 seconds
      action: {
        label: 'Delete',
        onClick: async () => {
          try {
            // Create the delete operation
            const deletePromise = deleteDocument.mutate({ id: document.id });

            // Use toast.promise with the operation
            toast.promise(deletePromise, {
              loading: 'Deleting document...',
              success: 'Document Deleted',
              error: (err: Error) =>
                `Failed to delete document: ${err instanceof Error ? err.message : 'Please try again.'}`,
            });

            // Wait for the document deletion to complete
            const result = await deletePromise;

            if (result && result.success) {
              console.log(
                'Document deleted successfully, navigating to documents page'
              );
              router.push(`/${docParams.username}/documents`);
            } else {
              // This shouldn't happen with our improved error handling, but just in case
              console.error('No result returned but no error thrown');
              toast.error('Something went wrong while deleting the document');
            }
          } catch (err: unknown) {
            console.error('Error deleting document:', err);
            setError(
              err instanceof Error
                ? err
                : new Error('Failed to delete document')
            );
            // No need to show another toast as the toast.promise will handle the error display
          }
        },
      },
      cancel: {
        label: 'Cancel',
        onClick: () => {
          // Do nothing, just close the toast
        },
      },
    });
  };

  const handleSectionsChange = (sections: DocumentSection[]) => {
    setContentSections(sections);
  };

  // Function to handle exiting the edit page with confirmation if there are unsaved changes
  const handleExit = () => {
    if (!document) return false;

    // Check if there are unsaved changes
    const hasUnsavedChanges =
      document.title !== title ||
      (document.description || '') !== description ||
      document.document_type !== documentType ||
      document.status !== status ||
      document.is_template !== isTemplate;

    // Also check if content has changed
    const originalContent = document.content as any;
    let contentChanged = false;

    if (
      originalContent &&
      originalContent.sections &&
      Array.isArray(originalContent.sections)
    ) {
      // If the number of sections is different, content has changed
      if (originalContent.sections.length !== contentSections.length) {
        contentChanged = true;
      } else {
        // Check if any section content has changed
        contentChanged = contentSections.some((section, index) => {
          if (index >= originalContent.sections.length) return true;
          return (
            section.content !== originalContent.sections[index].content ||
            section.title !== originalContent.sections[index].title
          );
        });
      }
    }

    if (hasUnsavedChanges || contentChanged) {
      const confirmExit = window.confirm(
        'You have unsaved changes. Are you sure you want to exit?'
      );
      if (!confirmExit) {
        return false;
      }
    }

    // Navigate back to the document view page
    router.push(`/${docParams.username}/documents/${document.id}`);
    return true;
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex items-center mb-8">
          <Button variant="outline" size="icon" className="mr-4">
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>

        <Card className="mb-6">
          <CardHeader>
            <Skeleton className="h-6 w-40" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-24 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-24" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-40 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className="container py-8">
        <div className="flex items-center mb-8">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push('/documents')}
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Error</h1>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <InfoIcon className="mx-auto h-12 w-12 text-red-500" />
              <h2 className="text-xl font-semibold">Document Not Found</h2>
              <p className="text-muted-foreground">
                {error?.message ||
                  'The document you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to edit it.'}
              </p>
              <Button
                onClick={() => router.push(`/${docParams.username}/documents`)}
              >
                Go back to Documents
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={handleExit}
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Edit Document</h1>
        </div>

        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleDeleteDocument}>
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete
          </Button>
          <Button onClick={handleSave}>
            <SaveIcon className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      <Tabs defaultValue="details">
        <TabsList>
          <TabsTrigger value="details">Document Details</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="signature">Signature</TabsTrigger>
          {document.file_url && <TabsTrigger value="file">File</TabsTrigger>}
        </TabsList>

        <TabsContent value="details" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Document title"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea.Root
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Document description"
                >
                  <Textarea.CharCounter
                    current={description.length}
                    max={500}
                  />
                </Textarea.Root>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="grid gap-2">
                  <Label htmlFor="document-type">Document Type</Label>
                  <Select
                    value={documentType}
                    onValueChange={(value) =>
                      setDocumentType(value as DocumentType)
                    }
                  >
                    <SelectTrigger id="document-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="form">Form</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="letter">Letter</SelectItem>
                      <SelectItem value="agreement">Agreement</SelectItem>
                      <SelectItem value="report">Report</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={status}
                    onValueChange={(value) =>
                      setStatus(value as DocumentStatus)
                    }
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                      <SelectItem value="template">Template</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-4">
                <Checkbox
                  id="is-template"
                  checked={isTemplate}
                  onCheckedChange={(checked) => setIsTemplate(checked === true)}
                />
                <Label htmlFor="is-template">Save as Template</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Content</CardTitle>
            </CardHeader>
            <CardContent>
              <SectionBasedEditor
                initialSections={contentSections}
                onChange={handleSectionsChange}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="signature" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileIcon className="h-5 w-5" />
                Document Signature
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Add your signature to this document. This signature will be
                  included when exporting the document.
                </p>
                <SignatureSection
                  onSignatureChange={setSignatureData}
                  initialSignature={
                    document?.content && typeof document.content === 'object'
                      ? ((document.content as unknown as DocumentContent)
                          .signature as string) || null
                      : null
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {document.file_url && (
          <TabsContent value="file" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>File Attachment</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center border rounded-md p-8">
                  <div className="text-center">
                    <FileIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="font-medium">
                      {document.file_url.split('/').pop()}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      {document.file_type} ·{' '}
                      {document.file_size
                        ? `${Math.round(document.file_size / 1024)} KB`
                        : 'Unknown size'}
                    </p>
                    <div className="flex justify-center gap-4">
                      <Button variant="outline" asChild>
                        <a
                          href={document.file_url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Download
                        </a>
                      </Button>
                      <Button variant="shadow_red" size="sm">
                        <TrashIcon className="mr-2 h-4 w-4" />
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>

      <div className="flex justify-end mt-8">
        <Button variant="outline" className="mr-3" onClick={handleExit}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          <SaveIcon className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>
    </div>
  );
}

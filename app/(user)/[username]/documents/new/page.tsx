'use client';

import SectionBasedEditor, {
  DocumentSection,
} from '@/components/documents/editor/SectionBasedEditor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import * as Textarea from '@/components/ui/textarea';
import { useDocuments } from '@/lib/hooks';

import {
  DocumentInsert,
  DocumentStatus,
  DocumentType,
} from '@/lib/types/database-modules';
import { ArrowLeftIcon, SaveIcon } from 'lucide-react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

export default function NewDocumentPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const username = params.username as string;
  const { createDocument } = useDocuments();

  const [error, setError] = useState<Error | null>(null);

  // Check if we're creating a template
  const isTemplateParam = searchParams.get('template') === 'true';

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [documentType, setDocumentType] = useState<string>('form');
  const [status, setStatus] = useState<string>('draft');
  const [isTemplate, setIsTemplate] = useState(isTemplateParam || false);
  const [contentSections, setContentSections] = useState<DocumentSection[]>([
    {
      id: Math.random().toString(36).substring(2, 11),
      title: '',
      content: '',
    },
  ]);

  const handleSectionsChange = (sections: DocumentSection[]) => {
    setContentSections(sections);
  };

  const handleSave = async () => {
    if (!title) {
      setError(new Error('Title is required'));
      return;
    }

    // Convert DocumentSection[] to the format expected by the API
    const apiSections = contentSections.map((section) => ({
      title: section.title,
      content: section.content,
    }));

    const newDocument: DocumentInsert = {
      title,
      description,
      document_type: documentType,
      status,
      is_template: isTemplate,
      content: {
        sections: apiSections,
      },
      template_id: null,
      owner_id: '', // Will be set by the service
      metadata: null,
      file_url: null,
      file_type: null,
      file_size: null,
      shared_with: null,
      version: 1,
    };

    // Create a promise for document creation
    const createPromise = createDocument.mutate(newDocument);

    // Use toast.promise to handle loading, success, and error states
    toast.promise(createPromise, {
      loading: 'Creating document...',
      success: (createdDocument) => {
        // Navigate to the new document after successful creation
        if (createdDocument && 'id' in createdDocument) {
          router.push(`/${username}/documents/${createdDocument.id}`);
        }
        return isTemplate ? 'Template Created' : 'Document Created';
      },
      error: (err) => {
        console.error('Error creating document:', err);
        return 'Failed to create document';
      },
    });
  };

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push(`/${username}/documents`)}
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            {isTemplate ? 'Create New Template' : 'Create New Document'}
          </h1>
        </div>

        <Button onClick={handleSave}>
          <SaveIcon className="mr-2 h-4 w-4" />
          {isTemplate ? 'Create Template' : 'Create Document'}
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error.message}
        </div>
      )}

      <Tabs defaultValue="details">
        <TabsList>
          <TabsTrigger value="details">Document Details</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Document title"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea.Root
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Document description"
                >
                  <Textarea.CharCounter
                    current={description.length}
                    max={500}
                  />
                </Textarea.Root>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="grid gap-2">
                  <Label htmlFor="document-type">Document Type</Label>
                  <Select
                    value={documentType}
                    onValueChange={(value) =>
                      setDocumentType(value as DocumentType)
                    }
                  >
                    <SelectTrigger id="document-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="form">Form</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="letter">Letter</SelectItem>
                      <SelectItem value="agreement">Agreement</SelectItem>
                      <SelectItem value="report">Report</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={status}
                    onValueChange={(value) =>
                      setStatus(value as DocumentStatus)
                    }
                  >
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                      <SelectItem value="template">Template</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-4">
                <Checkbox
                  id="is-template"
                  checked={isTemplate}
                  onCheckedChange={(checked) => setIsTemplate(checked === true)}
                />
                <Label htmlFor="is-template">Save as Template</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Content</CardTitle>
            </CardHeader>
            <CardContent>
              <SectionBasedEditor
                initialSections={contentSections}
                onChange={handleSectionsChange}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end mt-8">
        <Button
          variant="outline"
          className="mr-3"
          onClick={() => router.push(`/${username}/documents`)}
        >
          Cancel
        </Button>
        <Button onClick={handleSave}>
          <SaveIcon className="mr-2 h-4 w-4" />
          {isTemplate ? 'Create Template' : 'Create Document'}
        </Button>
      </div>
    </div>
  );
}

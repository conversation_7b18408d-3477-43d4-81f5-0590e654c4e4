'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PaginationControl } from '@/components/ui/pagination-control';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDocuments } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/client';
import RealtimeService from '@/lib/supabase/realtime/realtime-service';
import { debounce } from '@/lib/utils/performance-optimizations';
import {
  ArrowDownIcon,
  ArrowLeft,
  ArrowUpIcon,
  FileText as FileTextIcon,
  Plus as PlusIcon,
  Search as SearchIcon,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { Template, TemplateCard } from './components/TemplateCard';
import { TemplateLoadingState } from './components/TemplateLoadingState';

// Template interface is now imported from TemplateCard component

export default function TemplatesPage() {
  const { createFromTemplate } = useDocuments();
  const { username } = useParams();
  const router = useRouter();

  // State for templates
  const [userTemplates, setUserTemplates] = useState<Template[]>([]);
  const [globalTemplates, setGlobalTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  // No longer need previewTemplateId state

  // Pagination state
  const [currentGlobalPage, setCurrentGlobalPage] = useState(1);
  const [currentUserPage, setCurrentUserPage] = useState(1);
  const [totalGlobalTemplates, setTotalGlobalTemplates] = useState(0);
  const [totalUserTemplates, setTotalUserTemplates] = useState(0);
  const itemsPerPage = 9; // 3x3 grid

  // Use the existing Supabase client
  const supabase = supabaseClient;

  // Fetch templates with debounce for search
  const fetchTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const { user } = userStore.getState();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Calculate pagination ranges
      const userStart = (currentUserPage - 1) * itemsPerPage;
      const globalStart = (currentGlobalPage - 1) * itemsPerPage;

      // Build query with filters - select only necessary fields for count
      let userQuery = supabase
        .from('documents')
        .select('id', { count: 'exact', head: true })
        .eq('owner_id', user.id)
        .eq('is_template', true);

      // Apply filters to count query
      if (searchQuery) {
        userQuery = userQuery.or(
          `title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`
        );
      }

      if (categoryFilter !== 'all') {
        userQuery = userQuery.eq('metadata->>category', categoryFilter);
      }

      if (typeFilter !== 'all') {
        userQuery = userQuery.eq('document_type', typeFilter);
      }

      // Execute count query
      const { count: userCount, error: userCountError } = await userQuery;

      if (userCountError) {
        throw new Error(
          `Error counting user templates: ${userCountError.message}`
        );
      }

      setTotalUserTemplates(userCount || 0);

      // Build global templates query with filters - select only necessary fields for count
      let globalQuery = supabase
        .from('templates')
        .select('id', { count: 'exact', head: true })
        .eq('is_global', true);

      // Apply filters to global count query
      if (searchQuery) {
        globalQuery = globalQuery.or(
          `title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`
        );
      }

      if (categoryFilter !== 'all') {
        globalQuery = globalQuery.eq('category', categoryFilter);
      }

      if (typeFilter !== 'all') {
        globalQuery = globalQuery.eq('document_type', typeFilter);
      }

      // Execute global count query
      const { count: globalCount, error: globalCountError } = await globalQuery;

      if (globalCountError) {
        throw new Error(
          `Error counting global templates: ${globalCountError.message}`
        );
      }

      setTotalGlobalTemplates(globalCount || 0);

      // Build user templates data query with filters and pagination - select only necessary fields
      let userDataQuery = supabase
        .from('documents')
        .select(
          'id, title, description, document_type, content, created_at, metadata'
        )
        .eq('owner_id', user.id)
        .eq('is_template', true);

      // Apply filters to data query
      if (searchQuery) {
        userDataQuery = userDataQuery.or(
          `title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`
        );
      }

      if (categoryFilter !== 'all') {
        userDataQuery = userDataQuery.eq('metadata->>category', categoryFilter);
      }

      if (typeFilter !== 'all') {
        userDataQuery = userDataQuery.eq('document_type', typeFilter);
      }

      // Apply sorting
      userDataQuery = userDataQuery.order('created_at', {
        ascending: sortOrder === 'asc',
      });

      // Apply pagination
      userDataQuery = userDataQuery.range(
        userStart,
        userStart + itemsPerPage - 1
      );

      // Execute data query
      const { data: userTemplatesData, error: userTemplatesError } =
        await userDataQuery;

      if (userTemplatesError) {
        throw new Error(
          `Error fetching user templates: ${userTemplatesError.message}`
        );
      }

      // Build global templates data query with filters and pagination - select only necessary fields
      let globalDataQuery = supabase
        .from('templates')
        .select(
          'id, title, description, document_type, content, created_at, is_global, created_by, category'
        )
        .eq('is_global', true);

      // Apply filters to global data query
      if (searchQuery) {
        globalDataQuery = globalDataQuery.or(
          `title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`
        );
      }

      if (categoryFilter !== 'all') {
        globalDataQuery = globalDataQuery.eq('category', categoryFilter);
      }

      if (typeFilter !== 'all') {
        globalDataQuery = globalDataQuery.eq('document_type', typeFilter);
      }

      // Apply sorting
      globalDataQuery = globalDataQuery.order('created_at', {
        ascending: sortOrder === 'asc',
      });

      // Apply pagination
      globalDataQuery = globalDataQuery.range(
        globalStart,
        globalStart + itemsPerPage - 1
      );

      // Execute global data query
      const { data: globalTemplatesData, error: globalTemplatesError } =
        await globalDataQuery;

      if (globalTemplatesError) {
        throw new Error(
          `Error fetching global templates: ${globalTemplatesError.message}`
        );
      }

      // Transform user templates to match Template interface
      const transformedUserTemplates = userTemplatesData.map((doc: any) => ({
        id: doc.id,
        title: doc.title,
        description: doc.description,
        document_type: doc.document_type,
        content: doc.content,
        created_at: doc.created_at,
        is_global: false,
        created_by: user.id,
        category: doc.metadata?.category || 'Uncategorized',
        // thumbnail_url is no longer used
      }));

      setUserTemplates(transformedUserTemplates);
      setGlobalTemplates(
        globalTemplatesData.map((template: any) => ({
          ...template,
          document_type: template.document_type as DocumentType,
        }))
      );
    } catch (err) {
      console.error('Error fetching templates:', err);
      setError(
        err instanceof Error ? err : new Error('Failed to fetch templates')
      );
    } finally {
      setLoading(false);
    }
  }, [
    currentGlobalPage,
    currentUserPage,
    searchQuery,
    categoryFilter,
    typeFilter,
    sortOrder,
  ]);

  // Create a debounced search handler
  const debouncedSearch = useMemo(
    () => debounce((value: string) => setSearchQuery(value), 300),
    []
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    debouncedSearch(e.target.value);
  };

  // Fetch templates when pagination or filters change
  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  // Set up realtime subscription for templates
  useEffect(() => {
    const { user } = userStore.getState();
    if (!user) return;

    // Subscribe to template changes
    const unsubscribe = RealtimeService.subscribeToTemplates(() => {
      // Refresh templates when any change occurs
      fetchTemplates();
    });

    return () => {
      unsubscribe();
    };
  }, [fetchTemplates]);

  // Handle template selection
  const handleUseTemplate = useCallback(
    async (template: Template) => {
      try {
        if (!template.id) {
          toast.error('Invalid template');
          return;
        }

        // For global templates, we need to create a document from the template content
        if (template.is_global) {
          // Use the RPC function directly for global templates
          const { user } = userStore.getState();
          if (!user) {
            toast.error('You must be logged in to use templates');
            return;
          }

          // Call the RPC function directly
          const { data: documentId, error: rpcError } = await supabase.rpc(
            'create_document_from_template',
            {
              template_id: template.id,
              user_id: user.id,
              title: `${template.title} - Copy`,
              description: template.description || '',
            }
          );

          if (rpcError) {
            console.error('Error creating document from template:', rpcError);
            throw new Error(`Failed to create document: ${rpcError.message}`);
          }

          if (documentId) {
            toast.success('Template copied to documents', {
              description: 'You can now edit your new document',
              action: {
                label: 'Edit',
                onClick: () =>
                  router.push(`/${username}/documents/${documentId}`),
              },
            });
          } else {
            throw new Error('Failed to create document from template');
          }
        } else {
          // For user templates, use the hook function
          const createdDocument = await createFromTemplate(template.id, {
            title: `${template.title} - Copy`,
            description: template.description || '',
          });

          if (createdDocument) {
            toast.success('Template copied to documents', {
              description: 'You can now edit your new document',
              action: {
                label: 'Edit',
                onClick: () =>
                  router.push(`/${username}/documents/${createdDocument.id}`),
              },
            });
          } else {
            throw new Error('Failed to create document from template');
          }
        }
      } catch (err) {
        console.error('Error using template:', err);
        toast.error('Failed to use template', {
          description:
            err instanceof Error ? err.message : 'Unknown error occurred',
        });
      }
    },
    [createFromTemplate, router, supabase, username]
  );

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentGlobalPage(1);
    setCurrentUserPage(1);
  }, [searchQuery, categoryFilter, typeFilter, sortOrder]);

  // With server-side filtering, we don't need to filter the templates again
  const filteredUserTemplates = userTemplates;
  const filteredGlobalTemplates = globalTemplates;

  // Calculate pagination
  const totalGlobalPages = Math.ceil(totalGlobalTemplates / itemsPerPage);
  const totalUserPages = Math.ceil(totalUserTemplates / itemsPerPage);

  // With server-side pagination, we're already getting the current page items
  const currentGlobalTemplates = filteredGlobalTemplates;
  const currentUserTemplates = filteredUserTemplates;

  // Fetch all categories for filter dropdown
  const [allCategories, setAllCategories] = useState<string[]>([]);
  const [categoriesLoaded, setCategoriesLoaded] = useState(false);

  // Fetch all categories on component mount - but only once
  useEffect(() => {
    // Skip if already loaded
    if (categoriesLoaded) return;

    const fetchCategories = async () => {
      try {
        // Get unique categories from global templates
        const { data: globalCategories, error: globalError } = await supabase
          .from('templates')
          .select('category')
          .eq('is_global', true);

        if (globalError) throw globalError;

        // Get unique categories from user templates
        const { user } = userStore.getState();
        if (!user) return;

        const { data: userCategories, error: userError } = await supabase
          .from('documents')
          .select('metadata')
          .eq('owner_id', user.id)
          .eq('is_template', true);

        if (userError) throw userError;

        // Combine and deduplicate categories
        const categories = [
          ...new Set([
            ...globalCategories.map((item) => item.category),
            ...userCategories.map((item) => {
              const metadata = item.metadata as Record<string, any>;
              return metadata?.category;
            }),
          ]),
        ].filter(Boolean) as string[];

        setAllCategories(categories);
        setCategoriesLoaded(true);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, [categoriesLoaded]);

  // TemplateCard component is now imported from a separate file

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push(`/${username}/documents`)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Templates</h1>
        </div>
        <Button
          onClick={() =>
            router.push(`/${username}/documents/new?template=true`)
          }
        >
          <PlusIcon className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error.message}
        </div>
      )}

      <div className="flex flex-col gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search templates..."
              className="pl-9"
              defaultValue={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
          <div className="flex gap-2">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {allCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Document Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="form">Form</SelectItem>
                <SelectItem value="contract">Contract</SelectItem>
                <SelectItem value="letter">Letter</SelectItem>
                <SelectItem value="agreement">Agreement</SelectItem>
                <SelectItem value="report">Report</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="icon"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              title={sortOrder === 'asc' ? 'Oldest first' : 'Newest first'}
            >
              {sortOrder === 'asc' ? (
                <ArrowUpIcon className="h-4 w-4" />
              ) : (
                <ArrowDownIcon className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="global" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="global">Global Templates</TabsTrigger>
            <TabsTrigger value="user">My Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="global">
            {error ? (
              <div className="p-6 bg-red-50 rounded-lg text-red-800">
                <p>Error loading templates: {error.message}</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => fetchTemplates()}
                >
                  Retry
                </Button>
              </div>
            ) : loading && !globalTemplates.length ? (
              <TemplateLoadingState />
            ) : filteredGlobalTemplates.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4">
                  {currentGlobalTemplates.map((template) => (
                    <TemplateCard
                      key={template.id}
                      template={template}
                      username={username as string}
                      onUseTemplate={handleUseTemplate}
                    />
                  ))}
                </div>
                {totalGlobalPages > 1 && (
                  <PaginationControl
                    currentPage={currentGlobalPage}
                    totalPages={totalGlobalPages}
                    onPageChange={setCurrentGlobalPage}
                    className="mt-6"
                  />
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <FileTextIcon className="mx-auto h-12 w-12 text-muted-foreground opacity-50 mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  No global templates found
                </h3>
                <p className="text-muted-foreground mb-6">
                  {searchQuery ||
                  categoryFilter !== 'all' ||
                  typeFilter !== 'all'
                    ? 'Try adjusting your filters'
                    : 'Global templates will appear here when they are available'}
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="user">
            {error ? (
              <div className="p-6 bg-red-50 rounded-lg text-red-800">
                <p>Error loading templates: {error.message}</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => fetchTemplates()}
                >
                  Retry
                </Button>
              </div>
            ) : loading && !userTemplates.length ? (
              <TemplateLoadingState />
            ) : filteredUserTemplates.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4">
                  {currentUserTemplates.map((template) => (
                    <TemplateCard
                      key={template.id}
                      template={template}
                      username={username as string}
                      onUseTemplate={handleUseTemplate}
                    />
                  ))}
                </div>
                {totalUserPages > 1 && (
                  <PaginationControl
                    currentPage={currentUserPage}
                    totalPages={totalUserPages}
                    onPageChange={setCurrentUserPage}
                    className="mt-6"
                  />
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <FileTextIcon className="mx-auto h-12 w-12 text-muted-foreground opacity-50 mb-4" />
                <h3 className="text-lg font-medium mb-2">No templates yet</h3>
                <p className="text-muted-foreground mb-6">
                  {searchQuery ||
                  categoryFilter !== 'all' ||
                  typeFilter !== 'all'
                    ? 'Try adjusting your filters'
                    : 'Create your first template to get started'}
                </p>
                <Button
                  onClick={() =>
                    router.push(`/${username}/documents/new?template=true`)
                  }
                >
                  <PlusIcon className="mr-2 h-4 w-4" />
                  Create Template
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
      {/* Template preview is now a separate page */}
    </div>
  );
}

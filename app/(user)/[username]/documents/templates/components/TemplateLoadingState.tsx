'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from '@/components/ui/card';
import { DocCard } from '@/components/ux/comp/doc-card';
import { Skeleton } from '@/components/ui/skeleton';
import React from 'react';

export const TemplateLoadingState = React.memo(() => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4">
      {Array.from({ length: 6 }).map((_, i) => (
        <DocCard key={i} className="mb-4">
          <div className="h-full flex flex-col">
            <CardHeader className="pb-2 pt-6 px-6">
              <div className="flex justify-between items-start">
                <div>
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>
            </CardHeader>
            <CardContent className="flex-grow px-6 py-4">
              <Skeleton className="aspect-video w-full rounded-md mb-4 shadow-sm" />
              <div className="flex gap-2 mt-3">
                <Skeleton className="h-6 w-20 rounded-full" />
                <Skeleton className="h-6 w-24 rounded-full" />
              </div>
            </CardContent>
            <CardFooter className="pt-4 pb-6 px-6 flex justify-between border-t mt-auto">
              <Skeleton className="h-4 w-24" />
              <div className="flex gap-2">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-24" />
              </div>
            </CardFooter>
          </div>
        </DocCard>
      ))}
    </div>
  );
});

TemplateLoadingState.displayName = 'TemplateLoadingState';

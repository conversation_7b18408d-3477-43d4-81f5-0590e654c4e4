# Technical Context

## Technologies

### Frontend

- Next.js 15 with App Router
- React 19 with Server Components
- TypeScript for type safety
- Bun for package management

### UI

- Tailwind CSS for styling
- shadcn/ui for base components
- Custom components
- Motion/React for animations
- Sonner for toast notifications

### State Management

- Zustand for global state
  - User store for authentication state
  - Profile store for user profile data
  - Document store for document state
- React Context for component trees
- Form state persistence with Zod validation

### Data Access

- Supabase for database and authentication
- Centralized hooks in use-supabase.ts
- Type-safe database access with generated types
- Realtime subscriptions for collaborative features
- Row-level security policies for data protection

### Document Management

- Section-based document editor
- Document preview with content rendering
- Document sharing with permissions
- Document version history
- Document activities tracking
- Document comments and annotations
- Document templates with customization
- Document export to PDF, DOCX, HTML

### Authentication

- Supabase Auth for authentication
- JWT-based session management
- Role-based access control
- User profiles with avatars
- User settings management
- Security features:
  - Password reset
  - Email verification
  - Session management

## Components

### Sidebar

- Animated transitions between content
- Direction-aware animations based on navigation flow
- Blur effects for smoother transitions
- Overflow handling to prevent scrollbar appearance
- Layout composition with header, content, and footer sections

### Dropdowns

- Enhanced with blur and transform animations
- AnimatePresence for coordinated exit animations
- Maintains dropdown content accessibility
- Smooth reveal and hide animations
- Consistent animation timing across UI

### Toasts

- Using Sonner for all toast notifications
- Custom styled through shadcn/ui
- Consistent positioning at bottom-right
- Support for different types:
  - Success: Green, short duration (2s)
  - Error: Red, longer duration (5s)
  - Warning: Yellow, medium duration (3s)
  - Info: Blue, medium duration (3s)
- Action buttons for interactive notifications
- Automatic cleanup and queueing

### Forms

- Dynamic form generation
- Real-time validation
- Auto-save functionality
- Progress tracking
- Section navigation
- Preview generation

## Development

### Packages

- Bun as package manager
- NPM scripts for common tasks
- Automated dependency updates

### Tools

- VS Code as primary IDE
- ESLint for code quality
- Prettier for formatting
- TypeScript for type checking

### Testing

- Jest for unit tests
- React Testing Library
- Playwright for E2E tests
- MSW for API mocking

## Constraints

### Browser

- Modern browsers only
- Progressive enhancement
- Offline capabilities
- Mobile-first design

### Performance

- First contentful paint < 1s
- Time to interactive < 2s
- Lighthouse score > 90
- Bundle size < 200KB
- Animation performance:
  - 60fps target for all animations
  - Limit animations on low-power devices
  - Use will-change property sparingly
  - Optimize for composite layers

### Security

- CSRF protection
- XSS prevention
- Input sanitization
- Secure data storage

## Dependencies & Integrations

### Core

- next: ^15.3.2
- react: ^19.1.0
- typescript: ^5.8.3
- tailwindcss: ^4.1.7
- zustand: ^5.0.4
- zod: ^3.24.4
- sonner: ^2.0.3
- @supabase/ssr: ^0.6.1
- @supabase/auth-ui-react: ^0.4.7
- @radix-ui/\* components for UI
- date-fns: ^4.1.0
- react-hook-form: ^7.56.4

### Document Generation

- @react-pdf/renderer: ^4.3.0
- docx: ^9.5.0
- jspdf: ^3.0.1
- html2canvas: ^1.4.1

### Rich Text Editing

- @tiptap/react: ^2.12.0
- @tiptap/starter-kit: ^2.12.0
- @tiptap/extension-heading: ^2.12.0
- @tiptap/extension-link: ^2.12.0
- @tiptap/extension-placeholder: ^2.12.0
- dompurify: ^3.2.5

### Data Access

- Supabase client encapsulated in use-supabase.ts hook
- Supabase types defined in `lib/types/database-modules.ts`, extending from `lib/supabase/database-types.ts`
- `lib/supabase/database-types.ts` is immutable; all type extensions and modifications occur within `lib/types/database-modules.ts`

### Development

- eslint: ^9.27.0
- prettier: ^3.5.3
- bun for package management
- TypeScript for type safety

### API & Services

- Supabase API for database operations
- Document service for CRUD operations
- Authentication service for user management
- Template service for document templates
- Lawyer service for consultation management
- Notification service for user notifications
- Avatar service for profile images
- Export service for document generation

### Error Handling & Feedback

- Toast notifications for user feedback
- Error boundaries for component errors
- Form validation with Zod
- Optimistic UI updates with fallbacks
- Loading states for async operations

## Deployment & Infrastructure

### Environment

- Vercel for frontend hosting
- Supabase for backend services
- PostgreSQL database with RLS policies
- Edge functions for serverless operations
- CDN for static assets

### Monitoring & Analytics

- Error tracking with client-side logging
- Performance monitoring
- Usage analytics for feature adoption
- Health checks for service availability
- Database monitoring

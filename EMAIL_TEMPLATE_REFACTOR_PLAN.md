# Email Template Refactor Plan

## Overview
Based on the analysis of your current email templates and the new email assets in `public/assets/imgs/emails/`, this plan will standardize all email templates to match the improved design pattern from `waitlist-welcome-email.tsx` while creating reusable components.

## Current State Analysis

### Email Assets Available
- `email-header.png` - General header (currently used in waitlist welcome)
- `email-header-auth.png` - For authentication emails (verification, password reset)
- `email-header-notifications.png` - For notification emails (document notifications)
- `email-header-waitlists.png` - For waitlist-related emails

### Current Templates Status
1. **waitlist-welcome-email.tsx** ✅ - Already updated with modern design
2. **verification-email.tsx** ❌ - Needs complete redesign
3. **reset-password-email.tsx** ❌ - Needs complete redesign  
4. **document-notification-email.tsx** ❌ - Needs complete redesign
5. **waitlist-admin-notification-email.tsx** ❌ - Needs complete redesign

## Phase 1: Create Reusable Components

### 1.1 Extract Email Footer Component
**File:** `lib/emails/components/email-footer.tsx`

**Features:**
- Standardized footer with support contact
- Copyright notice
- **Resend unsubscribe link integration**
- Consistent styling matching waitlist-welcome design

**Props Interface:**
```typescript
interface EmailFooterProps {
  unsubscribeUrl?: string; // Resend unsubscribe URL
  showUnsubscribe?: boolean;
}
```

### 1.2 Create Email Header Component
**File:** `lib/emails/components/email-header.tsx`

**Features:**
- Dynamic header image based on email type
- Consistent header styling
- Proper image sizing and alt text

**Props Interface:**
```typescript
interface EmailHeaderProps {
  type: 'general' | 'auth' | 'notifications' | 'waitlist';
  alt?: string;
}
```

### 1.3 Create Shared Styles Module
**File:** `lib/emails/styles/shared-styles.ts`

**Features:**
- Export all common styles from waitlist-welcome-email
- Consistent color scheme (#0da2a6 accent, #f5f5f5 background)
- Standardized button styles with proper shadows
- Typography and spacing constants

## Phase 2: Update Individual Email Templates

### 2.1 Update verification-email.tsx
**Changes:**
- Replace header with `EmailHeader` component using 'auth' type
- Apply waitlist-welcome styling (main, container, text styles)
- Update button to match waitlist-welcome button design
- Replace footer with `EmailFooter` component
- Use shared styles from shared-styles.ts

### 2.2 Update reset-password-email.tsx  
**Changes:**
- Replace header with `EmailHeader` component using 'auth' type
- Apply waitlist-welcome styling
- Update button styling to match standard design
- Replace footer with `EmailFooter` component
- Use shared styles

### 2.3 Update document-notification-email.tsx
**Changes:**
- Replace header with `EmailHeader` component using 'notifications' type
- Apply waitlist-welcome styling and layout
- Update button styling to match standard design
- Replace footer with `EmailFooter` component
- Maintain existing dynamic content logic
- Use shared styles

### 2.4 Update waitlist-admin-notification-email.tsx
**Changes:**
- Replace header with `EmailHeader` component using 'waitlist' type
- Apply waitlist-welcome base styling
- Update primary/secondary buttons to match standard design
- Replace footer with `EmailFooter` component
- Maintain existing admin-specific features
- Use shared styles

### 2.5 Update waitlist-welcome-email.tsx
**Changes:**
- Replace inline footer with `EmailFooter` component
- Replace inline header with `EmailHeader` component
- Import styles from shared-styles.ts
- Add unsubscribe functionality

## Phase 3: Resend Unsubscribe Integration

### 3.1 Research Resend Unsubscribe API
- Investigate Resend's unsubscribe link generation
- Determine if automatic or manual unsubscribe URL generation
- Check if audience-specific unsubscribe links are needed

### 3.2 Update Email Services
**File:** `lib/services/emailService.ts`

**Changes:**
- Add unsubscribe URL generation for each email type
- Pass unsubscribe URLs to email templates
- Ensure proper audience management

### 3.3 Update Email Template Props
- Add optional `unsubscribeUrl` prop to all email template interfaces
- Pass through to `EmailFooter` component

## Phase 4: Testing & Validation

### 4.1 Email Template Testing
- Test each email template in development
- Verify proper image loading from new assets
- Confirm unsubscribe links work correctly
- Validate responsive design

### 4.2 Style Consistency Check
- Ensure all emails match waitlist-welcome design
- Verify button styling consistency
- Check color scheme adherence
- Validate typography consistency

## Implementation Order

1. **Phase 1.3** - Create shared-styles.ts (foundation)
2. **Phase 1.1** - Create EmailFooter component
3. **Phase 1.2** - Create EmailHeader component  
4. **Phase 2.1** - Update verification-email.tsx
5. **Phase 2.2** - Update reset-password-email.tsx
6. **Phase 2.3** - Update document-notification-email.tsx
7. **Phase 2.4** - Update waitlist-admin-notification-email.tsx
8. **Phase 2.5** - Update waitlist-welcome-email.tsx
9. **Phase 3** - Implement Resend unsubscribe integration
10. **Phase 4** - Testing and validation

## Key Design Standards (from waitlist-welcome-email.tsx)

### Colors
- Background: `#f5f5f5` (neutral-50)
- Container: `#ffffff` with shadow
- Primary text: `#374151` (gray-700)
- Accent: `#0da2a6` (teal-500)
- Footer text: `#6b7280` (gray-500)

### Button Style
```css
backgroundColor: '#0da2a6'
borderRadius: '9999px' (fully rounded)
boxShadow: '#0891a3 0px -2.4px 0px 0px inset, rgba(143, 39, 9, 0.2) 0px 1px 3px 0px, #0891a3 0px 0px 0px 1px'
```

### Typography
- Font: Geist Sans fallback stack
- Body text: 16px, line-height 24px
- Centered text alignment for most content

### Layout
- Max width: 600px
- Container padding: 40px 20px
- Border radius: 8px
- Header margin-bottom: 32px

## Files to Create/Modify

### New Files
- `lib/emails/components/email-footer.tsx`
- `lib/emails/components/email-header.tsx`
- `lib/emails/styles/shared-styles.ts`

### Modified Files
- `lib/emails/templates/verification-email.tsx`
- `lib/emails/templates/reset-password-email.tsx`
- `lib/emails/templates/document-notification-email.tsx`
- `lib/emails/templates/waitlist-admin-notification-email.tsx`
- `lib/emails/templates/waitlist-welcome-email.tsx`
- `lib/services/emailService.ts`

## Success Criteria
- ✅ All emails use consistent design language
- ✅ Proper header images for each email type
- ✅ Standardized button styling across all templates
- ✅ Reusable footer with unsubscribe functionality
- ✅ Clean, maintainable code structure
- ✅ Proper TypeScript interfaces
- ✅ Working unsubscribe links via Resend

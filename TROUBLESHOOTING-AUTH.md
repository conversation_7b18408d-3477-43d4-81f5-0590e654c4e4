# Authentication Error Troubleshooting Guide

## Problem: "Database error saving new user"

This error occurs when Supabase Auth cannot create a new user due to database issues, typically related to profile creation triggers or RLS policies.

## Step-by-Step Diagnosis

### 1. Run the Diagnostic Script

Copy and run the content of `scripts/diagnose-auth-issue.sql` in your Supabase SQL Editor. This will check:
- Trigger function existence
- Trigger status
- Table structure
- Enum types
- RLS policies
- Constraints

### 2. Apply the Fixed Migration

Run the improved migration: `lib/supabase/db/migrations/20250621000001_fix_auth_trigger_issues.sql`

This migration:
- Recreates the trigger function with proper enum casting
- Fixes RLS policies
- Adds a service role policy for trigger operations
- Includes a test function

### 3. Common Issues and Solutions

#### Issue: Enum Type Mismatch
**Symptoms**: Error mentions enum types or invalid values
**Solution**: Ensure the trigger uses proper enum casting:
```sql
'user'::user_role
'Free'::plan_new
```

#### Issue: RLS Policy Blocking Trigger
**Symptoms**: Permission denied errors during profile creation
**Solution**: Add service role policy:
```sql
CREATE POLICY "Service role can manage all profiles"
  ON public.profiles
  FOR ALL
  USING (auth.role() = 'service_role');
```

#### Issue: Missing Columns
**Symptoms**: Column does not exist errors
**Solution**: Check if your profiles table has all required columns:
- id (UUID, primary key)
- username (TEXT)
- full_name (TEXT)
- email (TEXT)
- user_role (user_role enum)
- plan (plan_new enum)
- is_onboarded (BOOLEAN)
- updated_at (TIMESTAMP)

#### Issue: Foreign Key Constraints
**Symptoms**: Foreign key constraint violation
**Solution**: Ensure the user ID exists in auth.users before profile creation

### 4. Manual Testing

Test profile creation manually:
```sql
-- Test if you can create a profile manually
INSERT INTO public.profiles (
  id,
  username,
  full_name,
  email,
  user_role,
  plan,
  is_onboarded,
  updated_at
) VALUES (
  gen_random_uuid(),
  'testuser',
  'Test User',
  '<EMAIL>',
  'user'::user_role,
  'Free'::plan_new,
  false,
  NOW()
);
```

### 5. Check Trigger Function

Verify the trigger function exists and works:
```sql
-- Check if function exists
SELECT proname FROM pg_proc WHERE proname = 'handle_new_user';

-- Check if trigger exists
SELECT tgname FROM pg_trigger WHERE tgname = 'on_auth_user_created';

-- Test the function
SELECT public.test_profile_creation();
```

### 6. Monitor Database Logs

In Supabase Dashboard:
1. Go to Logs
2. Filter by "Database"
3. Look for errors during user creation
4. Check for specific constraint violations or permission errors

### 7. Alternative Solutions

If the trigger approach doesn't work, you can:

#### Option A: Manual Profile Creation in Auth Callback
Handle profile creation in `app/auth/callback/route.ts` (already implemented as fallback)

#### Option B: Client-Side Profile Creation
Handle profile creation in the create-account form (already implemented as fallback)

#### Option C: Disable Email Confirmation Temporarily
In Supabase Dashboard > Authentication > Settings:
- Disable "Enable email confirmations"
- Test user creation
- Re-enable after fixing the issue

### 8. Verification Steps

After applying fixes:

1. **Test user registration** through the application
2. **Check database** for new profiles
3. **Verify trigger logs** for any warnings
4. **Test with different email formats**
5. **Check RLS policies** work correctly

### 9. Emergency Rollback

If issues persist:
```sql
-- Disable the trigger temporarily
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- The application will fall back to manual profile creation
```

### 10. Contact Support

If all else fails, provide this information:
- Database diagnostic results
- Error logs from browser console
- Supabase project logs
- Steps to reproduce the issue

## Prevention

To prevent future issues:
1. Always test migrations on staging first
2. Monitor user registration success rates
3. Set up alerts for authentication errors
4. Keep database schema documentation updated
5. Regular backup of database schema and policies

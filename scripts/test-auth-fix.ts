/**
 * Test script to verify the authentication fix
 * This script tests the auth service functions and database operations
 */

import { authService } from '../lib/supabase/auth/auth-service';
import { supabaseClient } from '../lib/supabase/client';

async function testAuthFix() {
  console.log('🧪 Testing Authentication Fix...\n');

  try {
    // Test 1: Username availability check
    console.log('1. Testing username availability check...');
    const testUsername = 'testuser123';
    const isAvailable = await authService.checkUsernameAvailability(testUsername);
    console.log(`   Username "${testUsername}" available: ${isAvailable}`);

    // Test 2: Username generation
    console.log('\n2. Testing username generation...');
    const generatedUsername = await authService.generateUniqueUsername('Test User');
    console.log(`   Generated username: "${generatedUsername}"`);

    // Test 3: Check if profiles table exists and has proper structure
    console.log('\n3. Testing profiles table structure...');
    const { data: profiles, error: profilesError } = await supabaseClient
      .from('profiles')
      .select('id, username, full_name, email, user_role, plan')
      .limit(1);

    if (profilesError) {
      console.error('   ❌ Profiles table error:', profilesError.message);
    } else {
      console.log('   ✅ Profiles table accessible');
      console.log(`   Found ${profiles?.length || 0} profile(s)`);
    }

    // Test 4: Check if trigger function exists
    console.log('\n4. Testing trigger function existence...');
    const { data: functions, error: functionsError } = await supabaseClient
      .rpc('pg_get_functiondef', { funcid: 'public.handle_new_user'::regproc })
      .single();

    if (functionsError) {
      console.log('   ⚠️  Trigger function not found - migration may not be applied yet');
      console.log('   Run the migration: 20250621000000_add_profile_creation_trigger.sql');
    } else {
      console.log('   ✅ Trigger function exists');
    }

    // Test 5: Validate auth service error handling
    console.log('\n5. Testing auth service error handling...');
    try {
      // This should fail gracefully
      await authService.checkUsernameAvailability('');
      console.log('   ✅ Empty username handled gracefully');
    } catch (error) {
      console.log('   ✅ Error handling working:', (error as Error).message);
    }

    console.log('\n🎉 Authentication fix tests completed!');
    console.log('\nNext steps:');
    console.log('1. Apply the database migration if not already done');
    console.log('2. Test user registration in the browser');
    console.log('3. Monitor for any remaining authentication errors');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\nTroubleshooting:');
    console.log('1. Check Supabase connection');
    console.log('2. Verify environment variables');
    console.log('3. Ensure database is accessible');
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testAuthFix();
}

export { testAuthFix };

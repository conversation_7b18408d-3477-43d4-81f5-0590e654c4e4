-- Diagnostic script to identify authentication issues
-- Run this in Supabase SQL Editor to diagnose the problem

-- 1. Check if the trigger function exists
SELECT 
  'Trigger Function Status' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'handle_new_user') 
    THEN 'EXISTS' 
    ELSE 'MISSING' 
  END as status;

-- 2. Check if the trigger exists and is active
SELECT 
  'Trigger Status' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created') 
    THEN 'EXISTS' 
    ELSE 'MISSING' 
  END as status;

-- 3. Check profiles table structure
SELECT 
  'Profiles Table Columns' as check_type,
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 4. Check enum types
SELECT 
  'Enum Types' as check_type,
  t.typname as enum_name,
  e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid 
WHERE t.typname IN ('user_role', 'plan_new')
ORDER BY t.typname, e.enumsortorder;

-- 5. Check RLS status
SELECT 
  'RLS Status' as check_type,
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'profiles';

-- 6. Check RLS policies
SELECT 
  'RLS Policies' as check_type,
  policyname,
  cmd,
  permissive,
  roles
FROM pg_policies 
WHERE tablename = 'profiles';

-- 7. Check for any constraints that might be failing
SELECT 
  'Table Constraints' as check_type,
  constraint_name,
  constraint_type,
  column_name
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu 
  ON tc.constraint_name = ccu.constraint_name
WHERE tc.table_name = 'profiles'
  AND tc.table_schema = 'public';

-- 8. Check foreign key relationships
SELECT 
  'Foreign Keys' as check_type,
  tc.constraint_name,
  tc.table_name,
  kcu.column_name,
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_name = 'profiles';

-- 9. Test profile creation manually (this will help identify the exact error)
-- Note: This is a test query - don't run in production
/*
DO $$
DECLARE
  test_user_id UUID := gen_random_uuid();
  test_email TEXT := '<EMAIL>';
BEGIN
  -- Try to insert a test profile to see what fails
  INSERT INTO public.profiles (
    id,
    username,
    full_name,
    email,
    avatar_url,
    is_onboarded,
    user_role,
    plan,
    updated_at
  ) VALUES (
    test_user_id,
    'testuser',
    'Test User',
    test_email,
    null,
    false,
    'user',
    'Free',
    NOW()
  );
  
  RAISE NOTICE 'Test profile creation: SUCCESS';
  
  -- Clean up
  DELETE FROM public.profiles WHERE id = test_user_id;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Test profile creation FAILED: % - %', SQLSTATE, SQLERRM;
END $$;
*/

-- 10. Check auth.users table structure (to understand what data is available)
SELECT 
  'Auth Users Columns' as check_type,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_schema = 'auth' 
  AND table_name = 'users'
ORDER BY ordinal_position;

-- 11. Check if there are any existing profiles that might give us clues
SELECT 
  'Existing Profiles Sample' as check_type,
  COUNT(*) as total_profiles,
  COUNT(CASE WHEN username IS NULL THEN 1 END) as null_usernames,
  COUNT(CASE WHEN user_role IS NULL THEN 1 END) as null_user_roles,
  COUNT(CASE WHEN plan IS NULL THEN 1 END) as null_plans
FROM public.profiles;

-- 12. Check database version and extensions
SELECT 
  'Database Info' as check_type,
  version() as postgresql_version;

SELECT 
  'Extensions' as check_type,
  extname as extension_name,
  extversion as version
FROM pg_extension
WHERE extname IN ('uuid-ossp', 'pgcrypto');

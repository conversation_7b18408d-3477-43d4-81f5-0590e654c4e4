import { triggerNotificationUpdate } from '@/lib/notification-events';
import { notificationService } from '@/lib/services/NotificationService';
import { userStore } from '@/lib/store/user';

/**
 * Helper function to create a notification when a comment is added to a document
 */
export async function notifyCommentAdded(
  documentOwnerId: string,
  documentId: string,
  documentName: string,
  commentContent: string
): Promise<void> {
  try {
    const { user } = userStore.getState();
    if (!user) return;

    // Don't notify if the commenter is the document owner
    if (user.id === documentOwnerId) return;

    // Create the notification directly using the notification service
    await notificationService.commentAdded(
      documentOwnerId,
      documentId,
      documentName,
      user.email || 'A user',
      commentContent
    );

    // Trigger a notification update event to refresh the UI
    triggerNotificationUpdate();

    console.log(`Comment notification created for ${documentName}`);
  } catch (error) {
    console.error('Error creating comment notification:', error);
  }
}

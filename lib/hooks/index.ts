/**
 * Central export file for all hooks in the application
 *
 * This file re-exports all hooks from use-supabase.ts and other utility hooks.
 * Always import hooks from this file rather than directly from their source files
 * to ensure consistency and make future refactoring easier.
 */

// Export all hooks from use-supabase.ts
export * from './use-supabase';

// Export other utility hooks
export { useIsMobile } from './use-mobile';
export { usePermissions } from './usePermissions';
export { default as useScroll } from './useScroll';
export { loadTemplateFromServer, useTemplateLoader } from './useTemplateLoader';
export { useClickOutside } from './use-click-outside';
export { useCurrencies } from './use-currencies';

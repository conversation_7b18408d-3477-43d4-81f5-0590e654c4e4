import { Database } from '@/lib/supabase/database-types';
import { useEffect, useState } from 'react';
import { userStore } from '../store/user';
import { getSupabaseClient } from '../supabase/auth/client';

type Permission = Database['public']['Enums']['permission'];

export function usePermissions() {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = userStore();
  const supabase = getSupabaseClient();

  useEffect(() => {
    async function loadPermissions() {
      if (!user) {
        setPermissions([]);
        setLoading(false);
        return;
      }

      try {
        // Get user's role
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (!profile?.user_role) {
          setPermissions([]);
          setLoading(false);
          return;
        }

        // Get role's permissions
        const { data: role } = await supabase
          .from('roles')
          .select('permissions')
          .eq('name', profile.user_role)
          .single();

        if (role?.permissions) {
          // Type assertion since we know the permissions in the database match our Permission type
          setPermissions(role.permissions as Permission[]);
        } else {
          setPermissions([]);
        }
      } catch (error) {
        console.error('Error loading permissions:', error);
        setPermissions([]);
      } finally {
        setLoading(false);
      }
    }

    loadPermissions();
  }, [user]);

  const hasPermission = (permission: Permission) => {
    return permissions.includes(permission);
  };

  return {
    permissions,
    hasPermission,
    loading,
  };
}

# Hooks

This directory contains React hooks used throughout the application.

## Usage

Always import hooks from the index file:

```tsx
import { useDocuments, useLawyers, useSettings } from '@/lib/hooks';
```

Do NOT import hooks directly from their source files:

```tsx
// ❌ Don't do this
import { useDocuments } from '@/lib/hooks';
```

## Available Hooks

### Document Hooks

- `useDocuments`: Operations for documents (create, read, update, delete, share)

### Lawyer Hooks

- `useLawyers`: Operations for lawyers (profiles, consultations, reviews)
- `useDirectLawyers`: Simplified lawyer interactions for direct usage
- `useLawyerAvailability`: Manage lawyer availability and time slots

### Messaging Hooks

- `useLawyerMessages`: Operations for lawyer-client messaging

### Organization Hooks

- `useOrganizations`: Operations for organizations
- `useTeams`: Operations for teams within organizations

### Calendar Hooks

- `useCalendarIntegration`: Operations for calendar integration

### Settings Hooks

- `useSettings`: Operations for user settings

### Notification Hooks

- `useNotifications`: Operations for notifications
- `usePushNotifications`: Operations for push notifications

### Client Notes Hooks

- `useClientNotes`: Operations for client notes

### Utility Hooks

- `useSupabaseMutation`: Simplified interface for Supabase mutations
- `useIsMobile`: Detect mobile devices
- `usePermissions`: Check user permissions
- `useScroll`: Handle scroll events
- `useTemplateLoader`: Load document templates

## Implementation Details

All main hooks are implemented in `use-supabase.ts` and exported through `index.ts`. This centralized approach:

1. Prevents duplicate functionality
2. Makes it easier to maintain and update hooks
3. Provides a single source of truth for data access patterns

Other hook files in this directory are utility hooks with specific functionality that complement the main hooks.

## Adding New Hooks

When adding new hooks:

1. Add the hook implementation to `use-supabase.ts`
2. Ensure it's exported through `export *` in `index.ts`
3. Document the hook in this README

## Migrating Existing Code

If you find code importing hooks directly from non-existent files (e.g., `@/lib/hooks/use-documents`), update it to import from `@/lib/hooks` instead.

Example:

```tsx
// Before
import { useDocuments } from '@/lib/hooks';

// After
import { useDocuments } from '@/lib/hooks';
```

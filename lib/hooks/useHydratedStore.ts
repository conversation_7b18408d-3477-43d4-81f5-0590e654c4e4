'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to safely use Zustand stores with persistence to prevent hydration mismatches
 * @param store - The Zustand store hook
 * @param selector - Optional selector function to select specific state
 * @returns The store state after hydration is complete, or initial state during SSR
 */
export function useHydratedStore<T, U = T>(
  store: () => T,
  selector?: (state: T) => U
): U | undefined {
  const [isHydrated, setIsHydrated] = useState(false);
  const storeState = store();

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Return undefined during SSR and initial hydration to prevent mismatch
  if (!isHydrated) {
    return undefined;
  }

  // Return selected state or full state after hydration
  return selector ? selector(storeState) : (storeState as unknown as U);
}

/**
 * Hook specifically for user store to prevent hydration mismatches
 */
export function useHydratedUserStore() {
  return useHydratedStore(() => {
    // Dynamic import to prevent issues during SSR
    const { userStore } = require('@/lib/store/user');
    return userStore();
  });
}

import { useEffect, useState } from 'react';

// Variables

// Type
import { CurrencyType } from '@/types';
import { CURRENCIES_API } from '../constants';

const useCurrencies = () => {
  const [currencies, setCurrencies] = useState<CurrencyType[]>([]);
  const [currenciesLoading, setCurrenciesLoading] = useState<boolean>(false);

  /**
   * Fetches all the currencies asynchronously.
   *
   * @return {Promise<void>} Promise that resolves when the currencies are fetched.
   */
  const fetchCurrencies = async (): Promise<void> => {
    setCurrenciesLoading(true);

    try {
      const response = await fetch(`${CURRENCIES_API}`);
      const data = await response.json();

      const currencyOptions = Object.keys(data).map((currencyCode) => {
        const currencyName = data[currencyCode];
        return { code: currencyCode, name: currencyName };
      });

      setCurrencies(currencyOptions);
    } catch (err) {
      console.log(err);
    } finally {
      setCurrenciesLoading(false);
    }
  };

  useEffect(() => {
    fetchCurrencies();
  }, []);

  return { currencies, currenciesLoading };
};

export { useCurrencies };

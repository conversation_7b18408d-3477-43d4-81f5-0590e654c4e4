import { useCallback, useEffect, useState } from 'react';

export default function useScroll(threshold: number) {
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);

  const onScroll = useCallback(() => {
    if (typeof window !== 'undefined') {
      setScrolled(window.scrollY > threshold);
    }
  }, [threshold]);

  useEffect(() => {
    setMounted(true);

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', onScroll);
      // Only call onScroll after component is mounted to prevent hydration mismatch
      onScroll();
      return () => window.removeEventListener('scroll', onScroll);
    }
  }, [onScroll]);

  // Return false during SSR and initial hydration to prevent mismatch
  return mounted ? scrolled : false;
}

'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { supabaseClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { userStore } from '../store/user';

type AuthProviderProps = {
  children: React.ReactNode;
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  const supabase = supabaseClient;

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth error:', error);
          // Import the clearAllStores function dynamically to avoid circular dependencies
          import('@/lib/store/clear-stores').then(({ clearAllStores }) => {
            // Clear all Zustand stores when there's an auth error
            clearAllStores();
            setIsAuthenticated(false);
            setIsLoading(false);
          });
          return;
        }

        if (!session) {
          console.log('No session found');
          // Import the clearAllStores function dynamically to avoid circular dependencies
          import('@/lib/store/clear-stores').then(({ clearAllStores }) => {
            // Clear all Zustand stores when no session is found
            clearAllStores();
            setIsAuthenticated(false);
            setIsLoading(false);
          });
          return;
        }

        // Update user store with the authenticated user
        const { user } = session;
        userStore.getState().updateUser(user);

        // Get user profile with better error handling
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) {
          console.error('Profile error:', profileError);
          if (profileError.code === 'PGRST116') {
            console.log(
              'No profile found for user, this might be expected for new users'
            );
          }
        } else if (profile) {
          userStore.getState().updateProfile(profile);
        }

        setIsAuthenticated(true);
      } catch (err) {
        console.error('Error checking auth:', err);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();

    // Set up auth state change listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);

      if (event === 'SIGNED_IN' && session) {
        userStore.getState().updateUser(session.user);

        // Get user profile with error handling
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (profileError) {
          console.error(
            'Profile error during auth state change:',
            profileError
          );
          if (profileError.code === 'PGRST116') {
            console.log('No profile found during auth state change');
          }
        } else if (profile) {
          userStore.getState().updateProfile(profile);
        }

        setIsAuthenticated(true);
      } else if (event === 'SIGNED_OUT') {
        // Import the clearAllStores function dynamically to avoid circular dependencies
        import('@/lib/store/clear-stores').then(({ clearAllStores }) => {
          // Clear all Zustand stores when signing out
          clearAllStores();
          setIsAuthenticated(false);
          // Let the middleware handle the routing
          // The middleware will redirect to login for protected routes
          // and to not-found for routes that are neither protected nor unprotected
        });
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase, router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="space-y-4 w-full max-w-md">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    );
  }

  // If not authenticated, let the middleware handle the routing
  // The middleware will redirect to login for protected routes
  // and to not-found for routes that are neither protected nor unprotected
  if (!isAuthenticated) {
    return null;
  }

  return <>{children}</>;
};

export default AuthProvider;

'use client';

import { LegalTemplate, Section } from '@/lib/constants/schemas/template';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { AutoSaveMiddleware } from '../middleware/autoSaveMiddleware';
import { FormPersistenceService } from '../services/FormPersistenceService';

interface FormState {
  formData: Record<string, unknown>;
  currentSection: number;
  isComplete: boolean;
  isDirty: boolean;
  lastSaved: Date | null;
  needsSync: boolean;
}

export type { FormState };

interface FormStateContextType {
  // State
  formState: FormState;
  template: LegalTemplate;
  currentSection: Section;
  progress: number;

  // Actions
  setFormData: (data: Record<string, unknown>) => void;
  nextSection: () => void;
  previousSection: () => void;
  goToSection: (index: number) => void;
  saveProgress: () => Promise<void>;
  retryLastSave: () => Promise<void>;

  // Status
  isSaving: boolean;
  canGoNext: boolean;
  canGoPrevious: boolean;
  isCurrentSectionValid: boolean;
  lastError: Error | null;
}

const FormStateContext = createContext<FormStateContextType | undefined>(
  undefined
);

const AUTO_SAVE_DELAY = 2000; // 2 seconds

export function FormStateProvider({
  children,
  template,
  initialData = {},
}: {
  children: React.ReactNode;
  template: LegalTemplate;
  initialData?: Record<string, unknown>;
}) {
  // State
  const [formState, setFormState] = useState<FormState>({
    formData: initialData,
    currentSection: 0,
    isComplete: false,
    isDirty: false,
    lastSaved: null,
    needsSync: false,
  });
  const [isSaving, setIsSaving] = useState(false);
  const [lastError, setLastError] = useState<Error | null>(null);
  const persistenceService = useRef(new FormPersistenceService());
  const autoSaveMiddleware = useRef(
    AutoSaveMiddleware.getInstance({
      onSaveStart: () => setIsSaving(true),
      onSaveSuccess: () => {
        setIsSaving(false);
        setFormState((prev) => ({
          ...prev,
          isDirty: false,
          lastSaved: new Date(),
          needsSync: false,
        }));
      },
      onSaveError: (error: Error) => {
        setIsSaving(false);
        setLastError(error);
      },
    })
  );
  const lastSaveAttempt = useRef<FormState | null>(null);

  // Load saved state
  useEffect(() => {
    const loadSavedState = async () => {
      try {
        const savedState = await persistenceService.current.loadState(
          template.metadata.id
        );
        if (savedState) {
          setFormState((prev) => ({
            ...prev,
            formData: savedState.formData,
            currentSection: savedState.currentSection,
            lastSaved: new Date(savedState.lastSaved!),
            needsSync: savedState.needsSync,
          }));
        }
      } catch (error) {
        console.error('Error loading saved form state:', error);
        setLastError(error as Error);
      }
    };

    loadSavedState();
  }, [template.metadata.id]);

  // Cleanup
  useEffect(() => {
    return () => {
      persistenceService.current.destroy();
      autoSaveMiddleware.current.destroy();
    };
  }, []);

  // Auto-save effect
  useEffect(() => {
    if (!formState.isDirty) return;
    autoSaveMiddleware.current.scheduleAutoSave(
      template.metadata.id,
      formState
    );
  }, [formState, template.metadata.id]);

  // Computed values
  const currentSection = useMemo(
    () => template.sections[formState.currentSection],
    [template.sections, formState.currentSection]
  );

  const progress = useMemo(
    () => ((formState.currentSection + 1) / template.sections.length) * 100,
    [formState.currentSection, template.sections.length]
  );

  const canGoNext = useMemo(
    () => formState.currentSection < template.sections.length - 1,
    [formState.currentSection, template.sections.length]
  );

  const canGoPrevious = useMemo(
    () => formState.currentSection > 0,
    [formState.currentSection]
  );

  const isCurrentSectionValid = useMemo(() => {
    const sectionVariables = currentSection.variables || [];
    const requiredFields = sectionVariables.filter((v) => v.isRequired);

    return requiredFields.every((field) => {
      const value = formState.formData[field.name];
      return value !== undefined && value !== null && value !== '';
    });
  }, [currentSection, formState.formData]);

  // Actions
  const setFormData = useCallback((data: Record<string, unknown>) => {
    setFormState((prev) => ({
      ...prev,
      formData: { ...prev.formData, ...data },
      isDirty: true,
      needsSync: true,
    }));
  }, []);

  const nextSection = useCallback(() => {
    if (canGoNext && isCurrentSectionValid) {
      setFormState((prev) => ({
        ...prev,
        currentSection: prev.currentSection + 1,
      }));
    }
  }, [canGoNext, isCurrentSectionValid]);

  const previousSection = useCallback(() => {
    if (canGoPrevious) {
      setFormState((prev) => ({
        ...prev,
        currentSection: prev.currentSection - 1,
      }));
    }
  }, [canGoPrevious]);

  const goToSection = useCallback(
    (index: number) => {
      if (index >= 0 && index < template.sections.length) {
        setFormState((prev) => ({
          ...prev,
          currentSection: index,
        }));
      }
    },
    [template.sections.length]
  );

  const saveProgress = async () => {
    setIsSaving(true);
    setLastError(null);
    lastSaveAttempt.current = formState;

    try {
      await persistenceService.current.saveState(
        template.metadata.id,
        formState
      );
      setFormState((prev) => ({
        ...prev,
        isDirty: false,
        lastSaved: new Date(),
      }));
    } catch (error) {
      console.error('Error saving form state:', error);
      setLastError(error as Error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  const retryLastSave = async () => {
    if (!lastSaveAttempt.current) return;

    setIsSaving(true);
    setLastError(null);

    try {
      await persistenceService.current.saveState(
        template.metadata.id,
        lastSaveAttempt.current
      );
      setFormState((prev) => ({
        ...prev,
        isDirty: false,
        lastSaved: new Date(),
      }));
    } catch (error) {
      console.error('Error retrying save:', error);
      setLastError(error as Error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  const value = {
    formState,
    template,
    currentSection,
    progress,
    setFormData,
    nextSection,
    previousSection,
    goToSection,
    saveProgress,
    retryLastSave,
    isSaving,
    canGoNext,
    canGoPrevious,
    isCurrentSectionValid,
    lastError,
  };

  return (
    <FormStateContext.Provider value={value}>
      {children}
    </FormStateContext.Provider>
  );
}

export function useFormState() {
  const context = useContext(FormStateContext);
  if (context === undefined) {
    throw new Error('useFormState must be used within a FormStateProvider');
  }
  return context;
}

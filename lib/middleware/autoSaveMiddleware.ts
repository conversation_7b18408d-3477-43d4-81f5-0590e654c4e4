import { FormState } from '@/lib/contexts/FormStateContext';
import { FormPersistenceService } from '@/lib/services/FormPersistenceService';

export interface AutoSaveOptions {
  delay?: number;
  onSaveStart?: () => void;
  onSaveSuccess?: () => void;
  onSaveError?: (error: Error) => void;
}

export class AutoSaveMiddleware {
  private static instance: AutoSaveMiddleware;
  private persistenceService: FormPersistenceService;
  private timers: Map<string, NodeJS.Timeout>;
  private options: Required<AutoSaveOptions>;

  private constructor(options: AutoSaveOptions = {}) {
    this.persistenceService = new FormPersistenceService();
    this.timers = new Map();
    this.options = {
      delay: options.delay ?? 2000,
      onSaveStart: options.onSaveStart ?? (() => {}),
      onSaveSuccess: options.onSaveSuccess ?? (() => {}),
      onSaveError: options.onSaveError ?? console.error,
    };
  }

  static getInstance(options?: AutoSaveOptions): AutoSaveMiddleware {
    if (!AutoSaveMiddleware.instance) {
      AutoSaveMiddleware.instance = new AutoSaveMiddleware(options);
    }
    return AutoSaveMiddleware.instance;
  }

  scheduleAutoSave(formId: string, state: FormState): void {
    // Clear any existing timer for this form
    this.clearTimer(formId);

    // Only schedule save if the state is dirty
    if (!state.isDirty) return;

    // Set new timer
    const timer = setTimeout(async () => {
      try {
        this.options.onSaveStart();
        await this.persistenceService.saveState(formId, state);
        this.options.onSaveSuccess();
      } catch (error) {
        this.options.onSaveError(error as Error);
      } finally {
        this.timers.delete(formId);
      }
    }, this.options.delay);

    this.timers.set(formId, timer);
  }

  clearTimer(formId: string): void {
    const existingTimer = this.timers.get(formId);
    if (existingTimer) {
      clearTimeout(existingTimer);
      this.timers.delete(formId);
    }
  }

  clearAllTimers(): void {
    this.timers.forEach((timer) => clearTimeout(timer));
    this.timers.clear();
  }

  destroy(): void {
    this.clearAllTimers();
    this.persistenceService.destroy();
  }
}

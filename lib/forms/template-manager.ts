import { promises as fs } from 'fs';
import path from 'path';
import {
  LegalTemplate,
  LegalTemplateSchema,
  Section,
} from '../constants/schemas/template';

export class TemplateManager {
  private templatesDir: string;

  constructor() {
    this.templatesDir = path.join(process.cwd(), 'lib', 'forms', 'templates');
  }

  async loadTemplate(
    jurisdiction: string,
    category: string,
    type: string
  ): Promise<LegalTemplate> {
    try {
      const templatePath = path.join(
        this.templatesDir,
        jurisdiction.toLowerCase(),
        category.toLowerCase(),
        `${type.toLowerCase()}.json`
      );

      const content = await fs.readFile(templatePath, 'utf-8');
      const template = JSON.parse(content);

      return LegalTemplateSchema.parse(template);
    } catch (error) {
      throw new Error(
        `Failed to load template: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async listTemplates(filters?: {
    jurisdiction?: string;
    category?: string;
  }): Promise<
    Array<{
      jurisdiction: string;
      category: string;
      type: string;
      metadata: LegalTemplate['metadata'];
    }>
  > {
    const templates: Array<{
      jurisdiction: string;
      category: string;
      type: string;
      metadata: LegalTemplate['metadata'];
    }> = [];

    try {
      const jurisdictions = filters?.jurisdiction
        ? [filters.jurisdiction]
        : await fs.readdir(this.templatesDir);

      for (const jurisdiction of jurisdictions) {
        const jurisdictionPath = path.join(this.templatesDir, jurisdiction);
        const categories = filters?.category
          ? [filters.category]
          : await fs.readdir(jurisdictionPath);

        for (const category of categories) {
          const categoryPath = path.join(jurisdictionPath, category);
          const files = await fs.readdir(categoryPath);

          for (const file of files) {
            if (file.endsWith('.json')) {
              const templatePath = path.join(categoryPath, file);
              const content = await fs.readFile(templatePath, 'utf-8');
              const template = JSON.parse(content) as LegalTemplate;

              templates.push({
                jurisdiction,
                category,
                type: file.replace('.json', ''),
                metadata: template.metadata,
              });
            }
          }
        }
      }

      return templates;
    } catch (error) {
      console.error('Error listing templates:', error);
      return [];
    }
  }

  async validateTemplate(template: unknown): Promise<boolean> {
    try {
      LegalTemplateSchema.parse(template);
      return true;
    } catch (error) {
      console.error('Template validation failed:', error);
      return false;
    }
  }

  async getTemplatePath(
    jurisdiction: string,
    category: string,
    type: string
  ): Promise<string> {
    return path.join(
      this.templatesDir,
      jurisdiction.toLowerCase(),
      category.toLowerCase(),
      `${type.toLowerCase()}.json`
    );
  }

  async validateVariables(
    template: LegalTemplate,
    variables: Record<string, unknown>
  ): Promise<{
    isValid: boolean;
    errors: Array<{ field: string; message: string }>;
  }> {
    const errors: Array<{ field: string; message: string }> = [];

    // Collect all variables from all sections
    const requiredVariables = template.sections.flatMap((section) =>
      section.variables.filter((v) => v.isRequired).map((v) => v.name)
    );

    // Check for missing required variables
    for (const varName of requiredVariables) {
      if (!(varName in variables)) {
        errors.push({
          field: varName,
          message: `Missing required variable: ${varName}`,
        });
      }
    }

    // Validate variable types and rules
    for (const section of template.sections) {
      for (const variable of section.variables) {
        const value = variables[variable.name];

        if (value !== undefined) {
          // Type validation
          switch (variable.type) {
            case 'date':
              if (!(value instanceof Date) && !Date.parse(String(value))) {
                errors.push({
                  field: variable.name,
                  message: `Invalid date format for ${variable.name}`,
                });
              }
              break;
            case 'number':
              if (typeof value !== 'number' || isNaN(value)) {
                errors.push({
                  field: variable.name,
                  message: `Invalid number format for ${variable.name}`,
                });
              }
              break;
          }

          // Custom validation rules
          if (variable.validationRules) {
            for (const rule of variable.validationRules) {
              switch (rule.type) {
                case 'min':
                  if (
                    typeof value === 'number' &&
                    typeof rule.value === 'number' &&
                    value < rule.value
                  ) {
                    errors.push({
                      field: variable.name,
                      message: rule.message,
                    });
                  }
                  break;
                // Add more validation rule types as needed
              }
            }
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private async loadBaseTemplate(
    templateId: string
  ): Promise<LegalTemplate | null> {
    try {
      const templates = await this.listTemplates();
      for (const template of templates) {
        const fullTemplate = await this.loadTemplate(
          template.jurisdiction,
          template.category,
          template.type
        );
        if (fullTemplate.metadata.id === templateId) {
          return fullTemplate;
        }
      }
      return null;
    } catch (error) {
      console.error('Error loading base template:', error);
      return null;
    }
  }

  private async resolveInheritance(
    template: LegalTemplate
  ): Promise<LegalTemplate> {
    // If no base template, return as is
    if (!template.metadata.baseTemplate) {
      return template;
    }

    // Load base template
    const baseTemplate = await this.loadBaseTemplate(
      template.metadata.baseTemplate
    );
    if (!baseTemplate) {
      throw new Error(
        `Base template ${template.metadata.baseTemplate} not found`
      );
    }

    // Merge sections, giving priority to the current template
    const mergedSections: Section[] = [];
    const sectionMap = new Map<string, Section>();

    // Add base template sections first
    for (const section of baseTemplate.sections) {
      sectionMap.set(section.id, { ...section });
    }

    // Override with current template sections
    for (const section of template.sections) {
      if (section.inheritsFrom) {
        // Inherit from base section
        const baseSection = sectionMap.get(section.inheritsFrom);
        if (baseSection) {
          sectionMap.set(section.id, {
            ...baseSection,
            ...section,
            variables: [...baseSection.variables, ...section.variables],
          });
        } else {
          sectionMap.set(section.id, section);
        }
      } else {
        sectionMap.set(section.id, section);
      }
    }

    // Convert map back to array and sort by order
    mergedSections.push(...Array.from(sectionMap.values()));
    mergedSections.sort((a, b) => a.order - b.order);

    return {
      ...template,
      sections: mergedSections,
    };
  }

  private evaluateCondition(
    condition: Section['condition'],
    variables: Record<string, unknown>
  ): boolean {
    if (!condition) {
      return true;
    }

    const value = variables[condition.if];
    if (typeof condition.equals === 'number') {
      return typeof value === 'number' && value === condition.equals;
    }
    return value === condition.equals;
  }

  async generateDocument(
    template: LegalTemplate,
    variables: Record<string, unknown>
  ): Promise<string> {
    // Validate variables first
    const validation = await this.validateVariables(template, variables);
    if (!validation.isValid) {
      throw new Error(
        `Variable validation failed: ${validation.errors
          .map((e) => e.message)
          .join(', ')}`
      );
    }

    // Resolve template inheritance
    const resolvedTemplate = await this.resolveInheritance(template);
    let document = '';

    // Generate document by replacing variables in each section
    for (const section of resolvedTemplate.sections) {
      // Skip section if condition is not met
      if (!this.evaluateCondition(section.condition, variables)) {
        continue;
      }

      let sectionContent = section.content;

      // Replace all variables in the section content
      for (const variable of section.variables) {
        const value = variables[variable.name];
        if (value !== undefined) {
          const regex = new RegExp(`{{${variable.name}}}`, 'g');
          sectionContent = sectionContent.replace(regex, String(value));
        }
      }

      document += `\n\n${section.title}\n${sectionContent}`;
    }

    return document.trim();
  }

  async getTemplateVersion(
    jurisdiction: string,
    category: string,
    type: string,
    version?: string
  ): Promise<LegalTemplate> {
    const template = await this.loadTemplate(jurisdiction, category, type);

    if (!version) {
      return template;
    }

    const targetVersion = template.versions.find((v) => v.version === version);
    if (!targetVersion) {
      throw new Error(`Version ${version} not found for template`);
    }

    return template;
  }

  async compareVersions(
    template: LegalTemplate,
    version1: string,
    version2: string
  ): Promise<{
    addedSections: string[];
    removedSections: string[];
    modifiedSections: string[];
  }> {
    const v1 = template.versions.find((v) => v.version === version1);
    const v2 = template.versions.find((v) => v.version === version2);

    if (!v1 || !v2) {
      throw new Error('One or both versions not found');
    }

    const changes: {
      addedSections: string[];
      removedSections: string[];
      modifiedSections: string[];
    } = {
      addedSections: [],
      removedSections: [],
      modifiedSections: [],
    };

    // Combine changes from all versions between v1 and v2
    const versions = template.versions
      .filter((v) => {
        const vNum = parseFloat(v.version);
        const v1Num = parseFloat(version1);
        const v2Num = parseFloat(version2);
        return vNum > Math.min(v1Num, v2Num) && vNum <= Math.max(v1Num, v2Num);
      })
      .sort((a, b) => parseFloat(a.version) - parseFloat(b.version));

    for (const version of versions) {
      for (const change of version.changes) {
        if (change.type === 'added') {
          changes.addedSections.push(change.description);
        } else if (change.type === 'removed') {
          changes.removedSections.push(change.description);
        } else if (change.type === 'modified') {
          changes.modifiedSections.push(change.description);
        }
      }
    }

    return changes;
  }
}

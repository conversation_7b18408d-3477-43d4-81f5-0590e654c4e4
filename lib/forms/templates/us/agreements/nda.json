{"metadata": {"id": "550e8400-e29b-41d4-a716-446655440000", "version": "1.0.0", "name": "Standard Non-Disclosure Agreement", "description": "A comprehensive non-disclosure agreement template for protecting confidential information between parties", "category": "agreement", "jurisdiction": "us", "language": "en", "tags": ["nda", "confidentiality", "business", "legal"], "lastUpdated": "2024-03-21T00:00:00.000Z", "isPublished": true}, "sections": [{"id": "parties", "title": "Parties", "content": "This Non-Disclosure Agreement (\"Agreement\") is entered into between {{disclosing_party_name}}, located at {{disclosing_party_address}} (\"Disclosing Party\") and {{receiving_party_name}}, located at {{receiving_party_address}} (\"Receiving Party\") on {{agreement_date}}.", "isRequired": true, "order": 1, "variables": [{"name": "disclosing_party_name", "type": "text", "description": "Legal name of the party disclosing confidential information", "isRequired": true}, {"name": "disclosing_party_address", "type": "text", "description": "Complete address of the disclosing party", "isRequired": true}, {"name": "receiving_party_name", "type": "text", "description": "Legal name of the party receiving confidential information", "isRequired": true}, {"name": "receiving_party_address", "type": "text", "description": "Complete address of the receiving party", "isRequired": true}, {"name": "agreement_date", "type": "date", "description": "Date when the agreement is signed", "isRequired": true}]}, {"id": "confidential_info", "title": "Definition of Confidential Information", "content": "\"Confidential Information\" means any and all non-public information, including but not limited to {{confidential_info_types}}, disclosed by the Disclosing Party to the Receiving Party, whether in written, oral, electronic, or other form.", "isRequired": true, "order": 2, "variables": [{"name": "confidential_info_types", "type": "text", "description": "Types of confidential information covered", "isRequired": true}]}, {"id": "obligations", "title": "Obligations of Receiving Party", "content": "The Receiving Party agrees to:\n1. Maintain the confidentiality of all Confidential Information\n2. Use Confidential Information solely for {{permitted_use}}\n3. Not disclose Confidential Information to any third party without prior written consent\n4. Protect Confidential Information with at least the same degree of care used to protect its own confidential information", "isRequired": true, "order": 3, "variables": [{"name": "permitted_use", "type": "text", "description": "Permitted use of the confidential information", "isRequired": true}]}, {"id": "term", "title": "Term and Termination", "content": "This Agreement shall remain in effect for a period of {{duration}} from the date of execution. The obligations regarding confidentiality shall survive for {{confidentiality_period}} after termination.", "isRequired": true, "order": 4, "variables": [{"name": "duration", "type": "text", "description": "Duration of the agreement", "isRequired": true}, {"name": "confidentiality_period", "type": "text", "description": "Period for which confidentiality obligations survive", "isRequired": true}]}, {"id": "return_of_info", "title": "Return of Confidential Information", "content": "Upon termination or at the Disclosing Party's request, the Receiving Party shall {{return_method}} all Confidential Information.", "isRequired": true, "order": 5, "variables": [{"name": "return_method", "type": "text", "description": "Method of returning or destroying confidential information", "isRequired": true}]}], "legalRequirements": ["Must be signed by authorized representatives of both parties", "Must clearly define what constitutes confidential information", "Must specify the duration of confidentiality obligations", "Must include provisions for the return or destruction of confidential information"], "formattingGuidelines": ["Use clear paragraph breaks between sections", "Number all major sections", "Use consistent font and spacing throughout", "Include page numbers on multi-page agreements"], "validationRules": [{"field": "agreement_date", "rule": "must be current or future date", "message": "Agreement date cannot be in the past"}, {"field": "duration", "rule": "must specify time period", "message": "Duration must include a specific time period (e.g., '2 years')"}], "versions": [{"id": "550e8400-e29b-41d4-a716-************", "version": "1.0.0", "changes": [{"type": "added", "description": "Initial template creation", "date": "2024-03-21T00:00:00.000Z"}], "createdAt": "2024-03-21T00:00:00.000Z", "createdBy": "550e8400-e29b-41d4-a716-************"}]}
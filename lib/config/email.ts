/**
 * Email Service Configuration
 * 
 * Centralized configuration for email operations including rate limiting,
 * retry logic, and service settings
 */

export interface EmailRateLimitConfig {
  /** Delay between sequential email operations (ms) */
  operationDelay: number;
  /** Maximum number of retry attempts for failed operations */
  maxRetries: number;
  /** Base delay for exponential backoff (ms) */
  retryBaseDelay: number;
  /** Maximum delay for exponential backoff (ms) */
  retryMaxDelay: number;
  /** Multiplier for exponential backoff */
  retryBackoffMultiplier: number;
}

export interface EmailServiceConfig {
  /** Rate limiting configuration */
  rateLimit: EmailRateLimitConfig;
  /** Email sender configuration */
  sender: {
    from: string;
    replyTo: string;
    adminEmail: string;
  };
  /** Resend API configuration */
  resend: {
    audienceId: string;
    domain: string;
  };
  /** Logging configuration */
  logging: {
    enabled: boolean;
    logRequests: boolean;
    logResponses: boolean;
    logErrors: boolean;
  };
}

/**
 * Default email service configuration
 */
export const EMAIL_CONFIG: EmailServiceConfig = {
  rateLimit: {
    // Conservative 600ms delay to stay well under Resend's 2 req/sec limit
    operationDelay: 600,
    maxRetries: 3,
    retryBaseDelay: 1000,
    retryMaxDelay: 10000,
    retryBackoffMultiplier: 2,
  },
  sender: {
    from: 'Notamess Forms <<EMAIL>>',
    replyTo: '<EMAIL>',
    adminEmail: '<EMAIL>',
  },
  resend: {
    audienceId: '157072ce-1d7d-47ed-47ed-9cec-b29d2c4d81d5',
    domain: 'forms.notamess.com',
  },
  logging: {
    enabled: true,
    logRequests: true,
    logResponses: true,
    logErrors: true,
  },
};

/**
 * Environment-specific configuration overrides
 */
export function getEmailConfig(): EmailServiceConfig {
  const config = { ...EMAIL_CONFIG };
  
  // Override with environment variables if available
  if (process.env.EMAIL_RATE_LIMIT_DELAY) {
    config.rateLimit.operationDelay = parseInt(process.env.EMAIL_RATE_LIMIT_DELAY);
  }
  
  if (process.env.EMAIL_MAX_RETRIES) {
    config.rateLimit.maxRetries = parseInt(process.env.EMAIL_MAX_RETRIES);
  }
  
  if (process.env.ADMIN_EMAIL) {
    config.sender.adminEmail = process.env.ADMIN_EMAIL;
  }
  
  if (process.env.RESEND_AUDIENCE_ID) {
    config.resend.audienceId = process.env.RESEND_AUDIENCE_ID;
  }
  
  // Disable logging in production if specified
  if (process.env.NODE_ENV === 'production' && process.env.DISABLE_EMAIL_LOGGING === 'true') {
    config.logging.enabled = false;
    config.logging.logRequests = false;
    config.logging.logResponses = false;
  }
  
  return config;
}

/**
 * Validate email configuration
 */
export function validateEmailConfig(config: EmailServiceConfig): void {
  const errors: string[] = [];
  
  // Validate rate limiting
  if (config.rateLimit.operationDelay < 100) {
    errors.push('Operation delay should be at least 100ms');
  }
  
  if (config.rateLimit.maxRetries < 0 || config.rateLimit.maxRetries > 10) {
    errors.push('Max retries should be between 0 and 10');
  }
  
  // Validate sender configuration
  if (!config.sender.from.includes('@')) {
    errors.push('Invalid sender email format');
  }
  
  if (!config.sender.adminEmail.includes('@')) {
    errors.push('Invalid admin email format');
  }
  
  // Validate Resend configuration
  if (!config.resend.audienceId) {
    errors.push('Resend audience ID is required');
  }
  
  if (!config.resend.domain) {
    errors.push('Resend domain is required');
  }
  
  if (errors.length > 0) {
    throw new Error(`Email configuration validation failed: ${errors.join(', ')}`);
  }
}

/**
 * Get rate limit configuration for specific operations
 */
export function getRateLimitConfig(operationType: 'email' | 'contact' | 'bulk'): EmailRateLimitConfig {
  const baseConfig = getEmailConfig().rateLimit;
  
  switch (operationType) {
    case 'email':
      return baseConfig;
    
    case 'contact':
      // Slightly more conservative for contact operations
      return {
        ...baseConfig,
        operationDelay: Math.max(baseConfig.operationDelay, 700),
      };
    
    case 'bulk':
      // More conservative for bulk operations
      return {
        ...baseConfig,
        operationDelay: Math.max(baseConfig.operationDelay, 1000),
        maxRetries: Math.min(baseConfig.maxRetries, 2),
      };
    
    default:
      return baseConfig;
  }
}

/**
 * Log configuration for debugging
 */
export function logEmailConfig(): void {
  const config = getEmailConfig();
  
  if (config.logging.enabled) {
    console.log('[EMAIL-CONFIG] Current configuration:', {
      rateLimit: {
        operationDelay: config.rateLimit.operationDelay,
        maxRetries: config.rateLimit.maxRetries,
      },
      sender: {
        from: config.sender.from,
        adminEmail: config.sender.adminEmail.replace(/(.{3}).*(@.*)/, '$1***$2'), // Mask email for security
      },
      resend: {
        audienceId: config.resend.audienceId.substring(0, 8) + '***', // Mask ID for security
        domain: config.resend.domain,
      },
      logging: config.logging,
    });
  }
}

/**
 * Constants for common email operations
 */
export const EMAIL_OPERATION_TYPES = {
  SEND_WELCOME: 'send-welcome-email',
  SEND_ADMIN_NOTIFICATION: 'send-admin-notification',
  ADD_TO_AUDIENCE: 'add-to-audience',
  SEND_VERIFICATION: 'send-verification-email',
  SEND_PASSWORD_RESET: 'send-password-reset',
  SEND_DOCUMENT_NOTIFICATION: 'send-document-notification',
} as const;

export type EmailOperationType = typeof EMAIL_OPERATION_TYPES[keyof typeof EMAIL_OPERATION_TYPES];

/**
 * Get operation-specific configuration
 */
export function getOperationConfig(operation: EmailOperationType): {
  delay: number;
  retries: number;
  priority: 'high' | 'medium' | 'low';
} {
  const config = getEmailConfig();
  
  switch (operation) {
    case EMAIL_OPERATION_TYPES.SEND_VERIFICATION:
    case EMAIL_OPERATION_TYPES.SEND_PASSWORD_RESET:
      return {
        delay: config.rateLimit.operationDelay,
        retries: config.rateLimit.maxRetries,
        priority: 'high',
      };
    
    case EMAIL_OPERATION_TYPES.SEND_WELCOME:
    case EMAIL_OPERATION_TYPES.ADD_TO_AUDIENCE:
      return {
        delay: config.rateLimit.operationDelay,
        retries: config.rateLimit.maxRetries,
        priority: 'medium',
      };
    
    case EMAIL_OPERATION_TYPES.SEND_ADMIN_NOTIFICATION:
    case EMAIL_OPERATION_TYPES.SEND_DOCUMENT_NOTIFICATION:
      return {
        delay: config.rateLimit.operationDelay + 100, // Slightly longer delay for admin emails
        retries: Math.max(config.rateLimit.maxRetries - 1, 1),
        priority: 'low',
      };
    
    default:
      return {
        delay: config.rateLimit.operationDelay,
        retries: config.rateLimit.maxRetries,
        priority: 'medium',
      };
  }
}

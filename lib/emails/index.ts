import { Resend } from 'resend';

// Initialize Resend with API key
const resendApiKey = process.env.RESEND_API_KEY;

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Validate API key in server environment
if (!isBrowser && !resendApiKey) {
  console.error('RESEND_API_KEY environment variable is not set');
}

// Create a conditional initialization to avoid errors in browser
export const resend = isBrowser
  ? new Resend('placeholder_for_browser') // This won't be used in browser
  : new Resend(resendApiKey || 'missing_api_key');

// Email sender configuration
export const emailConfig = {
  from: 'Notamess Forms <<EMAIL>>',
  replyTo: '<EMAIL>',
};

// Email template types
export type EmailTemplateProps =
  | VerificationEmailProps
  | ResetPasswordEmailProps
  | DocumentNotificationEmailProps
  | WaitlistWelcomeEmailProps
  | WaitlistAdminNotificationEmailProps;

// Verification email props
export interface VerificationEmailProps {
  type: 'verification';
  email: string;
  verificationLink: string;
  userName?: string;
}

// Reset password email props
export interface ResetPasswordEmailProps {
  type: 'resetPassword';
  email: string;
  resetLink: string;
  userName?: string;
}

// Document notification email props
export interface DocumentNotificationEmailProps {
  type: 'documentNotification';
  email: string;
  documentLink: string;
  userName?: string;
  documentName: string;
  notificationType: 'shared' | 'updated' | 'commented' | 'signed';
  senderName: string;
  message?: string;
}

export interface WaitlistWelcomeEmailProps {
  type: 'waitlistWelcome';
  email: string;
  userName: string;
  userEmail: string;
  role: 'user' | 'lawyer';
  waitlistPosition?: number;
}

export interface WaitlistAdminNotificationEmailProps {
  type: 'waitlistAdminNotification';
  email: string;
  userName: string;
  userEmail: string;
  role: 'user' | 'lawyer';
  totalWaitlistCount: number;
  userCount: number;
  lawyerCount: number;
  signupTime: string;
}

// Send email function
export async function sendEmail(props: EmailTemplateProps) {
  try {
    if (props.type === 'verification') {
      return await sendVerificationEmail(props);
    } else if (props.type === 'resetPassword') {
      return await sendResetPasswordEmail(props);
    } else if (props.type === 'documentNotification') {
      return await sendDocumentNotificationEmail(props);
    } else if (props.type === 'waitlistWelcome') {
      return await sendWaitlistWelcomeEmail(props);
    } else if (props.type === 'waitlistAdminNotification') {
      return await sendWaitlistAdminNotificationEmail(props);
    }
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

// Send verification email
async function sendVerificationEmail(props: VerificationEmailProps) {
  const { email, verificationLink, userName } = props;

  // Import the React component directly
  const { VerificationEmail } = await import('./templates/verification-email');

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject: 'Verify your email address',
    react: VerificationEmail({
      verificationLink,
      userName: userName || email.split('@')[0],
      unsubscribeUrl: undefined, // Verification emails don't need unsubscribe
    }),
  });
}

// Send reset password email
async function sendResetPasswordEmail(props: ResetPasswordEmailProps) {
  const { email, resetLink, userName } = props;

  // Import the React component directly
  const { ResetPasswordEmail } = await import(
    './templates/reset-password-email'
  );

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject: 'Reset your password',
    react: ResetPasswordEmail({
      resetLink,
      userName: userName || email.split('@')[0],
      unsubscribeUrl: undefined, // Password reset emails don't need unsubscribe
    }),
  });
}

// Send document notification email
async function sendDocumentNotificationEmail(
  props: DocumentNotificationEmailProps
) {
  const {
    email,
    documentLink,
    userName,
    documentName,
    notificationType,
    senderName,
    message,
  } = props;

  // Generate subject based on notification type
  let subject = '';
  switch (notificationType) {
    case 'shared':
      subject = `${senderName} shared a document with you`;
      break;
    case 'updated':
      subject = `${senderName} updated "${documentName}"`;
      break;
    case 'commented':
      subject = `${senderName} commented on "${documentName}"`;
      break;
    case 'signed':
      subject = `${senderName} signed "${documentName}"`;
      break;
    default:
      subject = `Notification about "${documentName}"`;
  }

  // Import the React component directly
  const { DocumentNotificationEmail } = await import(
    './templates/document-notification-email'
  );

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject: subject,
    react: DocumentNotificationEmail({
      documentLink,
      userName: userName || email.split('@')[0],
      documentName,
      notificationType,
      senderName,
      message,
      unsubscribeUrl: undefined, // TODO: Add unsubscribe URL generation here
    }),
  });
}
// Send waitlist welcome email
async function sendWaitlistWelcomeEmail(props: WaitlistWelcomeEmailProps) {
  const { email, userName, userEmail, role, waitlistPosition } = props;

  // Import the React component directly
  const { WaitlistWelcomeEmail } = await import(
    './templates/waitlist-welcome-email'
  );

  let subject = 'Welcome to the Notamess Waitlist!';
  if (typeof waitlistPosition === 'number') {
    subject += ` You're #${waitlistPosition} in line`;
  }

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject,
    react: WaitlistWelcomeEmail({
      userName,
      userEmail,
      role,
      waitlistPosition,
      unsubscribeUrl: undefined, // TODO: Add unsubscribe URL generation here
    }),
  });
}
// Send waitlist admin notification email
async function sendWaitlistAdminNotificationEmail(
  props: WaitlistAdminNotificationEmailProps
) {
  const {
    email,
    userName,
    userEmail,
    role,
    totalWaitlistCount,
    userCount,
    lawyerCount,
    signupTime,
  } = props;

  // Import the React component directly
  const { WaitlistAdminNotificationEmail } = await import(
    './templates/waitlist-admin-notification-email'
  );

  const subject = `New ${role === 'lawyer' ? 'Lawyer' : 'User'} Joined the Waitlist`;

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject,
    react: WaitlistAdminNotificationEmail({
      userName,
      userEmail,
      role,
      totalWaitlistCount,
      userCount,
      lawyerCount,
      signupTime,
      unsubscribeUrl: undefined, // Admin emails don't need unsubscribe
    }),
  });
}

import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';
import {
  button,
  buttonContainer,
  container,
  footer,
  h1,
  link,
  main,
  text,
} from '../styles/shared-styles';

interface ResetPasswordEmailProps {
  resetLink: string;
  userName: string;
  unsubscribeUrl?: string;
}

export const ResetPasswordEmail = ({
  resetLink,
  userName,
  unsubscribeUrl,
}: ResetPasswordEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Reset your Notamess Forms password</Preview>
      <Body style={main}>
        <Container style={container}>
          <EmailHeader type="auth" alt="Notamess Forms - Password Reset" />

          <Heading style={h1}>Reset Your Password</Heading>
          <Text style={text}>Hi {userName},</Text>
          <Text style={text}>
            We received a request to reset your password for your Notamess Forms
            account. Click the button below to set a new password:
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={resetLink}>
              Reset Password
            </Button>
          </Section>

          <Text style={text}>
            If you didn't request a password reset, you can safely ignore this
            email. Your password will not be changed.
          </Text>
          <Text style={text}>
            This password reset link will expire in 1 hour for security reasons.
          </Text>

          <Text style={footer}>
            If the button above doesn't work, paste this link into your browser:
          </Text>
          <Text style={footer}>
            <Link href={resetLink} style={link}>
              {resetLink}
            </Link>
          </Text>

          <EmailFooter
            unsubscribeUrl={unsubscribeUrl}
            showUnsubscribe={false} // Don't show unsubscribe for password reset emails
            showSupportContact={false} // Footer already has fallback link
          />
        </Container>
      </Body>
    </Html>
  );
};

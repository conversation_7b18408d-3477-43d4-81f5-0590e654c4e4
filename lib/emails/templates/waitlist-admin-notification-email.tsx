import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';
import {
  button,
  buttonContainer,
  container,
  h1,
  h2,
  h3,
  link,
  main,
  secondaryButton,
  statsCard,
  text,
  userCard,
} from '../styles/shared-styles';

interface WaitlistAdminNotificationEmailProps {
  userName: string;
  userEmail: string;
  role: 'user' | 'lawyer';
  totalWaitlistCount: number;
  userCount: number;
  lawyerCount: number;
  signupTime: string;
  unsubscribeUrl?: string;
}

export const WaitlistAdminNotificationEmail = ({
  userName,
  userEmail,
  role,
  totalWaitlistCount,
  userCount,
  lawyerCount,
  signupTime,
  unsubscribeUrl,
}: WaitlistAdminNotificationEmailProps) => {
  const roleText = role === 'lawyer' ? 'Legal Professional' : 'User';
  const roleEmoji = role === 'lawyer' ? '⚖️' : '👤';

  // Custom styles for admin-specific elements
  const detailText = {
    color: '#1e293b', // slate-800
    fontSize: '14px',
    lineHeight: '20px',
    margin: '0 0 8px 0',
  };

  const statsGrid = {
    display: 'flex',
    justifyContent: 'space-between',
    gap: '16px',
  };

  const statItem = {
    textAlign: 'center' as const,
    flex: '1',
  };

  const statNumber = {
    color: '#0da2a6', // accent-300
    fontSize: '32px',
    fontWeight: 'bold',
    margin: '0',
    fontFamily: 'SF Mono, monospace',
  };

  const statLabel = {
    color: '#6b7280',
    fontSize: '14px',
    margin: '4px 0 0 0',
  };

  const quickActions = {
    backgroundColor: '#fefefe',
    borderRadius: '8px',
    padding: '20px',
    margin: '24px 0',
  };

  const actionText = {
    color: '#475569',
    fontSize: '14px',
    lineHeight: '20px',
    margin: '0 0 8px 0',
  };

  return (
    <Html>
      <Head />
      <Preview>
        New waitlist signup: {userName} ({roleText})
      </Preview>
      <Body style={main}>
        <Container style={container}>
          <EmailHeader type="waitlist" alt="Notamess Forms - Admin Dashboard" />

          <Heading style={h1}>🚀 New Waitlist Signup!</Heading>

          <Text style={text}>
            A new {roleText.toLowerCase()} has joined the Notamess Forms
            waitlist.
          </Text>

          {/* User Details Card */}
          <Section style={userCard}>
            <Heading style={h2}>{roleEmoji} New Signup Details</Heading>
            <Text style={detailText}>
              <strong>Name:</strong> {userName}
            </Text>
            <Text style={detailText}>
              <strong>Email:</strong> {userEmail}
            </Text>
            <Text style={detailText}>
              <strong>Role:</strong> {roleText}
            </Text>
            <Text style={detailText}>
              <strong>Signup Time:</strong> {signupTime}
            </Text>
          </Section>

          {/* Stats Card */}
          <Section style={statsCard}>
            <Heading style={h2}>📊 Current Waitlist Stats</Heading>
            <Section style={statsGrid}>
              <Section style={statItem}>
                <Text style={statNumber}>{totalWaitlistCount}</Text>
                <Text style={statLabel}>Total Signups</Text>
              </Section>
              <Section style={statItem}>
                <Text style={statNumber}>{userCount}</Text>
                <Text style={statLabel}>Users</Text>
              </Section>
              <Section style={statItem}>
                <Text style={statNumber}>{lawyerCount}</Text>
                <Text style={statLabel}>Lawyers</Text>
              </Section>
            </Section>
          </Section>

          {/* Action Buttons */}
          <Section style={buttonContainer}>
            <Button style={button} href="https://notamess.com/admin/waitlist">
              View Waitlist Dashboard
            </Button>
          </Section>

          <Section style={buttonContainer}>
            <Button
              style={secondaryButton}
              href={`mailto:${userEmail}?subject=Welcome to Notamess Forms&body=Hi ${userName},%0D%0A%0D%0AThank you for joining our waitlist!`}
            >
              Send Personal Welcome Email
            </Button>
          </Section>

          {/* Quick Actions */}
          <Section style={quickActions}>
            <Heading style={h3}>Quick Actions:</Heading>
            <Text style={actionText}>
              •{' '}
              <Link href="https://notamess.com/admin/waitlist" style={link}>
                View full waitlist
              </Link>
            </Text>
            <Text style={actionText}>
              •{' '}
              <Link href="https://notamess.com/admin/analytics" style={link}>
                Check signup analytics
              </Link>
            </Text>
            <Text style={actionText}>
              •{' '}
              <Link
                href="https://notamess.com/admin/email-campaigns"
                style={link}
              >
                Send update to waitlist
              </Link>
            </Text>
          </Section>

          <EmailFooter
            unsubscribeUrl={unsubscribeUrl}
            showUnsubscribe={false} // Admin emails don't need unsubscribe
            showSupportContact={false}
          />
        </Container>
      </Body>
    </Html>
  );
};

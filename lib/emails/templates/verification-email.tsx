import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';
import {
  button,
  buttonContainer,
  container,
  footer,
  h1,
  link,
  main,
  text,
} from '../styles/shared-styles';

interface VerificationEmailProps {
  verificationLink: string;
  userName: string;
  unsubscribeUrl?: string;
}

export const VerificationEmail = ({
  verificationLink,
  userName,
  unsubscribeUrl,
}: VerificationEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Verify your email address for Notamess Forms</Preview>
      <Body style={main}>
        <Container style={container}>
          <EmailHeader type="auth" alt="Notamess Forms - Email Verification" />

          <Heading style={h1}>Verify your email address</Heading>
          <Text style={text}>Hi {userName},</Text>
          <Text style={text}>
            Thanks for signing up for Notamess Forms! Please verify your email
            address by clicking the button below.
          </Text>

          <Section style={buttonContainer}>
            <Button style={button} href={verificationLink}>
              Verify Email Address
            </Button>
          </Section>

          <Text style={text}>
            If you didn't sign up for Notamess Forms, you can safely ignore this
            email.
          </Text>
          <Text style={text}>
            This verification link will expire in 24 hours.
          </Text>

          <Text style={footer}>
            If the button above doesn't work, paste this link into your browser:
          </Text>
          <Text style={footer}>
            <Link href={verificationLink} style={link}>
              {verificationLink}
            </Link>
          </Text>

          <EmailFooter
            unsubscribeUrl={unsubscribeUrl}
            showUnsubscribe={false} // Don't show unsubscribe for verification emails
            showSupportContact={false} // Footer already has fallback link
          />
        </Container>
      </Body>
    </Html>
  );
};

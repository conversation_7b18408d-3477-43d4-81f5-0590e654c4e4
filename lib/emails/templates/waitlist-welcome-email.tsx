import {
  Body,
  Button,
  Container,
  Head,
  Html,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';
import {
  button,
  buttonContainer,
  container,
  main,
  positionContainer,
  positionText,
  text,
} from '../styles/shared-styles';

interface WaitlistWelcomeEmailProps {
  userName: string;
  userEmail: string;
  role: 'user' | 'lawyer';
  waitlistPosition?: number;
  unsubscribeUrl?: string;
}

export const WaitlistWelcomeEmail = ({
  userName,
  userEmail: _userEmail,
  role,
  waitlistPosition,
  unsubscribeUrl,
}: WaitlistWelcomeEmailProps) => {
  const roleText = role === 'lawyer' ? 'Legal Professional' : 'User';

  return (
    <Html>
      <Head />
      <Preview>Welcome to the Notamess Forms waitlist!</Preview>
      <Body style={main}>
        <Container style={container}>
          <EmailHeader
            type="waitlist"
            alt="Notamess Forms - Waitlist Welcome"
          />
          <Text style={text}>Hi {userName},</Text>

          <Text style={text}>
            Thank you for joining the Notamess Forms waitlist as a{' '}
            <strong>{roleText}</strong>! We're excited to have you as part of
            our early community.
          </Text>

          {waitlistPosition && (
            <Section style={positionContainer}>
              <Text style={positionText}>
                You're #{waitlistPosition} on our waitlist
              </Text>
            </Section>
          )}
          <Section style={buttonContainer}>
            <Button style={button} href="https://forms.notamess.com">
              Learn More
            </Button>
          </Section>

          <Text style={text}>
            We'll keep you updated on our progress and notify you as soon as
            Notamess Forms is ready for early access. In the meantime, follow us
            on social media for the latest updates!
          </Text>
          <EmailFooter
            unsubscribeUrl={unsubscribeUrl}
            showUnsubscribe={true}
            showSupportContact={true}
          />
        </Container>
      </Body>
    </Html>
  );
};

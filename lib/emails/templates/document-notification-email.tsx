import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON><PERSON>,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';
import {
  button,
  buttonContainer,
  container,
  footer,
  h1,
  link,
  main,
  messageContainer,
  messageText,
  text,
} from '../styles/shared-styles';

interface DocumentNotificationEmailProps {
  documentLink: string;
  userName: string;
  documentName: string;
  notificationType: 'shared' | 'updated' | 'commented' | 'signed';
  senderName: string;
  message?: string;
  unsubscribeUrl?: string;
}

export const DocumentNotificationEmail = ({
  documentLink,
  userName,
  documentName,
  notificationType,
  senderName,
  message,
  unsubscribeUrl,
}: DocumentNotificationEmailProps) => {
  // Generate the appropriate subject and content based on notification type
  const getSubject = () => {
    switch (notificationType) {
      case 'shared':
        return `${senderName} shared a document with you`;
      case 'updated':
        return `${senderName} updated "${documentName}"`;
      case 'commented':
        return `${senderName} commented on "${documentName}"`;
      case 'signed':
        return `${senderName} signed "${documentName}"`;
      default:
        return `Notification about "${documentName}"`;
    }
  };

  const getHeading = () => {
    switch (notificationType) {
      case 'shared':
        return 'Document Shared With You';
      case 'updated':
        return 'Document Updated';
      case 'commented':
        return 'New Comment on Document';
      case 'signed':
        return 'Document Signed';
      default:
        return 'Document Notification';
    }
  };

  const getMainContent = () => {
    switch (notificationType) {
      case 'shared':
        return `${senderName} has shared the document "${documentName}" with you. You can now view and collaborate on this document.`;
      case 'updated':
        return `${senderName} has made updates to the document "${documentName}". You can review the changes by clicking the button below.`;
      case 'commented':
        return `${senderName} has added a comment to the document "${documentName}". You can view the comment and respond by clicking the button below.`;
      case 'signed':
        return `${senderName} has signed the document "${documentName}". You can view the signed document by clicking the button below.`;
      default:
        return `You have a new notification regarding the document "${documentName}".`;
    }
  };

  const getButtonText = () => {
    switch (notificationType) {
      case 'shared':
        return 'View Document';
      case 'updated':
        return 'Review Changes';
      case 'commented':
        return 'View Comment';
      case 'signed':
        return 'View Signed Document';
      default:
        return 'View Document';
    }
  };

  return (
    <Html>
      <Head />
      <Preview>{getSubject()}</Preview>
      <Body style={main}>
        <Container style={container}>
          <EmailHeader
            type="notifications"
            alt="Notamess Forms - Document Notification"
          />

          <Heading style={h1}>{getHeading()}</Heading>
          <Text style={text}>Hi {userName},</Text>
          <Text style={text}>{getMainContent()}</Text>

          {message && (
            <Section style={messageContainer}>
              <Text style={messageText}>{message}</Text>
            </Section>
          )}

          <Section style={buttonContainer}>
            <Button style={button} href={documentLink}>
              {getButtonText()}
            </Button>
          </Section>

          <Text style={text}>
            If you have any questions, please contact our support team.
          </Text>

          <Text style={footer}>
            If the button above doesn't work, paste this link into your browser:
          </Text>
          <Text style={footer}>
            <Link href={documentLink} style={link}>
              {documentLink}
            </Link>
          </Text>

          <EmailFooter
            unsubscribeUrl={unsubscribeUrl}
            showUnsubscribe={true}
            showSupportContact={false} // Already mentioned support above
          />
        </Container>
      </Body>
    </Html>
  );
};

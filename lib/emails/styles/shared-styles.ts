/**
 * Shared email styles for Notamess Forms
 * Extracted from waitlist-welcome-email.tsx for consistency across all email templates
 */

// Main container styles
export const main = {
  backgroundColor: '#f5f5f5', // neutral-50 from your design
  fontFamily:
    'Geist Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  padding: '20px 0',
};

export const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '40px 20px',
  borderRadius: '8px',
  maxWidth: '600px',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
};

// Header styles
export const header = {
  textAlign: 'center' as const,
  marginBottom: '32px',
  paddingBottom: '24px',
};

// Typography styles
export const h1 = {
  color: '#111827', // gray-900
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 24px 0',
  textAlign: 'center' as const,
};

export const h2 = {
  color: '#111827', // gray-900
  fontSize: '18px',
  fontWeight: '600',
  margin: '0 0 16px 0',
};

export const h3 = {
  color: '#111827', // gray-900
  fontSize: '16px',
  fontWeight: '600',
  margin: '0 0 12px 0',
};

export const text = {
  color: '#374151', // gray-700
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px 0',
  textAlign: 'center' as const,
};

export const textLeft = {
  ...text,
  textAlign: 'left' as const,
};

// Button styles
export const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

export const button = {
  backgroundColor: '#0da2a6', // accent-300 - matches your project colors
  borderRadius: '9999px', // full rounded like your buttons
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
  boxShadow:
    '#0891a3 0px -2.4px 0px 0px inset, rgba(143, 39, 9, 0.2) 0px 1px 3px 0px, #0891a3 0px 0px 0px 1px', // shadow-teal style
  transition: 'all 0.3s ease',
};

export const secondaryButton = {
  backgroundColor: '#f87550', // notamess-50
  borderRadius: '9999px',
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 20px',
  boxShadow:
    '#e3370b 0px -2.4px 0px 0px inset, rgba(143, 39, 9, 0.2) 0px 1px 3px 0px, #e5370f 0px 0px 0px 1px',
};

// Divider styles
export const hr = {
  borderColor: '#e5e7eb',
  margin: '32px 0',
};

// Footer styles
export const footer = {
  color: '#6b7280', // gray-500
  fontSize: '14px',
  lineHeight: '20px',
  margin: '8px 0',
  textAlign: 'center' as const,
};

export const link = {
  color: '#0da2a6', // accent-300
  textDecoration: 'underline',
};

// Special component styles
export const positionContainer = {
  background:
    'repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(13, 162, 166, 0.1) 10px, rgba(13, 162, 166, 0.1) 20px)',
  padding: '16px',
  textAlign: 'center' as const,
  margin: '24px 0',
};

export const positionText = {
  color: '#047857', // green-700
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0',
};

// Card styles for admin emails
export const userCard = {
  backgroundColor: '#f0f9ff', // sky-50
  border: '1px solid #0ea5e9', // sky-500
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
};

export const statsCard = {
  backgroundColor: '#f8fafc', // slate-50
  border: '1px solid #e2e8f0', // slate-200
  borderRadius: '8px',
  padding: '24px',
  margin: '24px 0',
};

export const messageContainer = {
  backgroundColor: '#f9f9f9',
  borderLeft: '4px solid #6366f1',
  padding: '12px 15px',
  margin: '20px 0',
  borderRadius: '4px',
};

export const messageText = {
  color: '#4b5563',
  fontSize: '15px',
  lineHeight: '22px',
  margin: '0',
  fontStyle: 'italic',
};

// Color constants
export const colors = {
  primary: '#0da2a6', // accent-300
  secondary: '#f87550', // notamess-50
  background: '#f5f5f5', // neutral-50
  white: '#ffffff',
  textPrimary: '#111827', // gray-900
  textSecondary: '#374151', // gray-700
  textMuted: '#6b7280', // gray-500
  border: '#e5e7eb', // gray-200
  success: '#047857', // green-700
  info: '#0ea5e9', // sky-500
};

// Spacing constants
export const spacing = {
  xs: '8px',
  sm: '12px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '40px',
};

// Typography constants
export const typography = {
  fontFamily: 'Geist Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '24px',
    xxl: '32px',
  },
  lineHeight: {
    tight: '16px',
    normal: '20px',
    relaxed: '24px',
  },
};

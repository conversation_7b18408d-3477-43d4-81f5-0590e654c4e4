import { Hr, <PERSON>, Text } from '@react-email/components';
import { footer, hr, link } from '../styles/shared-styles';

interface EmailFooterProps {
  unsubscribeUrl?: string;
  showUnsubscribe?: boolean;
  supportEmail?: string;
  showSupportContact?: boolean;
}

export const EmailFooter = ({
  unsubscribeUrl,
  showUnsubscribe = true,
  supportEmail = '<EMAIL>',
  showSupportContact = true,
}: EmailFooterProps) => {
  return (
    <>
      <Hr style={hr} />
      
      {showSupportContact && (
        <Text style={footer}>
          Questions? Reply to this email or contact us at{' '}
          <Link href={`mailto:${supportEmail}`} style={link}>
            {supportEmail}
          </Link>
        </Text>
      )}

      {showUnsubscribe && unsubscribeUrl && (
        <Text style={footer}>
          <Link href={unsubscribeUrl} style={link}>
            Unsubscribe
          </Link>
        </Text>
      )}

      <Text style={footer}>
        © {new Date().getFullYear()} Notamess Forms. All rights reserved.
      </Text>
    </>
  );
};

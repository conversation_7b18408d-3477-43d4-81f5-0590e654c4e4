import { Img, Section } from '@react-email/components';
import { header } from '../styles/shared-styles';

interface EmailHeaderProps {
  type: 'general' | 'auth' | 'notifications' | 'waitlist';
  alt?: string;
  width?: string;
  height?: string;
}

export const EmailHeader = ({
  type,
  alt = 'Notamess Forms',
  width = '100%',
  height = 'auto',
}: EmailHeaderProps) => {
  // Map email types to their corresponding header images
  const getHeaderImage = (emailType: string): string => {
    const baseUrl = 'https://forms.notamess.com/assets/imgs/emails';

    switch (emailType) {
      case 'auth':
        return `${baseUrl}/email-header-auth.png`;
      case 'notifications':
        return `${baseUrl}/email-header-notofications.png`; // Note: keeping original filename with typo
      case 'waitlist':
        return `${baseUrl}/email-header-wailtlists.png`; // Note: keeping original filename with typo
      case 'general':
      default:
        return `${baseUrl}/email-header.png`;
    }
  };

  return (
    <Section style={header}>
      <Img src={getHeaderImage(type)} alt={alt} width={width} height={height} />
    </Section>
  );
};

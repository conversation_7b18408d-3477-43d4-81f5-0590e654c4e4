import { triggerNotificationUpdate } from '@/lib/notification-events';
import { notificationService } from '@/lib/services/NotificationService';
import { userStore } from '@/lib/store/user';

/**
 * Helper function to create a notification when a document is exported
 */
export async function notifyDocumentExport(
  documentId: string,
  documentName: string,
  exportFormat: string = 'PDF'
): Promise<void> {
  try {
    const { user } = userStore.getState();
    if (!user) return;

    // Create the notification directly using the notification service
    await notificationService.documentExported(
      user.id,
      documentId,
      documentName,
      exportFormat
    );

    // Trigger a notification update event to refresh the UI
    triggerNotificationUpdate();

    console.log(`Document export notification created for ${documentName}`);
  } catch (error) {
    console.error('Error creating document export notification:', error);
  }
}

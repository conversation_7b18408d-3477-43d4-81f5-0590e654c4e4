import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type WaitlistEntry = {
  id: string;
  full_name: string;
  email: string;
  role: 'user' | 'lawyer';
  created_at: string;
  updated_at: string;
};

export type WaitlistStats = {
  total: number;
  users: number;
  lawyers: number;
  recent: Array<{
    role: string;
    created_at: string;
  }>;
};

export type State = {
  isOnWaitlist: boolean;
  userWaitlistEntry: WaitlistEntry | null;
  stats: WaitlistStats | null;
  isLoading: boolean;
  error: string | null;
};

export type Action = {
  setIsOnWaitlist: (isOnWaitlist: boolean) => void;
  setUserWaitlistEntry: (entry: WaitlistEntry | null) => void;
  setStats: (stats: WaitlistStats | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
};

export type WaitlistStoreProps = State & Action;

const initialState: State = {
  isOnWaitlist: false,
  userWaitlistEntry: null,
  stats: null,
  isLoading: false,
  error: null,
};

export const waitlistStore = create<State & Action>()(
  persist(
    (set) => ({
      ...initialState,
      setIsOnWaitlist: (isOnWaitlist) => set({ isOnWaitlist }),
      setUserWaitlistEntry: (entry) => set({ userWaitlistEntry: entry }),
      setStats: (stats) => set({ stats }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      reset: () => set(initialState),
    }),
    {
      name: 'waitlist-store',
      partialize: (state) => ({
        isOnWaitlist: state.isOnWaitlist,
        userWaitlistEntry: state.userWaitlistEntry,
      }),
    }
  )
);

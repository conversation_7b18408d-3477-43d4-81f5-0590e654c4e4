import { supabaseClient } from '@/lib/supabase/client';
import {
  Organization,
  OrganizationMember,
  Team,
  TeamMember,
  TeamWithMembers,
  OrganizationWithDetails,
  CreateOrganizationParams,
  UpdateOrganizationParams,
  CreateTeamParams,
  UpdateTeamParams,
  AddTeamMemberParams,
  UpdateTeamMemberParams,
} from '@/lib/types/database-modules';
import { Database } from '@/lib/supabase/database-types';
import { toast } from 'sonner';

// Define types for enterprise features
export type SubscriptionLevel = 'basic' | 'professional' | 'enterprise';
export type TeamRole = 'member' | 'admin';
export type PermissionAction =
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'manage';

// Database row type for team_members table
interface TeamMemberRow {
  id: string;
  team_id: string;
  user_id: string;
  role: string;
  joined_at: string;
}

// Helper function to validate team member role
function isValidTeamRole(role: string): role is 'member' | 'admin' {
  return role === 'member' || role === 'admin';
}

// Helper function to transform database row to TeamMember interface
function transformTeamMemberRow(row: TeamMemberRow): TeamMember {
  const validRole = isValidTeamRole(row.role) ? row.role : 'member';
  return {
    id: row.id,
    team_id: row.team_id,
    user_id: row.user_id,
    role: validRole,
    created_at: row.joined_at,
    updated_at: row.joined_at, // Use joined_at as fallback for updated_at
  };
}

export interface Feature {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

export interface Permission {
  id: string;
  resource: string;
  action: PermissionAction;
  conditions?: Record<string, any>;
  createdAt: Date;
}

// Service error types
export interface ServiceError {
  message: string;
  code?: string;
  details?: any;
}

/**
 * Service for managing enterprise organizations, teams, and permissions
 */
export class EnterpriseService {
  private static instance: EnterpriseService;
  private currentUser: { id: string; email: string } | null = null;

  private constructor() {
    // Initialize with current user from auth
    this.initializeCurrentUser();
  }

  public static getInstance(): EnterpriseService {
    if (!EnterpriseService.instance) {
      EnterpriseService.instance = new EnterpriseService();
    }
    return EnterpriseService.instance;
  }

  /**
   * Initialize current user from Supabase auth
   */
  private async initializeCurrentUser(): Promise<void> {
    try {
      const {
        data: { user },
      } = await supabaseClient.auth.getUser();
      if (user) {
        this.currentUser = {
          id: user.id,
          email: user.email || '',
        };
      }
    } catch (error) {
      console.error('Error initializing current user:', error);
      // Don't throw here as this is called in constructor
    }
  }

  /**
   * Set the current user
   */
  public setCurrentUser(user: { id: string; email: string }): void {
    this.currentUser = user;
  }

  /**
   * Get the current user
   */
  public getCurrentUser(): { id: string; email: string } | null {
    return this.currentUser;
  }

  /**
   * Create a new organization
   */
  public async createOrganization(
    params: CreateOrganizationParams & {
      subscription_level?: SubscriptionLevel;
      admin_user_id?: string;
    }
  ): Promise<Organization> {
    try {
      // Ensure user is authenticated
      const {
        data: { user },
      } = await supabaseClient.auth.getUser();
      if (!user) {
        throw new Error('User must be logged in to create an organization');
      }

      // Create the organization in the database
      const { data: organization, error: orgError } = await supabaseClient
        .from('organizations')
        .insert({
          name: params.name,
          description: params.description || null,
          logo_url: params.logo_url || null,
          subscription_level: params.subscription_level || 'basic',
          features: JSON.stringify(
            this.getFeaturesBySubscriptionLevel(
              params.subscription_level || 'basic'
            )
          ) as any,
        })
        .select()
        .single();

      if (orgError) {
        throw new Error(`Failed to create organization: ${orgError.message}`);
      }

      // Add the creator as an admin member
      const { error: memberError } = await supabaseClient
        .from('organization_members')
        .insert({
          organization_id: organization.id,
          user_id: params.admin_user_id || user.id,
          role: 'admin',
        });

      if (memberError) {
        console.error('Error adding admin member:', memberError);
        // Don't throw here as organization was created successfully
      }

      // Create a default team for the organization
      try {
        await this.createTeam({
          name: 'Default Team',
          description: 'Default team for all members',
          organization_id: organization.id,
        });
      } catch (teamError) {
        console.error('Error creating default team:', teamError);
        // Don't throw here as organization was created successfully
      }

      toast.success(`Organization "${params.name}" created successfully`);
      return organization;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to create organization';
      toast.error(errorMessage);
      throw error;
    }
  }

  /**
   * Get an organization by ID
   */
  public async getOrganization(
    organizationId: string
  ): Promise<Organization | null> {
    try {
      const { data: organization, error } = await supabaseClient
        .from('organizations')
        .select('*')
        .eq('id', organizationId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw new Error(`Failed to get organization: ${error.message}`);
      }

      return organization;
    } catch (error) {
      console.error('Error getting organization:', error);
      return null;
    }
  }

  /**
   * Get all organizations for the current user
   */
  public async getUserOrganizations(): Promise<Organization[]> {
    try {
      const {
        data: { user },
      } = await supabaseClient.auth.getUser();
      if (!user) {
        return [];
      }

      // Get organizations where the user is a member
      const { data: memberOf, error: memberError } = await supabaseClient
        .from('organization_members')
        .select('organization_id')
        .eq('user_id', user.id);

      if (memberError) {
        throw new Error(
          `Failed to get user organizations: ${memberError.message}`
        );
      }

      if (!memberOf || memberOf.length === 0) {
        return [];
      }

      const orgIds = memberOf.map((m) => m.organization_id);

      const { data: organizations, error: orgsError } = await supabaseClient
        .from('organizations')
        .select('*')
        .in('id', orgIds)
        .order('name');

      if (orgsError) {
        throw new Error(`Failed to get organizations: ${orgsError.message}`);
      }

      return organizations || [];
    } catch (error) {
      console.error('Error getting user organizations:', error);
      toast.error('Failed to load organizations');
      return [];
    }
  }

  /**
   * Update an organization
   */
  public async updateOrganization(
    organizationId: string,
    updates: UpdateOrganizationParams
  ): Promise<Organization | null> {
    try {
      // Check if user has permission
      const hasAccess = await this.hasOrganizationAdminAccess(organizationId);
      if (!hasAccess) {
        toast.error('You do not have permission to update this organization');
        return null;
      }

      // Update the organization in the database
      const { data: organization, error } = await supabaseClient
        .from('organizations')
        .update({
          name: updates.name,
          description: updates.description,
          logo_url: updates.logo_url,
          updated_at: new Date().toISOString(),
        })
        .eq('id', organizationId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update organization: ${error.message}`);
      }

      toast.success('Organization updated successfully');
      return organization;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to update organization';
      toast.error(errorMessage);
      console.error('Error updating organization:', error);
      return null;
    }
  }

  /**
   * Create a new team within an organization
   */
  public async createTeam(params: CreateTeamParams): Promise<Team> {
    try {
      // Check if organization exists
      const organization = await this.getOrganization(params.organization_id);
      if (!organization) {
        throw new Error('Organization not found');
      }

      // Check if user has permission to create a team
      const hasAccess = await this.hasOrganizationAdminAccess(
        params.organization_id
      );
      if (!hasAccess) {
        throw new Error(
          'You do not have permission to create a team in this organization'
        );
      }

      // Create the team in the database
      const { data: team, error } = await supabaseClient
        .from('teams')
        .insert({
          name: params.name,
          description: params.description || null,
          organization_id: params.organization_id,
          permissions: JSON.stringify(this.getDefaultTeamPermissions()) as any,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create team: ${error.message}`);
      }

      toast.success(`Team "${params.name}" created successfully`);
      return team;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to create team';
      toast.error(errorMessage);
      throw error;
    }
  }

  /**
   * Get a team by ID
   */
  public async getTeam(teamId: string): Promise<Team | null> {
    try {
      const { data: team, error } = await supabaseClient
        .from('teams')
        .select('*')
        .eq('id', teamId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw new Error(`Failed to get team: ${error.message}`);
      }

      return team;
    } catch (error) {
      console.error('Error getting team:', error);
      return null;
    }
  }

  /**
   * Update a team
   */
  public async updateTeam(
    teamId: string,
    updates: UpdateTeamParams
  ): Promise<Team | null> {
    try {
      // Check if user has permission
      const hasAccess = await this.hasTeamAdminAccess(teamId);
      if (!hasAccess) {
        toast.error('You do not have permission to update this team');
        return null;
      }

      // Update the team in the database
      const { data: team, error } = await supabaseClient
        .from('teams')
        .update({
          name: updates.name,
          description: updates.description,
          updated_at: new Date().toISOString(),
        })
        .eq('id', teamId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update team: ${error.message}`);
      }

      toast.success('Team updated successfully');
      return team;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update team';
      toast.error(errorMessage);
      console.error('Error updating team:', error);
      return null;
    }
  }

  /**
   * Add a user to a team
   */
  public async addTeamMember(
    params: AddTeamMemberParams
  ): Promise<TeamMember | null> {
    try {
      // Check if user has permission to add members
      const hasAccess = await this.hasTeamAdminAccess(params.team_id);
      if (!hasAccess) {
        toast.error('You do not have permission to add members to this team');
        return null;
      }

      // Check if user is already a member
      const { data: existingMember } = await supabaseClient
        .from('team_members')
        .select('*')
        .eq('team_id', params.team_id)
        .eq('user_id', params.user_id)
        .single();

      if (existingMember) {
        toast.info('User is already a member of this team');
        // Transform existing member to match interface
        return transformTeamMemberRow(existingMember as TeamMemberRow);
      }

      // Add the team member to the database
      const { data: teamMember, error } = await supabaseClient
        .from('team_members')
        .insert({
          team_id: params.team_id,
          user_id: params.user_id,
          role: params.role,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to add team member: ${error.message}`);
      }

      // Transform the database result to match TeamMember interface
      const transformedMember = transformTeamMemberRow(
        teamMember as TeamMemberRow
      );

      toast.success('Team member added successfully');
      return transformedMember;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to add team member';
      toast.error(errorMessage);
      console.error('Error adding team member:', error);
      return null;
    }
  }

  /**
   * Update a team member's role
   */
  public async updateTeamMemberRole(
    teamId: string,
    userId: string,
    params: UpdateTeamMemberParams
  ): Promise<TeamMember | null> {
    try {
      // Check if user has permission to update members
      const hasAccess = await this.hasTeamAdminAccess(teamId);
      if (!hasAccess) {
        toast.error(
          'You do not have permission to update members in this team'
        );
        return null;
      }

      // Update the team member's role in the database
      const { data: teamMember, error } = await supabaseClient
        .from('team_members')
        .update({
          role: params.role,
        })
        .eq('team_id', teamId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update team member role: ${error.message}`);
      }

      // Transform the database result to match TeamMember interface
      const transformedMember = transformTeamMemberRow(
        teamMember as TeamMemberRow
      );

      toast.success('Team member role updated successfully');
      return transformedMember;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to update team member role';
      toast.error(errorMessage);
      console.error('Error updating team member role:', error);
      return null;
    }
  }

  /**
   * Remove a user from a team
   */
  public async removeTeamMember(
    teamId: string,
    userId: string
  ): Promise<boolean> {
    try {
      // Check if user has permission to remove members
      const hasAccess = await this.hasTeamAdminAccess(teamId);
      if (!hasAccess) {
        toast.error(
          'You do not have permission to remove members from this team'
        );
        return false;
      }

      // Remove the team member from the database
      const { error } = await supabaseClient
        .from('team_members')
        .delete()
        .eq('team_id', teamId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`Failed to remove team member: ${error.message}`);
      }

      toast.success('Team member removed successfully');
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to remove team member';
      toast.error(errorMessage);
      console.error('Error removing team member:', error);
      return false;
    }
  }

  /**
   * Check if the current user has admin access to an organization
   */
  public async hasOrganizationAdminAccess(
    organizationId: string
  ): Promise<boolean> {
    try {
      const {
        data: { user },
      } = await supabaseClient.auth.getUser();
      if (!user) {
        return false;
      }

      // Check if user is an admin or owner of the organization
      const { data: membership, error } = await supabaseClient
        .from('organization_members')
        .select('role')
        .eq('organization_id', organizationId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error checking organization admin access:', error);
        return false;
      }

      return membership?.role === 'admin' || membership?.role === 'owner';
    } catch (error) {
      console.error('Error checking organization admin access:', error);
      return false;
    }
  }

  /**
   * Check if the current user has admin access to a team
   */
  public async hasTeamAdminAccess(teamId: string): Promise<boolean> {
    try {
      const {
        data: { user },
      } = await supabaseClient.auth.getUser();
      if (!user) {
        return false;
      }

      // Get team details
      const team = await this.getTeam(teamId);
      if (!team) {
        return false;
      }

      // Check if user is an admin of the parent organization
      const hasOrgAccess = await this.hasOrganizationAdminAccess(
        team.organization_id
      );
      if (hasOrgAccess) {
        return true;
      }

      // Check if user is an admin of the team
      const { data: membership, error } = await supabaseClient
        .from('team_members')
        .select('role')
        .eq('team_id', teamId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error checking team admin access:', error);
        return false;
      }

      return membership?.role === 'admin';
    } catch (error) {
      console.error('Error checking team admin access:', error);
      return false;
    }
  }

  /**
   * Check if the current user has a specific permission on a team
   */
  public async hasTeamPermission(
    teamId: string,
    action: string
  ): Promise<boolean> {
    try {
      const {
        data: { user },
      } = await supabaseClient.auth.getUser();
      if (!user) {
        return false;
      }

      // Admins always have all permissions
      const hasAdminAccess = await this.hasTeamAdminAccess(teamId);
      if (hasAdminAccess) {
        return true;
      }

      // Find the user's role in the team
      const { data: membership, error } = await supabaseClient
        .from('team_members')
        .select('role')
        .eq('team_id', teamId)
        .eq('user_id', user.id)
        .single();

      if (error || !membership) {
        return false;
      }

      // Admins can do everything
      if (membership.role === 'admin') {
        return true;
      }

      // Members have limited permissions
      if (membership.role === 'member') {
        // Members can read and create, but not update, delete, or manage
        return action === 'read' || action === 'create';
      }

      return false;
    } catch (error) {
      console.error('Error checking team permission:', error);
      return false;
    }
  }

  /**
   * Get features available for a subscription level
   */
  private getFeaturesBySubscriptionLevel(level: SubscriptionLevel): Feature[] {
    const features: Feature[] = [
      {
        id: 'feature_basic_forms',
        name: 'Basic Forms',
        description: 'Create and manage basic forms',
        enabled: true,
      },
      {
        id: 'feature_advanced_forms',
        name: 'Advanced Forms',
        description: 'Create and manage advanced forms with conditional logic',
        enabled: level !== 'basic',
      },
      {
        id: 'feature_ai_suggestions',
        name: 'AI Suggestions',
        description: 'Get AI-powered suggestions for form fields',
        enabled: level !== 'basic',
      },
      {
        id: 'feature_blockchain',
        name: 'Blockchain Verification',
        description: 'Verify documents on the blockchain',
        enabled: level === 'enterprise',
      },
      {
        id: 'feature_custom_branding',
        name: 'Custom Branding',
        description: 'Add custom branding to forms and documents',
        enabled: level !== 'basic',
      },
      {
        id: 'feature_team_collaboration',
        name: 'Team Collaboration',
        description: 'Collaborate with team members on forms',
        enabled: true,
      },
      {
        id: 'feature_consulting',
        name: 'Consulting Services',
        description: 'Access professional consulting services',
        enabled: level === 'enterprise',
      },
    ];

    return features;
  }

  /**
   * Get default permissions for a new team
   */
  private getDefaultTeamPermissions(): Permission[] {
    const now = new Date();
    return [
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'form',
        action: 'create',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'form',
        action: 'read',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'form',
        action: 'update',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'document',
        action: 'create',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'document',
        action: 'read',
        createdAt: now,
      },
    ];
  }

  /**
   * Get organization with details (members and teams)
   */
  public async getOrganizationWithDetails(
    organizationId: string
  ): Promise<OrganizationWithDetails | null> {
    try {
      // Get organization
      const organization = await this.getOrganization(organizationId);
      if (!organization) {
        return null;
      }

      // Get organization members
      const { data: members, error: membersError } = await supabaseClient
        .from('organization_members')
        .select(
          `
          *,
          profiles:user_id (
            full_name,
            email
          )
        `
        )
        .eq('organization_id', organizationId);

      if (membersError) {
        console.error('Error fetching organization members:', membersError);
      }

      // Get organization teams
      const { data: teams, error: teamsError } = await supabaseClient
        .from('teams')
        .select('*')
        .eq('organization_id', organizationId);

      if (teamsError) {
        console.error('Error fetching organization teams:', teamsError);
      }

      return {
        ...organization,
        members: (members || []) as any,
        teams: teams || [],
      };
    } catch (error) {
      console.error('Error getting organization with details:', error);
      return null;
    }
  }

  /**
   * Get team with members
   */
  public async getTeamWithMembers(
    teamId: string
  ): Promise<TeamWithMembers | null> {
    try {
      // Get team details
      const team = await this.getTeam(teamId);
      if (!team) {
        return null;
      }

      // Get team members with user details
      const { data: members, error: membersError } = await supabaseClient
        .from('team_members')
        .select(
          `
          *,
          profiles:user_id (
            full_name,
            email,
            avatar_url
          )
        `
        )
        .eq('team_id', teamId);

      if (membersError) {
        console.error('Error fetching team members:', membersError);
        return {
          ...team,
          members: [],
        };
      }

      // Transform the data to match the expected format
      const transformedMembers =
        members?.map((member: any) => ({
          id: member.id,
          team_id: member.team_id,
          user_id: member.user_id,
          role: member.role,
          created_at: member.joined_at, // Map joined_at to created_at
          full_name: member.profiles?.full_name || '',
          email: member.profiles?.email || '',
          avatar_url: member.profiles?.avatar_url || null,
        })) || [];

      return {
        ...team,
        members: transformedMembers,
      };
    } catch (error) {
      console.error('Error getting team with members:', error);
      return null;
    }
  }

  /**
   * Delete an organization (admin only)
   */
  public async deleteOrganization(organizationId: string): Promise<boolean> {
    try {
      const hasAccess = await this.hasOrganizationAdminAccess(organizationId);
      if (!hasAccess) {
        toast.error('You do not have permission to delete this organization');
        return false;
      }

      const { error } = await supabaseClient
        .from('organizations')
        .delete()
        .eq('id', organizationId);

      if (error) {
        throw new Error(`Failed to delete organization: ${error.message}`);
      }

      toast.success('Organization deleted successfully');
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to delete organization';
      toast.error(errorMessage);
      console.error('Error deleting organization:', error);
      return false;
    }
  }

  /**
   * Delete a team (admin only)
   */
  public async deleteTeam(teamId: string): Promise<boolean> {
    try {
      const hasAccess = await this.hasTeamAdminAccess(teamId);
      if (!hasAccess) {
        toast.error('You do not have permission to delete this team');
        return false;
      }

      const { error } = await supabaseClient
        .from('teams')
        .delete()
        .eq('id', teamId);

      if (error) {
        throw new Error(`Failed to delete team: ${error.message}`);
      }

      toast.success('Team deleted successfully');
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to delete team';
      toast.error(errorMessage);
      console.error('Error deleting team:', error);
      return false;
    }
  }

  /**
   * Get teams for an organization
   */
  public async getOrganizationTeams(organizationId: string): Promise<Team[]> {
    try {
      const { data: teams, error } = await supabaseClient
        .from('teams')
        .select('*')
        .eq('organization_id', organizationId)
        .order('name');

      if (error) {
        throw new Error(`Failed to get organization teams: ${error.message}`);
      }

      return teams || [];
    } catch (error) {
      console.error('Error getting organization teams:', error);
      toast.error('Failed to load teams');
      return [];
    }
  }
}

/**
 * Unified Document Service
 *
 * Consolidates all document-related functionality including:
 * - Document generation and PDF creation
 * - Document version management
 * - Document caching
 * - Document attachments
 * - Document export functionality
 *
 * This service consolidates functionality from:
 * - DocumentGenerationService.tsx
 * - DocumentVersionService.ts
 * - DocumentCacheService.ts
 * - document-attachment-service.ts
 * - document-export-service.ts
 */

'use client';

import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/client';
import {
  DocumentAttachment,
  DocumentAttachmentInsert,
} from '@/lib/types/database-modules';
import { toast } from 'sonner';

// Re-export types from other services for convenience
export type { ExportFormat, ExportOptions } from './documentExportService';
export type { DocumentVariable } from './DocumentGenerationService';
export type { SignatureOptions } from './SignatureOptions';

// Storage bucket name for document attachments
const STORAGE_BUCKET = 'document-attachments';

// Interface for file upload options
interface UploadOptions {
  cacheControl?: string;
  upsert?: boolean;
  contentType?: string;
}

/**
 * Helper function to handle errors consistently
 */
const handleError = (error: any, message: string, shouldToast = true): void => {
  console.error(`${message}:`, error);
  if (shouldToast) {
    toast.error(message);
  }
};

/**
 * Unified Document Service Class
 */
export class DocumentService {
  private static instance: DocumentService;

  static getInstance(): DocumentService {
    if (!DocumentService.instance) {
      DocumentService.instance = new DocumentService();
    }
    return DocumentService.instance;
  }

  // =============================================================================
  // DOCUMENT ATTACHMENT METHODS
  // =============================================================================

  /**
   * Upload a file to Supabase storage and create a document attachment record
   */
  async uploadAttachment(
    file: File,
    documentId: string,
    description?: string
  ): Promise<DocumentAttachment | null> {
    try {
      // First, check if the user is authenticated
      const { data: sessionData } = await supabaseClient.auth.getSession();
      if (!sessionData.session) {
        handleError(
          new Error('No active session'),
          'You must be logged in to upload attachments'
        );
        return null;
      }

      // Get user ID from session or store
      const userId =
        sessionData.session.user.id || userStore.getState().user?.id;
      if (!userId) {
        handleError(new Error('User ID not found'), 'Authentication error');
        return null;
      }

      // Create a unique filename to avoid collisions
      const timestamp = Date.now();
      const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filePath = `${documentId}/${timestamp}_${sanitizedFilename}`;

      console.log('Uploading attachment for document:', documentId);
      console.log('File details:', {
        name: file.name,
        type: file.type,
        size: file.size,
        path: filePath,
      });

      // Upload the file to Supabase storage
      const uploadResult = await supabaseClient.storage
        .from(STORAGE_BUCKET)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          contentType: file.type,
        } as UploadOptions);

      if (uploadResult.error) {
        const error = uploadResult.error;
        console.error('Storage upload error details:', {
          message: error.message || 'No message',
          name: error.name || 'Unknown error',
          stack: error.stack || 'No stack trace',
        });

        let errorMessage = 'Failed to upload attachment';
        if (error.message?.includes('permission')) {
          errorMessage = 'Permission denied when uploading attachment';
        } else if (error.message?.includes('not found')) {
          errorMessage = 'Storage bucket not found';
        } else if (error.message) {
          errorMessage = `Upload error: ${error.message}`;
        }

        toast.error(errorMessage);
        return null;
      }

      // Get the public URL
      const { data: publicUrlData } = supabaseClient.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl(filePath);

      if (!publicUrlData) {
        console.error('Failed to get public URL');
        toast.error('Could not generate public URL for attachment');
        return null;
      }

      const publicUrl = publicUrlData.publicUrl;

      // Create a record in the document_attachments table
      const attachmentData: DocumentAttachmentInsert = {
        document_id: documentId,
        file_name: file.name,
        file_path: filePath,
        file_type: file.type,
        file_size: file.size,
        file_url: publicUrl,
        uploaded_by: userId,
        description: description || null,
      };

      const { data: attachment, error: insertError } = await supabaseClient
        .from('document_attachments')
        .insert(attachmentData)
        .select('*')
        .single();

      if (insertError) {
        handleError(insertError, 'Failed to create attachment record');

        // Try to clean up the uploaded file to avoid orphaned storage files
        try {
          await supabaseClient.storage.from(STORAGE_BUCKET).remove([filePath]);
        } catch (cleanupError) {
          console.warn('Failed to clean up uploaded file:', cleanupError);
        }

        return null;
      }

      return attachment as DocumentAttachment;
    } catch (error: any) {
      handleError(error, 'Failed to upload attachment');
      return null;
    }
  }

  /**
   * Get all attachments for a document
   */
  async getAttachments(documentId: string): Promise<DocumentAttachment[]> {
    try {
      const { data, error } = (await supabaseClient
        .from('document_attachments')
        .select('*')
        .eq('document_id', documentId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })) as {
        data: DocumentAttachment[] | null;
        error: any;
      };

      if (error) {
        handleError(error, 'Failed to fetch attachments');
        return [];
      }

      return data as DocumentAttachment[];
    } catch (error) {
      handleError(error, 'Failed to fetch attachments');
      return [];
    }
  }

  /**
   * Get a single attachment by ID
   */
  async getAttachment(
    attachmentId: string
  ): Promise<DocumentAttachment | null> {
    try {
      const { data, error } = await supabaseClient
        .from('document_attachments')
        .select('*')
        .eq('id', attachmentId)
        .single();

      if (error) {
        handleError(error, 'Failed to fetch attachment');
        return null;
      }

      return data as DocumentAttachment;
    } catch (error) {
      handleError(error, 'Failed to fetch attachment');
      return null;
    }
  }

  /**
   * Delete an attachment
   */
  async deleteAttachment(attachmentId: string): Promise<boolean> {
    try {
      // First get the attachment to get the file path
      const attachment = await this.getAttachment(attachmentId);

      if (!attachment) {
        return false;
      }

      // Soft delete the attachment record
      const { error: updateError } = await supabaseClient
        .from('document_attachments')
        .update({ is_deleted: true })
        .eq('id', attachmentId);

      if (updateError) {
        handleError(updateError, 'Failed to delete attachment');
        return false;
      }

      // Delete the file from storage (optional - can be done in a background job)
      const { error: storageError } = await supabaseClient.storage
        .from(STORAGE_BUCKET)
        .remove([attachment.file_path as string]);

      if (storageError) {
        // Log but don't show toast or fail the operation
        handleError(
          storageError,
          'Error deleting attachment file from storage',
          false
        );
      }

      return true;
    } catch (error) {
      handleError(error, 'Failed to delete attachment');
      return false;
    }
  }

  // =============================================================================
  // DOCUMENT EXPORT METHODS (delegated to existing service)
  // =============================================================================

  /**
   * Export document - delegates to DocumentExportService
   */
  async exportDocument(document: any, options: any): Promise<any> {
    // Import the existing service dynamically to avoid circular dependencies
    const { DocumentExportService } = await import('./documentExportService');
    const exportService = new DocumentExportService();
    return exportService.exportDocument(document, options);
  }

  // =============================================================================
  // DOCUMENT GENERATION METHODS (delegated to existing service)
  // =============================================================================

  /**
   * Generate document - delegates to DocumentGenerationService
   */
  async generateDocument(
    template: any,
    formState: any,
    options: any
  ): Promise<any> {
    // Import the existing service dynamically to avoid circular dependencies
    const { DocumentGenerationService } = await import(
      './DocumentGenerationService'
    );
    const generationService = DocumentGenerationService.getInstance();
    return generationService.generateDocument(template, formState, options);
  }
}

// Export default instance for convenience
export const documentService = DocumentService.getInstance();

// Export the class for static method usage
export default DocumentService;

# Services Directory - Unified Architecture

This directory has been refactored to follow a unified, feature-based organization pattern. All service functionality is now consolidated into feature-specific files rather than use-case-specific files.

## New Service Structure

### Core Services

#### 📧 `emailService.ts`

**Unified email functionality** - Consolidates all email-related operations

- **Waitlist emails**: Welcome emails, admin notifications, updates, audience management
- **Document notifications**: Shared, updated, commented, signed notifications
- **Authentication emails**: Verification, password reset
- **User communications**: General email sending capabilities

**Key Methods:**

- `EmailService.sendWaitlistEmails(data)` - Send welcome + admin notification
- `EmailService.sendDocumentNotification(data)` - Send document notifications
- `EmailService.sendVerificationEmail(data)` - Send verification emails
- `EmailService.sendPasswordResetEmail(data)` - Send password reset emails

#### 🔔 `notificationService.ts` (Unified)

**All notification functionality** - Handles database, email, and toast notifications

- **Database notifications**: Create, read, update, delete notifications
- **Email integration**: Automatic email sending based on user preferences
- **Toast notifications**: Success, error, warning, info messages
- **Form state notifications**: Save, load, sync status notifications

**Key Methods:**

- `notificationService.createNotification(params)` - Create database notification
- `notificationService.showSuccess(message)` - Show success toast
- `notificationService.documentShared(userId, docId, ...)` - Document-specific notifications
- `notificationService.formStateSaved()` - Form state notifications

#### 📄 `documentService.ts`

**Document management** - Consolidates all document-related operations

- **Attachments**: Upload, download, delete document attachments
- **Export**: PDF, DOCX, and other format exports (delegates to existing services)
- **Generation**: Document creation and PDF generation (delegates to existing services)
- **Version management**: Document versioning (planned)

**Key Methods:**

- `documentService.uploadAttachment(file, documentId, description)`
- `documentService.getAttachments(documentId)`
- `documentService.deleteAttachment(attachmentId)`
- `documentService.exportDocument(documentId, format, options)`

#### 👤 `userService.ts`

**User management** - Consolidates user-related functionality

- **Avatar management**: Upload, set mapped avatars, generate UI avatars
- **Settings management**: User preferences, notifications, privacy settings
- **Profile management**: User profile updates and management

**Key Methods:**

- `userService.uploadAvatar(file, userId)` - Upload custom avatar
- `userService.setMappedAvatar(avatarId, userId)` - Set predefined avatar
- `userService.getBestAvatar(user)` - Get best available avatar
- `userService.getUserSettings(userId)` - Get user settings
- `userService.updateUserSettings(userId, settings)` - Update settings

### Legacy Services (Removed)

All legacy services have been successfully removed and their functionality consolidated into the unified services:

- ✅ `NotificationService.ts` → Merged into `notificationService.ts`
- ✅ `waitlist-email-service.ts` → Merged into `emailService.ts`
- ✅ `avatar-service.ts` → Merged into `userService.ts`
- ✅ `document-attachment-service.ts` → Merged into `documentService.ts`
- ✅ `settings-service.ts` → Merged into `userService.ts`

## Migration Guide

### Email Service Migration

**Before:**

```typescript
import { WaitlistEmailService } from '@/lib/services/waitlist-email-service';

// Send waitlist emails
const result = await WaitlistEmailService.sendWaitlistEmails(data);
```

**After:**

```typescript
import { EmailService } from '@/lib/services/emailService';

// Send waitlist emails (same interface)
const result = await EmailService.sendWaitlistEmails(data);
```

### Notification Service Migration

**Before:**

```typescript
import { NotificationService } from '@/lib/services/NotificationService';
import { notificationService } from '@/lib/services/notificationService';

const formNotifications = NotificationService.getInstance();
const dbNotifications = notificationService;
```

**After:**

```typescript
import { notificationService } from '@/lib/services/notificationService';

// Single unified service for all notifications
notificationService.formStateSaved(); // Form notifications
notificationService.createNotification(params); // Database notifications
notificationService.showSuccess(message); // Toast notifications
```

### Avatar Service Migration

**Before:**

```typescript
import { avatarService } from '@/lib/services/avatar-service';

const avatarUrl = await avatarService.uploadAvatar(file, userId);
```

**After:**

```typescript
import { userService } from '@/lib/services/userService';

const result = await userService.uploadAvatar(file, userId);
if (result.success) {
  console.log('Avatar URL:', result.avatarUrl);
}
```

### Document Attachment Migration

**Before:**

```typescript
import { documentAttachmentService } from '@/lib/services/document-attachment-service';

const attachment = await documentAttachmentService.uploadAttachment(
  file,
  docId
);
```

**After:**

```typescript
import { documentService } from '@/lib/services/documentService';

const attachment = await documentService.uploadAttachment(file, docId);
```

## Benefits of the New Architecture

### 🎯 **Feature-Based Organization**

- Related functionality is grouped together
- Easier to find and maintain code
- Clear separation of concerns

### 🔄 **Consistent APIs**

- Unified error handling patterns
- Consistent return types and interfaces
- Better TypeScript support

### 📈 **Scalability**

- Easy to add new functionality to existing services
- Reduced file proliferation
- Better code reuse

### 🛠 **Maintainability**

- Single source of truth for each feature area
- Easier testing and debugging
- Clearer dependencies

## Service Dependencies

```
emailService.ts
├── lib/emails/index.ts
├── lib/emails/templates/*
└── resend (external)

notificationService.ts
├── lib/supabase/client.ts
├── lib/emails/index.ts
├── sonner (toast library)
└── lib/notification-events.ts

documentService.ts
├── lib/supabase/client.ts
├── lib/store/user.ts
├── documentExportService.ts (delegation)
└── DocumentGenerationService.tsx (delegation)

userService.ts
├── lib/supabase/client.ts
├── lib/imgs/avatars/* (static assets)
└── sonner (toast library)
```

## Future Enhancements

### Planned Features

- **Payment Service**: Consolidate billing and subscription management
- **Analytics Service**: User behavior and system metrics
- **Integration Service**: Third-party API integrations
- **Audit Service**: System audit logs and compliance

### Service Improvements

- Add comprehensive error handling
- Implement service-level caching
- Add request/response logging
- Implement rate limiting
- Add service health checks

## Best Practices

### When Adding New Functionality

1. **Identify the feature area** - Which service does this belong to?
2. **Check existing patterns** - Follow established conventions
3. **Add proper TypeScript types** - Ensure type safety
4. **Include error handling** - Use consistent error patterns
5. **Update documentation** - Keep this README current

### Service Design Principles

- **Single Responsibility** - Each service handles one feature area
- **Dependency Injection** - Services can be easily tested and mocked
- **Error Boundaries** - Graceful error handling and user feedback
- **Async/Await** - Consistent asynchronous patterns
- **Type Safety** - Full TypeScript coverage

---

_Last updated: January 2025_
_Refactoring completed as part of Phase 8 - Document Management System_

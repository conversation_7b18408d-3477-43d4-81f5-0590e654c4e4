/**
 * Unified User Service
 *
 * Consolidates all user-related functionality including:
 * - Avatar management and generation
 * - User settings management
 * - Profile management
 * - User preferences
 *
 * This service consolidates functionality from:
 * - avatar-service.ts
 * - settings-service.ts
 */

'use client';

import { supabaseClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

// Avatar mappings - using placeholder URLs since static images don't exist
const avatarMap: Record<string, string> = {
  'avatar-1':
    'https://ui-avatars.com/api/?name=A1&size=128&background=3b82f6&color=fff',
  'avatar-2':
    'https://ui-avatars.com/api/?name=A2&size=128&background=ef4444&color=fff',
  'avatar-3':
    'https://ui-avatars.com/api/?name=A3&size=128&background=10b981&color=fff',
  'avatar-4':
    'https://ui-avatars.com/api/?name=A4&size=128&background=f59e0b&color=fff',
  'avatar-5':
    'https://ui-avatars.com/api/?name=A5&size=128&background=8b5cf6&color=fff',
  'avatar-6':
    'https://ui-avatars.com/api/?name=A6&size=128&background=ec4899&color=fff',
  'avatar-7':
    'https://ui-avatars.com/api/?name=A7&size=128&background=06b6d4&color=fff',
  'avatar-8':
    'https://ui-avatars.com/api/?name=A8&size=128&background=84cc16&color=fff',
  'avatar-9':
    'https://ui-avatars.com/api/?name=A9&size=128&background=f97316&color=fff',
  'avatar-10':
    'https://ui-avatars.com/api/?name=A10&size=128&background=6366f1&color=fff',
  'avatar-11':
    'https://ui-avatars.com/api/?name=A11&size=128&background=14b8a6&color=fff',
  'avatar-12':
    'https://ui-avatars.com/api/?name=A12&size=128&background=f43f5e&color=fff',
};

// Types and interfaces
export interface UserSettings {
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  timezone?: string;
  notifications?: {
    email?: boolean;
    push?: boolean;
    desktop?: boolean;
  };
  privacy?: {
    profileVisibility?: 'public' | 'private' | 'contacts';
    showEmail?: boolean;
    showPhone?: boolean;
  };
}

export interface AvatarUploadResult {
  success: boolean;
  avatarUrl?: string;
  error?: string;
}

/**
 * Unified User Service Class
 */
export class UserService {
  private static instance: UserService;

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  // =============================================================================
  // AVATAR MANAGEMENT METHODS
  // =============================================================================

  /**
   * Get all available avatar options
   */
  getAvailableAvatars(): Array<{ id: string; src: string; alt: string }> {
    return Object.entries(avatarMap).map(([id, src]) => ({
      id,
      src,
      alt: `Avatar ${id.split('-')[1]}`,
    }));
  }

  /**
   * Get mapped avatar by ID
   */
  getMappedAvatar(avatarId: string): string {
    const avatar = avatarMap[avatarId];
    if (avatar) {
      return avatar;
    }

    // Fallback to generated avatar
    return this.generateUIAvatar(avatarId);
  }

  /**
   * Generate UI Avatar using external service
   */
  generateUIAvatar(name: string, size: number = 128): string {
    const cleanName = encodeURIComponent(name || 'User');
    return `https://ui-avatars.com/api/?name=${cleanName}&size=${size}&background=random&color=fff&bold=true`;
  }

  /**
   * Get the best avatar for a user
   */
  getBestAvatar(user: any): string {
    // Priority: custom uploaded avatar > selected mapped avatar > generated avatar
    if (user?.avatar_url && !user.avatar_url.includes('ui-avatars.com')) {
      return user.avatar_url;
    }

    if (user?.avatar_id && avatarMap[user.avatar_id]) {
      return avatarMap[user.avatar_id];
    }

    // Generate avatar from user's name or email
    const name = user?.full_name || user?.email?.split('@')[0] || 'User';
    return this.generateUIAvatar(name);
  }

  /**
   * Upload custom avatar
   */
  async uploadAvatar(file: File, userId: string): Promise<AvatarUploadResult> {
    try {
      // Validate file
      if (!file.type.startsWith('image/')) {
        return { success: false, error: 'Please select an image file' };
      }

      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        return { success: false, error: 'File size must be less than 5MB' };
      }

      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Date.now()}.${fileExt}`;

      // Upload to Supabase storage
      const { data, error } = await supabaseClient.storage
        .from('avatars')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true,
        });

      if (error) {
        console.error('Avatar upload error:', error);
        return { success: false, error: 'Failed to upload avatar' };
      }

      // Get public URL
      const { data: publicUrlData } = supabaseClient.storage
        .from('avatars')
        .getPublicUrl(fileName);

      if (!publicUrlData) {
        return { success: false, error: 'Failed to get avatar URL' };
      }

      const avatarUrl = publicUrlData.publicUrl;

      // Update user profile
      const { error: updateError } = await supabaseClient
        .from('profiles')
        .update({
          avatar_url: avatarUrl,
          avatar_id: null, // Clear mapped avatar when uploading custom
        })
        .eq('id', userId);

      if (updateError) {
        console.error('Profile update error:', updateError);
        return { success: false, error: 'Failed to update profile' };
      }

      return { success: true, avatarUrl };
    } catch (error) {
      console.error('Avatar upload error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  /**
   * Set mapped avatar
   */
  async setMappedAvatar(
    avatarId: string,
    userId: string
  ): Promise<AvatarUploadResult> {
    try {
      if (!avatarMap[avatarId]) {
        return { success: false, error: 'Invalid avatar ID' };
      }

      // Update user profile
      const { error } = await supabaseClient
        .from('profiles')
        .update({
          avatar_id: avatarId,
          avatar_url: null, // Clear custom avatar when setting mapped
        })
        .eq('id', userId);

      if (error) {
        console.error('Profile update error:', error);
        return { success: false, error: 'Failed to update avatar' };
      }

      return { success: true, avatarUrl: avatarMap[avatarId] };
    } catch (error) {
      console.error('Set avatar error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    }
  }

  // =============================================================================
  // USER SETTINGS METHODS
  // =============================================================================

  /**
   * Get user settings
   */
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    try {
      const { data, error } = await supabaseClient
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No settings found, return default
          return this.getDefaultSettings();
        }
        console.error('Error fetching user settings:', error);
        return null;
      }

      return this.parseSettings(data);
    } catch (error) {
      console.error('Error in getUserSettings:', error);
      return null;
    }
  }

  /**
   * Update user settings
   */
  async updateUserSettings(
    userId: string,
    settings: Partial<UserSettings>
  ): Promise<boolean> {
    try {
      const { error } = await supabaseClient.from('user_settings').upsert({
        user_id: userId,
        ...this.flattenSettings(settings),
        updated_at: new Date().toISOString(),
      });

      if (error) {
        console.error('Error updating user settings:', error);
        toast.error('Failed to update settings');
        return false;
      }

      toast.success('Settings updated successfully');
      return true;
    } catch (error) {
      console.error('Error in updateUserSettings:', error);
      toast.error('Failed to update settings');
      return false;
    }
  }

  /**
   * Get default settings
   */
  private getDefaultSettings(): UserSettings {
    return {
      theme: 'system',
      language: 'en',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      notifications: {
        email: true,
        push: true,
        desktop: false,
      },
      privacy: {
        profileVisibility: 'public',
        showEmail: false,
        showPhone: false,
      },
    };
  }

  /**
   * Parse settings from database format
   */
  private parseSettings(data: any): UserSettings {
    return {
      theme: data.theme || 'system',
      language: data.language || 'en',
      timezone:
        data.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      notifications: data.email_notifications || {
        email: true,
        push: true,
        desktop: false,
      },
      privacy: data.privacy_settings || {
        profileVisibility: 'public',
        showEmail: false,
        showPhone: false,
      },
    };
  }

  /**
   * Flatten settings for database storage
   */
  private flattenSettings(settings: Partial<UserSettings>): any {
    return {
      theme: settings.theme,
      language: settings.language,
      timezone: settings.timezone,
      email_notifications: settings.notifications,
      privacy_settings: settings.privacy,
    };
  }

  /**
   * Send test notification
   */
  async sendTestNotification(
    type: 'email' | 'app' | 'sms'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // TODO: Implement actual test notification sending
      console.log(`Sending test ${type} notification`);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      return { success: true };
    } catch (error) {
      console.error('Error sending test notification:', error);
      return { success: false, error: String(error) };
    }
  }
}

// Export default instance for convenience
export const userService = UserService.getInstance();

// Export the class for static method usage
export default UserService;

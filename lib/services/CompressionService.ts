/**
 * CompressionService provides data compression and decompression functionality
 * using the browser's CompressionStream API with fallback to JSON stringification.
 */
export class CompressionService {
  private static instance: CompressionService;
  private hasCompressionSupport: boolean;

  private constructor() {
    this.hasCompressionSupport =
      typeof CompressionStream !== 'undefined' &&
      typeof DecompressionStream !== 'undefined';
  }

  static getInstance(): CompressionService {
    if (!CompressionService.instance) {
      CompressionService.instance = new CompressionService();
    }
    return CompressionService.instance;
  }

  private createReadableStream(data: Uint8Array): ReadableStream {
    return new ReadableStream({
      start(controller) {
        controller.enqueue(data);
        controller.close();
      },
    });
  }

  /**
   * Compresses data using GZIP compression
   */
  async compress(data: unknown): Promise<string> {
    if (!this.hasCompressionSupport) {
      return JSON.stringify(data);
    }

    try {
      const jsonString = JSON.stringify(data);
      const encoder = new TextEncoder();
      const encodedData = encoder.encode(jsonString);

      const compressedStream = this.createReadableStream(
        encodedData
      ).pipeThrough(new CompressionStream('gzip'));

      const compressedResponse = new Response(compressedStream);
      const compressedBlob = await compressedResponse.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(reader.error);
        reader.readAsDataURL(compressedBlob);
      });
    } catch (error) {
      console.warn('Compression failed, falling back to JSON:', error);
      return JSON.stringify(data);
    }
  }

  /**
   * Decompresses data that was compressed with the compress method
   */
  async decompress<T>(compressedData: string): Promise<T> {
    if (!this.hasCompressionSupport || !compressedData.startsWith('data:')) {
      return JSON.parse(compressedData);
    }

    try {
      const response = await fetch(compressedData);
      const blob = await response.blob();
      const decompressedStream = blob
        .stream()
        .pipeThrough(new DecompressionStream('gzip'));

      const decompressedResponse = new Response(decompressedStream);
      const decompressedText = await decompressedResponse.text();
      return JSON.parse(decompressedText);
    } catch (error) {
      console.warn('Decompression failed, attempting direct parse:', error);
      return JSON.parse(compressedData);
    }
  }

  /**
   * Estimates the compressed size of data without actually compressing it
   */
  estimateCompressedSize(data: unknown): number {
    const jsonString = JSON.stringify(data);
    // Estimate compression ratio (typical gzip achieves 60-80% compression)
    return Math.ceil(jsonString.length * 0.4); // Conservative estimate
  }
}

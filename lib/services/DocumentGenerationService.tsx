import { SignatureBlock } from '@/components/ux/comp/SignatureBlock';
import type { FormState } from '@/lib/contexts/FormStateContext';
import type { SignatureOptions } from '@/lib/services/SignatureOptions';
import { SignatureService } from '@/lib/services/SignatureService';
import {
  Document,
  PDFViewer,
  Page,
  StyleSheet,
  Text,
  View,
  pdf,
} from '@react-pdf/renderer';
import type { DocumentProps } from '@react-pdf/types';
import CryptoJS from 'crypto-js';
import {
  Document as DocxDocument,
  HeadingLevel,
  Packer,
  Paragraph,
} from 'docx';
import { ReactElement } from 'react';

export interface DocumentVariable {
  name: string;
  description: string;
  type: string;
  value?: string;
}

export interface DocumentTemplate {
  metadata: {
    id: string;
    version: string;
    name: string;
    description: string;
    category: 'contract' | 'agreement' | 'declaration' | 'notice' | 'form';
    jurisdiction: string;
    language: string;
    tags: string[];
    lastUpdated: Date;
    isPublished: boolean;
    baseTemplate?: string;
  };
  content: string;
  variables: DocumentVariable[];
}

export interface DocumentStyling {
  theme: 'professional' | 'light' | 'dark';
  fontFamily?: string;
  fontSize?: number;
  lineHeight?: number;
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  colors?: {
    primary: string;
    secondary: string;
    text: string;
    background: string;
  };
  signature?: {
    signatory: {
      name: string;
      role?: string;
    };
    timestamp: Date;
    validUntil?: Date;
  };
  customCss?: string;
  headerTemplate?: string;
  footerTemplate?: string;
  pageNumbering?: boolean;
  watermark?: {
    text: string;
    color: string;
    opacity: number;
  };
}

export interface EncryptionOptions {
  password: string;
  algorithm?: 'AES' | 'DES' | 'TripleDES';
  keySize?: 128 | 192 | 256;
}

export interface DocumentGenerationOptions {
  format: 'pdf' | 'docx' | 'html';
  styling: DocumentStyling;
  encryption?: EncryptionOptions;
  signature?: SignatureOptions;
}

const defaultStyling: DocumentStyling = {
  theme: 'professional',
  fontFamily: 'Helvetica',
  fontSize: 12,
  lineHeight: 1.5,
  margins: {
    top: 40,
    right: 40,
    bottom: 40,
    left: 40,
  },
  colors: {
    primary: '#1a73e8',
    secondary: '#4285f4',
    text: '#202124',
    background: '#ffffff',
  },
};

export class DocumentGenerationService {
  private static instance: DocumentGenerationService;
  private signatureService: SignatureService;

  private constructor() {
    this.signatureService = new SignatureService();
  }

  public static getInstance(): DocumentGenerationService {
    if (!DocumentGenerationService.instance) {
      DocumentGenerationService.instance = new DocumentGenerationService();
    }
    return DocumentGenerationService.instance;
  }

  private replaceVariables(
    content: string,
    variables: DocumentVariable[]
  ): string {
    let result = content;
    variables.forEach((variable) => {
      const placeholder = `{{${variable.name}}}`;
      result = result.replace(
        new RegExp(placeholder, 'g'),
        variable.value || ''
      );
    });
    return result;
  }

  private getDocumentStyles(theme: DocumentStyling['theme']) {
    return StyleSheet.create({
      page: {
        padding: 30,
        backgroundColor: theme === 'dark' ? '#333' : '#fff',
      },
      content: {
        flex: 1,
      },
      title: {
        fontSize: 24,
        marginBottom: 20,
        color: theme === 'dark' ? '#fff' : '#000',
      },
      body: {
        fontSize: 12,
        lineHeight: 1.5,
        color: theme === 'dark' ? '#fff' : '#333',
      },
    });
  }

  private generatePDFContent(
    template: DocumentTemplate,
    formState: FormState,
    options: DocumentGenerationOptions
  ): ReactElement<DocumentProps> {
    const variables = template.variables.map((v) => ({
      ...v,
      value: String(formState.formData[v.name] || ''),
    }));
    const content = this.replaceVariables(template.content, variables);
    const styles = this.getDocumentStyles(options.styling.theme);

    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <View style={styles.content}>
            <Text style={styles.title}>{template.metadata.name}</Text>
            <Text style={styles.body}>{content}</Text>
            {options.styling.signature && (
              <SignatureBlock
                metadata={{
                  signatureType: 'digital',
                  signedBy: options.styling.signature.signatory.name,
                  signedAt: options.styling.signature.timestamp.toISOString(),
                  validUntil:
                    options.styling.signature.validUntil?.toISOString(),
                  certificateInfo: JSON.stringify({
                    role: options.styling.signature.signatory.role,
                    timestamp: options.styling.signature.timestamp,
                    name: options.styling.signature.signatory.name,
                  }),
                }}
              />
            )}
          </View>
        </Page>
      </Document>
    );
  }

  private async generateDOCXContent(
    template: DocumentTemplate,
    formState: FormState,
    options: DocumentGenerationOptions
  ): Promise<Buffer> {
    const variables = template.variables.map((v) => ({
      ...v,
      value: String(formState.formData[v.name] || ''),
    }));
    const content = this.replaceVariables(template.content, variables);

    const signatureBlocks = options.styling.signature
      ? [
          new Paragraph({ text: '' }),
          new Paragraph({ text: '--- Digital Signature ---' }),
          new Paragraph({
            text: `Signed by: ${options.styling.signature.signatory.name}`,
          }),
          new Paragraph({
            text: `Role: ${options.styling.signature.signatory.role || 'N/A'}`,
          }),
          new Paragraph({
            text: `Date: ${options.styling.signature.timestamp.toLocaleString()}`,
          }),
          ...(options.styling.signature.validUntil
            ? [
                new Paragraph({
                  text: `Valid Until: ${options.styling.signature.validUntil.toLocaleString()}`,
                }),
              ]
            : []),
        ]
      : [];

    const doc = new DocxDocument({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              text: template.metadata.name,
              heading: HeadingLevel.HEADING_1,
            }),
            new Paragraph({ text: content }),
            ...signatureBlocks,
          ],
        },
      ],
    });

    return await Packer.toBuffer(doc);
  }

  private encryptDocument(
    content: string | Buffer,
    options: EncryptionOptions
  ): Buffer {
    const contentStr = Buffer.isBuffer(content)
      ? content.toString('base64')
      : content;

    const wordArray = CryptoJS.enc.Utf8.parse(contentStr);
    const encrypted = CryptoJS.AES.encrypt(
      wordArray,
      options.password
    ).toString();

    return Buffer.from(encrypted);
  }

  private generateHTMLContent(
    template: DocumentTemplate,
    formState: FormState,
    options: DocumentGenerationOptions
  ): string {
    const variables = template.variables.map((v) => ({
      ...v,
      value: String(formState.formData[v.name] || ''),
    }));
    const content = this.replaceVariables(template.content, variables);
    const styling = options.styling;

    const watermarkHtml = styling.watermark
      ? `<div class="watermark" style="
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(-45deg);
          font-size: 72px;
          opacity: ${styling.watermark.opacity || 0.2};
          color: ${styling.watermark.color || '#d0d0d0'};
          pointer-events: none;
          z-index: 1000;
          user-select: none;
        ">${styling.watermark.text}</div>`
      : '';

    const headerHtml = styling.headerTemplate
      ? `<header style="
          position: fixed;
          top: 0;
          width: 100%;
          padding: 10px ${styling.margins?.left || 40}px;
          background: ${styling.colors?.background || '#ffffff'};
          border-bottom: 1px solid #eaeaea;
        ">${styling.headerTemplate}</header>`
      : '';

    const footerHtml = styling.footerTemplate
      ? `<footer style="
          position: fixed;
          bottom: 0;
          width: 100%;
          padding: 10px ${styling.margins?.left || 40}px;
          background: ${styling.colors?.background || '#ffffff'};
          border-top: 1px solid #eaeaea;
          text-align: center;
        ">${styling.footerTemplate}</footer>`
      : '';

    const pageNumberHtml = styling.pageNumbering
      ? `<div class="page-number" style="
          position: fixed;
          bottom: 10px;
          right: 10px;
          font-size: 10px;
          color: ${styling.colors?.text || '#333333'};
        ">Page <span class="page"></span></div>`
      : '';

    const customStyles = styling.customCss
      ? `<style>${styling.customCss}</style>`
      : '';

    const signatureHtml = styling.signature
      ? `<div class="signature" style="
          margin-top: 30px;
          border-top: 1px solid #ccc;
          padding-top: 10px;
        ">
          <p><strong>Signed by:</strong> ${styling.signature.signatory.name}</p>
          ${styling.signature.signatory.role ? `<p><strong>Role:</strong> ${styling.signature.signatory.role}</p>` : ''}
          <p><strong>Date:</strong> ${styling.signature.timestamp.toLocaleDateString()}</p>
          ${styling.signature.validUntil ? `<p><strong>Valid Until:</strong> ${styling.signature.validUntil.toLocaleDateString()}</p>` : ''}
        </div>`
      : '';

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${template.metadata.name}</title>
        <style>
          body {
            font-family: ${styling.fontFamily || 'Helvetica, Arial, sans-serif'};
            font-size: ${styling.fontSize || 12}px;
            line-height: ${styling.lineHeight || 1.5};
            color: ${styling.colors?.text || '#202124'};
            background-color: ${styling.colors?.background || '#ffffff'};
            margin: ${styling.margins?.top || 40}px ${styling.margins?.right || 40}px ${styling.margins?.bottom || 40}px ${styling.margins?.left || 40}px;
            position: relative;
          }
          h1, h2, h3, h4, h5, h6 {
            color: ${styling.colors?.primary || '#1a73e8'};
          }
          a {
            color: ${styling.colors?.secondary || '#4285f4'};
          }
          .content {
            padding-top: ${headerHtml ? '60px' : '0'};
            padding-bottom: ${footerHtml ? '60px' : '0'};
          }
          @media print {
            .page-break {
              page-break-after: always;
            }
          }
        </style>
        ${customStyles}
      </head>
      <body>
        ${watermarkHtml}
        ${headerHtml}
        ${footerHtml}
        ${pageNumberHtml}
        <div class="content">
          <h1>${template.metadata.name}</h1>
          ${content}
          ${signatureHtml}
        </div>
      </body>
      </html>
    `;

    return html;
  }

  public async generateDocument(
    template: DocumentTemplate,
    formState: FormState,
    options: DocumentGenerationOptions
  ): Promise<Buffer | Blob | string> {
    const mergedOptions = {
      ...options,
      styling: { ...defaultStyling, ...options.styling },
    };

    let content: string | Buffer | ReactElement | Blob;
    switch (options.format) {
      case 'pdf':
        content = this.generatePDFContent(template, formState, mergedOptions);
        break;
      case 'docx':
        content = await this.generateDOCXContent(
          template,
          formState,
          mergedOptions
        );
        break;
      case 'html':
        content = this.generateHTMLContent(template, formState, mergedOptions);
        break;
      default:
        throw new Error(`Unsupported format: ${options.format}`);
    }

    if (options.encryption) {
      if (content instanceof Object && !(content instanceof Buffer)) {
        throw new Error('Cannot encrypt content before rendering');
      }
      return this.encryptDocument(
        content as string | Buffer,
        options.encryption
      );
    }

    if (options.format === 'pdf') {
      return await pdf(content as ReactElement<DocumentProps>).toBlob();
    }

    if (
      typeof content === 'string' ||
      content instanceof Buffer ||
      content instanceof Blob
    ) {
      return content;
    }

    throw new Error('Unexpected content type');
  }

  getPreviewComponent(
    template: DocumentTemplate,
    formState: FormState,
    styling: DocumentStyling = defaultStyling
  ): ReactElement {
    return (
      <PDFViewer width="100%" height="100%">
        {this.generatePDFContent(template, formState, {
          format: 'pdf',
          styling: { ...defaultStyling, ...styling },
        })}
      </PDFViewer>
    );
  }

  getHTMLPreviewComponent(
    template: DocumentTemplate,
    formState: FormState,
    styling: DocumentStyling = defaultStyling
  ): ReactElement {
    const htmlContent = this.generateHTMLContent(template, formState, {
      format: 'html',
      styling: { ...defaultStyling, ...styling },
    });

    return (
      <iframe
        title="HTML Document Preview"
        width="100%"
        height="100%"
        srcDoc={htmlContent}
        style={{ border: 'none' }}
      />
    );
  }
}

/**
 * Email Service - Enhanced with Comprehensive Resend API Integration
 *
 * Unified service for all email functionality including:
 * - Waitlist emails (welcome, admin notifications, updates)
 * - Document notifications (shared, updated, commented, signed)
 * - Authentication emails (verification, password reset)
 * - User communications
 *
 * Features:
 * - Complete Resend API integration according to official documentation
 * - Comprehensive logging of all API requests and responses
 * - Detailed error handling with HTTP status codes and error types
 * - Response data capture (email IDs, contact IDs, etc.)
 * - Rate limiting awareness and handling with sequential processing
 * - Proper authentication and domain verification
 * - Exponential backoff retry logic for rate limit errors
 */

import { emailConfig, resend } from '../emails/index';
import { DocumentNotificationEmail } from '../emails/templates/document-notification-email';
import { ResetPasswordEmail } from '../emails/templates/reset-password-email';
import { VerificationEmail } from '../emails/templates/verification-email';
import { WaitlistAdminNotificationEmail } from '../emails/templates/waitlist-admin-notification-email';
import { WaitlistWelcomeEmail } from '../emails/templates/waitlist-welcome-email';

// Rate limiting and configuration imports
import {
  EMAIL_OPERATION_TYPES,
  getEmailConfig,
  logEmailConfig,
} from '@/lib/config/email';
import { EmailRateLimiter } from '@/lib/utils/rateLimiter';

// Constants
const WAITLIST_AUDIENCE_ID = '157072ce-1d7d-47ed-9cec-b29d2c4d81d5';
const ADMIN_EMAIL = '<EMAIL>';

// Initialize email configuration
const emailConfigInstance = getEmailConfig();

// Unsubscribe URL generation
const generateUnsubscribeUrl = (
  email: string,
  type: 'waitlist' | 'notifications' | 'all' = 'all'
): string => {
  const baseUrl =
    process.env.NEXT_PUBLIC_APP_URL || 'https://forms.notamess.com';
  const encodedEmail = encodeURIComponent(email);
  return `${baseUrl}/unsubscribe?email=${encodedEmail}&type=${type}`;
};

// Generate unsubscribe headers for Resend
const generateUnsubscribeHeaders = (
  email: string,
  type: 'waitlist' | 'notifications' | 'all' = 'all'
) => {
  const unsubscribeUrl = generateUnsubscribeUrl(email, type);
  return {
    'List-Unsubscribe': `<${unsubscribeUrl}>`,
    'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
  };
};

// Resend API Response Types (based on official documentation)
export interface ResendEmailResponse {
  id: string;
}

export interface ResendContactResponse {
  object: 'contact';
  id: string;
}

export interface ResendAudienceResponse {
  object: 'audience';
  id: string;
  name: string;
  created_at: string;
}

export interface ResendErrorResponse {
  message: string;
  name: string;
}

export interface ResendApiError extends Error {
  status?: number;
  code?: string;
  response?: {
    status: number;
    data: ResendErrorResponse;
  };
}

// Enhanced Result Types with Resend Response Data
export interface EmailOperationResult {
  success: boolean;
  emailId?: string;
  contactId?: string;
  timestamp: string;
  error?: string;
  errorCode?: string;
  httpStatus?: number;
  rateLimitInfo?: {
    remaining?: number;
    reset?: number;
  };
}

export interface WaitlistEmailResult {
  userEmailSent: boolean;
  adminEmailSent: boolean;
  audienceAdded: boolean;
  userEmailId?: string;
  adminEmailId?: string;
  contactId?: string;
  errors: string[];
  timestamp: string;
  operations: EmailOperationResult[];
}

// Application Data Types
export interface WaitlistEmailData {
  userName: string;
  userEmail: string;
  role: 'user' | 'lawyer';
  waitlistStats?: {
    totalCount: number;
    userCount: number;
    lawyerCount: number;
    userPosition?: number;
  };
}

export interface DocumentNotificationData {
  email: string;
  documentLink: string;
  userName?: string;
  documentName: string;
  notificationType: 'shared' | 'updated' | 'commented' | 'signed';
  senderName: string;
  message?: string;
}

export interface AuthEmailData {
  email: string;
  userName?: string;
  verificationLink?: string;
  resetLink?: string;
}

export interface EmailResult {
  success: boolean;
  emailId?: string;
  timestamp: string;
  error?: string;
  errorCode?: string;
  httpStatus?: number;
}

// Logging Utilities
class EmailLogger {
  private static formatTimestamp(): string {
    return new Date().toISOString();
  }

  static logRequest(operation: string, params: any): void {
    console.log(
      `[RESEND API REQUEST] ${this.formatTimestamp()} - ${operation}`
    );
    console.log('Request Parameters:', JSON.stringify(params, null, 2));
  }

  static logResponse(operation: string, response: any): void {
    console.log(
      `[RESEND API RESPONSE] ${this.formatTimestamp()} - ${operation}`
    );
    console.log('Response Data:', JSON.stringify(response, null, 2));
  }

  static logError(operation: string, error: any): void {
    console.error(
      `[RESEND API ERROR] ${this.formatTimestamp()} - ${operation}`
    );
    console.error('Error Details:', JSON.stringify(error, null, 2));
    if (error.stack) {
      console.error('Stack Trace:', error.stack);
    }
  }

  static logSuccess(operation: string, result: any): void {
    console.log(
      `[RESEND API SUCCESS] ${this.formatTimestamp()} - ${operation}`
    );
    console.log('Success Result:', JSON.stringify(result, null, 2));
  }
}

/**
 * Unified Email Service Class
 */
export class EmailService {
  // =============================================================================
  // PRIVATE HELPER METHODS FOR RESEND API OPERATIONS
  // =============================================================================

  /**
   * Add user to Resend audience with comprehensive error handling and rate limiting
   */
  private static async addToAudience(
    data: WaitlistEmailData
  ): Promise<EmailOperationResult> {
    const timestamp = new Date().toISOString();
    const operation = 'addToAudience';

    const requestParams = {
      audienceId: WAITLIST_AUDIENCE_ID,
      email: data.userEmail,
      firstName: data.userName.split(' ')[0],
      lastName: data.userName.split(' ').slice(1).join(' ') || '',
      unsubscribed: false,
    };

    EmailLogger.logRequest(operation, requestParams);

    try {
      // Use rate limiter with retry logic for the actual API call
      const response = await EmailRateLimiter.executeWithRetry(
        async () => {
          const apiResponse = await resend.contacts.create(requestParams);
          EmailLogger.logResponse(operation, apiResponse);
          return apiResponse;
        },
        {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 10000,
          backoffMultiplier: 2,
        }
      );

      if (response.data && response.data.id) {
        return {
          success: true,
          contactId: response.data.id,
          timestamp,
        };
      } else {
        throw new Error('No contact ID returned from Resend API');
      }
    } catch (error: any) {
      const result: EmailOperationResult = {
        success: false,
        timestamp,
        error: error.message || String(error),
        errorCode: error.code,
        httpStatus: error.response?.status,
      };

      // Parse rate limit information
      const rateLimitInfo = EmailRateLimiter.parseRateLimitInfo(error);
      if (rateLimitInfo) {
        result.rateLimitInfo = rateLimitInfo;
        EmailRateLimiter.logRateLimitInfo(operation, rateLimitInfo);
      }

      EmailLogger.logError(operation, error);
      return result;
    }
  }

  /**
   * Send welcome email with comprehensive error handling and rate limiting
   */
  private static async sendWelcomeEmail(
    data: WaitlistEmailData
  ): Promise<EmailOperationResult> {
    const timestamp = new Date().toISOString();
    const operation = 'sendWelcomeEmail';

    const unsubscribeUrl = generateUnsubscribeUrl(data.userEmail, 'waitlist');
    const unsubscribeHeaders = generateUnsubscribeHeaders(
      data.userEmail,
      'waitlist'
    );

    const requestParams = {
      from: emailConfig.from,
      to: data.userEmail,
      subject: '🎉 Welcome to the Notamess Forms Waitlist!',
      react: WaitlistWelcomeEmail({
        userName: data.userName,
        userEmail: data.userEmail,
        role: data.role,
        waitlistPosition: data.waitlistStats?.userPosition,
        unsubscribeUrl,
      }),
      headers: unsubscribeHeaders,
      tags: [
        { name: 'category', value: 'waitlist' },
        { name: 'type', value: 'welcome' },
        { name: 'role', value: data.role },
      ],
    };

    EmailLogger.logRequest(operation, requestParams);

    try {
      // Use rate limiter with retry logic for the actual API call
      const response = await EmailRateLimiter.executeWithRetry(
        async () => {
          const apiResponse = await resend.emails.send(requestParams);
          EmailLogger.logResponse(operation, apiResponse);
          return apiResponse;
        },
        {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 10000,
          backoffMultiplier: 2,
        }
      );

      // Handle Resend API response structure
      if (response.data && response.data.id) {
        return {
          success: true,
          emailId: response.data.id,
          timestamp,
        };
      } else {
        // Log the actual response structure for debugging
        console.error(
          'Unexpected Resend API response structure:',
          JSON.stringify(response, null, 2)
        );
        throw new Error(
          `No email ID returned from Resend API. Response: ${JSON.stringify(response)}`
        );
      }
    } catch (error: any) {
      const result: EmailOperationResult = {
        success: false,
        timestamp,
        error: error.message || String(error),
        errorCode: error.code,
        httpStatus: error.response?.status,
      };

      // Parse rate limit information
      const rateLimitInfo = EmailRateLimiter.parseRateLimitInfo(error);
      if (rateLimitInfo) {
        result.rateLimitInfo = rateLimitInfo;
        EmailRateLimiter.logRateLimitInfo(operation, rateLimitInfo);
      }

      EmailLogger.logError(operation, error);
      return result;
    }
  }

  /**
   * Send admin notification email with comprehensive error handling and rate limiting
   */
  private static async sendAdminNotification(
    data: WaitlistEmailData
  ): Promise<EmailOperationResult> {
    const timestamp = new Date().toISOString();
    const operation = 'sendAdminNotification';

    const signupTime = new Date().toLocaleString('en-US', {
      timeZone: 'America/New_York',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    const requestParams = {
      from: emailConfig.from,
      to: ADMIN_EMAIL,
      subject: `🚀 New Waitlist Signup: ${data.userName} (${data.role})`,
      react: WaitlistAdminNotificationEmail({
        userName: data.userName,
        userEmail: data.userEmail,
        role: data.role,
        totalWaitlistCount: data.waitlistStats?.totalCount || 0,
        userCount: data.waitlistStats?.userCount || 0,
        lawyerCount: data.waitlistStats?.lawyerCount || 0,
        signupTime,
        unsubscribeUrl: undefined, // Admin emails don't need unsubscribe
      }),
      tags: [
        { name: 'category', value: 'waitlist' },
        { name: 'type', value: 'admin-notification' },
        { name: 'role', value: data.role },
      ],
    };

    EmailLogger.logRequest(operation, requestParams);

    try {
      // Use rate limiter with retry logic for the actual API call
      const response = await EmailRateLimiter.executeWithRetry(
        async () => {
          const apiResponse = await resend.emails.send(requestParams);
          EmailLogger.logResponse(operation, apiResponse);
          return apiResponse;
        },
        {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 10000,
          backoffMultiplier: 2,
        }
      );

      // Handle Resend API response structure
      if (response.data && response.data.id) {
        return {
          success: true,
          emailId: response.data.id,
          timestamp,
        };
      } else {
        // Log the actual response structure for debugging
        console.error(
          'Unexpected Resend API response structure:',
          JSON.stringify(response, null, 2)
        );
        throw new Error(
          `No email ID returned from Resend API. Response: ${JSON.stringify(response)}`
        );
      }
    } catch (error: any) {
      const result: EmailOperationResult = {
        success: false,
        timestamp,
        error: error.message || String(error),
        errorCode: error.code,
        httpStatus: error.response?.status,
      };

      // Parse rate limit information
      const rateLimitInfo = EmailRateLimiter.parseRateLimitInfo(error);
      if (rateLimitInfo) {
        result.rateLimitInfo = rateLimitInfo;
        EmailRateLimiter.logRateLimitInfo(operation, rateLimitInfo);
      }

      EmailLogger.logError(operation, error);
      return result;
    }
  }

  // =============================================================================
  // WAITLIST EMAIL METHODS
  // =============================================================================

  /**
   * Send welcome email to new waitlist member and admin notification
   * Enhanced with comprehensive Resend API integration, logging, and rate limiting
   */
  static async sendWaitlistEmails(
    data: WaitlistEmailData
  ): Promise<WaitlistEmailResult> {
    const timestamp = new Date().toISOString();
    const results: WaitlistEmailResult = {
      userEmailSent: false,
      adminEmailSent: false,
      audienceAdded: false,
      errors: [],
      timestamp,
      operations: [],
    };

    EmailLogger.logRequest('sendWaitlistEmails', {
      operation: 'Waitlist Email Process (Sequential with Rate Limiting)',
      userEmail: data.userEmail,
      userName: data.userName,
      role: data.role,
      waitlistStats: data.waitlistStats,
      timestamp,
    });

    // Log email configuration for debugging
    try {
      logEmailConfig();
    } catch (error) {
      console.warn('Failed to log email config:', error);
    }

    // Validate Resend API key
    if (!process.env.RESEND_API_KEY) {
      const error = 'RESEND_API_KEY environment variable is not set';
      EmailLogger.logError('sendWaitlistEmails', { error });
      results.errors.push(error);
      return results;
    }

    // Validate required data
    if (!data.userEmail || !data.userName || !data.role) {
      const error =
        'Missing required waitlist data (userEmail, userName, or role)';
      EmailLogger.logError('sendWaitlistEmails', { error, data });
      results.errors.push(error);
      return results;
    }

    try {
      // Get email configuration for rate limiting
      const config = getEmailConfig();
      const operationDelay = config.rateLimit.operationDelay;

      console.log(
        `[WAITLIST-EMAILS] Starting sequential email operations with ${operationDelay}ms delays`
      );

      // Define operations to execute sequentially with rate limiting
      const operations = [
        {
          name: EMAIL_OPERATION_TYPES.ADD_TO_AUDIENCE,
          operation: () => this.addToAudience(data),
          processResult: (operation: EmailOperationResult) => {
            results.operations.push(operation);
            if (operation.success) {
              results.audienceAdded = true;
              results.contactId = operation.contactId;
              EmailLogger.logSuccess('addToAudience', {
                contactId: operation.contactId,
                email: data.userEmail,
              });
            } else {
              results.errors.push(
                `Failed to add to audience: ${operation.error}`
              );
              EmailLogger.logError('addToAudience', {
                error: operation.error,
                errorCode: operation.errorCode,
                httpStatus: operation.httpStatus,
              });
            }
          },
        },
        {
          name: EMAIL_OPERATION_TYPES.SEND_WELCOME,
          operation: () => this.sendWelcomeEmail(data),
          processResult: (operation: EmailOperationResult) => {
            results.operations.push(operation);
            if (operation.success) {
              results.userEmailSent = true;
              results.userEmailId = operation.emailId;
              EmailLogger.logSuccess('sendWelcomeEmail', {
                emailId: operation.emailId,
                email: data.userEmail,
              });
            } else {
              results.errors.push(
                `Failed to send user email: ${operation.error}`
              );
              EmailLogger.logError('sendWelcomeEmail', {
                error: operation.error,
                errorCode: operation.errorCode,
                httpStatus: operation.httpStatus,
              });
            }
          },
        },
        {
          name: EMAIL_OPERATION_TYPES.SEND_ADMIN_NOTIFICATION,
          operation: () => this.sendAdminNotification(data),
          processResult: (operation: EmailOperationResult) => {
            results.operations.push(operation);
            if (operation.success) {
              results.adminEmailSent = true;
              results.adminEmailId = operation.emailId;
              EmailLogger.logSuccess('sendAdminNotification', {
                emailId: operation.emailId,
                adminEmail: ADMIN_EMAIL,
              });
            } else {
              results.errors.push(
                `Failed to send admin email: ${operation.error}`
              );
              EmailLogger.logError('sendAdminNotification', {
                error: operation.error,
                errorCode: operation.errorCode,
                httpStatus: operation.httpStatus,
              });
            }
          },
        },
      ];

      // Execute operations sequentially with rate limiting
      for (let i = 0; i < operations.length; i++) {
        const { name, operation, processResult } = operations[i];

        // Add delay before each operation except the first one
        if (i > 0) {
          console.log(
            `[WAITLIST-EMAILS] Waiting ${operationDelay}ms before ${name}`
          );
          await EmailRateLimiter.delay(operationDelay);
        }

        console.log(`[WAITLIST-EMAILS] Executing ${name}`);

        try {
          const operationResult = await EmailRateLimiter.executeEmailOperation(
            operation,
            name
          );
          processResult(operationResult);
        } catch (error: any) {
          console.error(`[WAITLIST-EMAILS] Failed ${name}:`, error);

          // Create a failed operation result
          const failedOperation: EmailOperationResult = {
            success: false,
            timestamp: new Date().toISOString(),
            error: error.message || String(error),
            errorCode: error.code,
            httpStatus: error.response?.status,
          };

          // Parse rate limit info if available
          const rateLimitInfo = EmailRateLimiter.parseRateLimitInfo(error);
          if (rateLimitInfo) {
            failedOperation.rateLimitInfo = rateLimitInfo;
            EmailRateLimiter.logRateLimitInfo(name, rateLimitInfo);
          }

          processResult(failedOperation);
        }
      }

      // Log final results summary
      EmailLogger.logSuccess('sendWaitlistEmails', {
        summary: {
          userEmailSent: results.userEmailSent,
          adminEmailSent: results.adminEmailSent,
          audienceAdded: results.audienceAdded,
          totalOperations: results.operations.length,
          successfulOperations: results.operations.filter((op) => op.success)
            .length,
          failedOperations: results.operations.filter((op) => !op.success)
            .length,
          errors: results.errors,
        },
        userEmailId: results.userEmailId,
        adminEmailId: results.adminEmailId,
        contactId: results.contactId,
      });

      return results;
    } catch (error) {
      console.error('Unexpected error in waitlist email service:', error);
      EmailLogger.logError('sendWaitlistEmails', error);
      results.errors.push(`Unexpected error: ${error}`);
      return results;
    }
  }

  /**
   * Send update email to all waitlist members
   */
  static async sendWaitlistUpdate(
    subject: string,
    content: string,
    includeRoles: ('user' | 'lawyer')[] = ['user', 'lawyer']
  ): Promise<EmailResult> {
    try {
      // This would typically use Resend's broadcast feature
      // For now, we'll return a placeholder
      console.log('Waitlist update would be sent:', {
        subject,
        content,
        includeRoles,
      });

      // TODO: Implement broadcast email functionality
      // This could use Resend's audience broadcast feature when available

      return { success: true, timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Error sending waitlist update:', error);
      return {
        success: false,
        error: String(error),
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Remove user from waitlist audience
   */
  static async removeFromWaitlistAudience(email: string): Promise<EmailResult> {
    try {
      await resend.contacts.remove({
        audienceId: WAITLIST_AUDIENCE_ID,
        email,
      });

      console.log(`Removed ${email} from Resend audience`);
      return { success: true, timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Error removing from audience:', error);
      return {
        success: false,
        error: String(error),
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get waitlist audience stats from Resend
   */
  static async getWaitlistAudienceStats(): Promise<{
    success: boolean;
    stats?: {
      totalContacts: number;
    };
    error?: string;
  }> {
    try {
      const audience = await resend.audiences.get(WAITLIST_AUDIENCE_ID);

      return {
        success: true,
        stats: {
          totalContacts: audience.data?.object === 'audience' ? 0 : 0, // Adjust based on actual API response
        },
      };
    } catch (error) {
      console.error('Error getting audience stats:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * Send launch notification to waitlist
   */
  static async sendLaunchNotification(
    launchUrl: string,
    earlyAccessCode?: string
  ): Promise<EmailResult> {
    try {
      // TODO: Implement launch notification
      // This would send a special email to all waitlist members
      // announcing the launch with their early access details

      console.log('Launch notification would be sent:', {
        launchUrl,
        earlyAccessCode,
      });
      return { success: true, timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Error sending launch notification:', error);
      return {
        success: false,
        error: String(error),
        timestamp: new Date().toISOString(),
      };
    }
  }

  // =============================================================================
  // DOCUMENT NOTIFICATION EMAIL METHODS
  // =============================================================================

  /**
   * Send document notification email
   */
  static async sendDocumentNotification(
    data: DocumentNotificationData
  ): Promise<EmailResult> {
    try {
      // Generate subject based on notification type
      let subject = '';
      switch (data.notificationType) {
        case 'shared':
          subject = `${data.senderName} shared a document with you`;
          break;
        case 'updated':
          subject = `${data.senderName} updated "${data.documentName}"`;
          break;
        case 'commented':
          subject = `${data.senderName} commented on "${data.documentName}"`;
          break;
        case 'signed':
          subject = `${data.senderName} signed "${data.documentName}"`;
          break;
        default:
          subject = `Notification about "${data.documentName}"`;
      }

      const unsubscribeUrl = generateUnsubscribeUrl(
        data.email,
        'notifications'
      );
      const unsubscribeHeaders = generateUnsubscribeHeaders(
        data.email,
        'notifications'
      );

      await resend.emails.send({
        from: emailConfig.from,
        to: data.email,
        subject: subject,
        react: DocumentNotificationEmail({
          documentLink: data.documentLink,
          userName: data.userName || data.email.split('@')[0],
          documentName: data.documentName,
          notificationType: data.notificationType,
          senderName: data.senderName,
          message: data.message,
          unsubscribeUrl,
        }),
        headers: unsubscribeHeaders,
        tags: [
          { name: 'category', value: 'document' },
          { name: 'type', value: data.notificationType },
        ],
      });

      console.log(`Document notification email sent to ${data.email}`);
      return { success: true, timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Error sending document notification email:', error);
      return {
        success: false,
        error: String(error),
        timestamp: new Date().toISOString(),
      };
    }
  }

  // =============================================================================
  // AUTHENTICATION EMAIL METHODS
  // =============================================================================

  /**
   * Send verification email
   */
  static async sendVerificationEmail(
    data: AuthEmailData
  ): Promise<EmailResult> {
    try {
      if (!data.verificationLink) {
        throw new Error('Verification link is required');
      }

      // Verification emails typically don't need unsubscribe, but we'll include the parameter
      await resend.emails.send({
        from: emailConfig.from,
        to: data.email,
        subject: 'Verify your email address',
        react: VerificationEmail({
          verificationLink: data.verificationLink,
          userName: data.userName || data.email.split('@')[0],
          unsubscribeUrl: undefined, // Verification emails don't need unsubscribe
        }),
        tags: [
          { name: 'category', value: 'auth' },
          { name: 'type', value: 'verification' },
        ],
      });

      console.log(`Verification email sent to ${data.email}`);
      return { success: true, timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Error sending verification email:', error);
      return {
        success: false,
        error: String(error),
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Send password reset email
   */
  static async sendPasswordResetEmail(
    data: AuthEmailData
  ): Promise<EmailResult> {
    try {
      if (!data.resetLink) {
        throw new Error('Reset link is required');
      }

      // Password reset emails typically don't need unsubscribe, but we'll include the parameter
      await resend.emails.send({
        from: emailConfig.from,
        to: data.email,
        subject: 'Reset your password',
        react: ResetPasswordEmail({
          resetLink: data.resetLink,
          userName: data.userName || data.email.split('@')[0],
          unsubscribeUrl: undefined, // Password reset emails don't need unsubscribe
        }),
        tags: [
          { name: 'category', value: 'auth' },
          { name: 'type', value: 'password-reset' },
        ],
      });

      console.log(`Password reset email sent to ${data.email}`);
      return { success: true, timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return {
        success: false,
        error: String(error),
        timestamp: new Date().toISOString(),
      };
    }
  }

  // =============================================================================
  // CONFIGURATION AND VALIDATION METHODS
  // =============================================================================

  /**
   * Validate Resend configuration and test connectivity
   */
  static async validateConfiguration(): Promise<{
    success: boolean;
    details: {
      apiKeyPresent: boolean;
      domainConfigured: boolean;
      audienceIdPresent: boolean;
      adminEmailPresent: boolean;
    };
    errors: string[];
  }> {
    const timestamp = new Date().toISOString();
    const operation = 'validateConfiguration';

    EmailLogger.logRequest(operation, {
      timestamp,
      checking: ['API_KEY', 'EMAIL_CONFIG', 'AUDIENCE_ID', 'ADMIN_EMAIL'],
    });

    const result = {
      success: true,
      details: {
        apiKeyPresent: !!process.env.RESEND_API_KEY,
        domainConfigured:
          !!emailConfig.from && emailConfig.from.includes('forms.notamess.com'),
        audienceIdPresent: !!WAITLIST_AUDIENCE_ID,
        adminEmailPresent: !!ADMIN_EMAIL,
      },
      errors: [] as string[],
    };

    // Check API key
    if (!result.details.apiKeyPresent) {
      result.errors.push('RESEND_API_KEY environment variable is not set');
      result.success = false;
    }

    // Check domain configuration
    if (!result.details.domainConfigured) {
      result.errors.push(
        'Email domain is not configured to use forms.notamess.com'
      );
      result.success = false;
    }

    // Check audience ID
    if (!result.details.audienceIdPresent) {
      result.errors.push('Waitlist audience ID is not configured');
      result.success = false;
    }

    // Check admin email
    if (!result.details.adminEmailPresent) {
      result.errors.push('Admin email is not configured');
      result.success = false;
    }

    // Test API connectivity if basic config is valid
    if (result.success) {
      try {
        // Test by getting audience info
        const audienceResponse =
          await resend.audiences.get(WAITLIST_AUDIENCE_ID);
        EmailLogger.logResponse(operation, {
          audienceTest: 'SUCCESS',
          audienceData: audienceResponse.data,
        });
      } catch (error: any) {
        result.errors.push(`API connectivity test failed: ${error.message}`);
        result.success = false;
        EmailLogger.logError(operation, error);
      }
    }

    if (result.success) {
      EmailLogger.logSuccess(operation, result);
    } else {
      EmailLogger.logError(operation, result);
    }

    return result;
  }

  // =============================================================================
  // CONVENIENCE METHODS (for backward compatibility)
  // =============================================================================

  /**
   * Legacy method for waitlist emails (for backward compatibility)
   * @deprecated Use sendWaitlistEmails instead
   */
  static async sendWaitlistWelcomeEmail(
    data: WaitlistEmailData
  ): Promise<WaitlistEmailResult> {
    return this.sendWaitlistEmails(data);
  }
}

// Export default instance for convenience
export const emailService = new EmailService();

// Export the class for static method usage
export default EmailService;

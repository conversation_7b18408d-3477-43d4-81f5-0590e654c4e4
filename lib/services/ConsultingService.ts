import {
  Attachment,
  Communication,
  ConsultingRequest,
  ConsultingSession,
  RequestPriority,
  RequestStatus,
} from '@/types';
import { EnterpriseService } from './EnterpriseService';
import { notificationService } from './NotificationService';

/**
 * Service for managing consulting requests and sessions
 */
export class ConsultingService {
  private static instance: ConsultingService;
  private notificationService: typeof notificationService;
  private enterpriseService: EnterpriseService;

  // In-memory storage for demo purposes
  // In production, this would use a database
  private consultingRequests: Map<string, ConsultingRequest> = new Map();
  private communications: Map<string, Communication> = new Map();
  private sessions: Map<string, ConsultingSession> = new Map();
  private attachments: Map<string, Attachment> = new Map();

  private constructor() {
    this.notificationService = notificationService;
    this.enterpriseService = EnterpriseService.getInstance();
    this.initializeDummyData();
  }

  public static getInstance(): ConsultingService {
    if (!ConsultingService.instance) {
      ConsultingService.instance = new ConsultingService();
    }
    return ConsultingService.instance;
  }

  /**
   * Create a new consulting request
   */
  public async createConsultingRequest(
    organizationId: string,
    requesterId: string,
    title: string,
    description: string,
    requirementDetails: string,
    priority: RequestPriority = 'medium',
    attachmentIds: string[] = []
  ): Promise<ConsultingRequest | null> {
    // Check if user has permission
    const orgAccess =
      this.enterpriseService.hasOrganizationAdminAccess(organizationId);
    if (!orgAccess) {
      this.notificationService.showToast(
        'You do not have permission to create consulting requests for this organization',
        { type: 'error' }
      );
      return null;
    }

    const requestId = `req_${Math.random().toString(36).substring(2, 15)}`;

    // Get attachments
    const attachments: Attachment[] = [];
    for (const attachmentId of attachmentIds) {
      const attachment = this.attachments.get(attachmentId);
      if (attachment) {
        attachments.push(attachment);
      }
    }

    const request: ConsultingRequest = {
      id: requestId,
      organizationId,
      requesterId,
      title,
      description,
      requirementDetails,
      priority,
      status: 'pending',
      attachments,
      communications: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store the request
    this.consultingRequests.set(requestId, request);

    this.notificationService.showToast(
      'Consulting request created successfully',
      {
        type: 'success',
      }
    );
    return request;
  }

  /**
   * Get a consulting request by ID
   */
  public async getConsultingRequest(
    requestId: string
  ): Promise<ConsultingRequest | null> {
    const request = this.consultingRequests.get(requestId);
    if (!request) {
      return null;
    }

    // Check if user has access
    const orgAccess = this.enterpriseService.hasOrganizationAdminAccess(
      request.organizationId
    );
    if (!orgAccess) {
      // Check if user is a team member
      const userOrgs = await this.enterpriseService.getUserOrganizations();
      const hasAccess = userOrgs.some(
        (org) => org.id === request.organizationId
      );

      if (!hasAccess) {
        this.notificationService.showToast(
          'You do not have permission to view this consulting request'
        );
        return null;
      }
    }

    return request;
  }

  /**
   * Get all consulting requests for an organization
   */
  public async getOrganizationRequests(
    organizationId: string
  ): Promise<ConsultingRequest[]> {
    // Check if user has access
    const orgAccess =
      this.enterpriseService.hasOrganizationAdminAccess(organizationId);
    if (!orgAccess) {
      // Check if user is a team member
      const userOrgs = await this.enterpriseService.getUserOrganizations();
      const hasAccess = userOrgs.some((org) => org.id === organizationId);

      if (!hasAccess) {
        this.notificationService.showToast(
          'You do not have permission to view consulting requests for this organization'
        );
        return [];
      }
    }

    const requests: ConsultingRequest[] = [];
    for (const request of this.consultingRequests.values()) {
      if (request.organizationId === organizationId) {
        requests.push(request);
      }
    }

    return requests.sort((a, b) => {
      // Sort by status (pending first, then in_progress, then others)
      if (a.status === 'pending' && b.status !== 'pending') return -1;
      if (a.status !== 'pending' && b.status === 'pending') return 1;
      if (a.status === 'in_progress' && b.status !== 'in_progress') return -1;
      if (a.status !== 'in_progress' && b.status === 'in_progress') return 1;

      // Then sort by priority
      const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      }

      // Finally sort by created date (newest first)
      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  }

  /**
   * Update a consulting request
   */
  public async updateConsultingRequest(
    requestId: string,
    updates: Partial<ConsultingRequest>
  ): Promise<ConsultingRequest | null> {
    const request = this.consultingRequests.get(requestId);
    if (!request) {
      this.notificationService.showToast('Consulting request not found', {
        type: 'error',
      });
      return null;
    }

    // Check if user has permission
    const orgAccess = this.enterpriseService.hasOrganizationAdminAccess(
      request.organizationId
    );
    if (!orgAccess) {
      this.notificationService.showToast(
        'You do not have permission to update this consulting request',
        { type: 'error' }
      );
      return null;
    }

    // Protect certain fields from being updated
    const protectedFields = [
      'id',
      'organizationId',
      'requesterId',
      'createdAt',
      'communications',
    ];
    const safeUpdates = { ...updates };

    for (const field of protectedFields) {
      delete safeUpdates[field as keyof typeof safeUpdates];
    }

    // Update the request
    const updatedRequest = {
      ...request,
      ...safeUpdates,
      updatedAt: new Date(),
    };

    this.consultingRequests.set(requestId, updatedRequest);
    this.notificationService.showToast(
      'Consulting request updated successfully',
      {
        type: 'success',
      }
    );

    return updatedRequest;
  }

  /**
   * Update the status of a consulting request
   */
  public async updateRequestStatus(
    requestId: string,
    newStatus: RequestStatus,
    consultantId?: string
  ): Promise<ConsultingRequest | null> {
    const request = this.consultingRequests.get(requestId);
    if (!request) {
      this.notificationService.showToast('Consulting request not found', {
        type: 'error',
      });
      return null;
    }

    // Check if user has permission
    const orgAccess = this.enterpriseService.hasOrganizationAdminAccess(
      request.organizationId
    );
    if (!orgAccess) {
      this.notificationService.showToast(
        'You do not have permission to update this consulting request',
        { type: 'error' }
      );
      return null;
    }

    // Update the request status
    const updatedRequest = {
      ...request,
      status: newStatus,
      updatedAt: new Date(),
    };

    // If status is 'accepted' or 'in_progress', update the assigned consultant
    if (
      (newStatus === 'accepted' || newStatus === 'in_progress') &&
      consultantId
    ) {
      updatedRequest.assignedConsultantId = consultantId;
    }

    // If status is 'completed', set the completedAt date
    if (newStatus === 'completed') {
      updatedRequest.completedAt = new Date();
    }

    this.consultingRequests.set(requestId, updatedRequest);
    this.notificationService.showToast(
      `Consulting request status updated to ${newStatus}`,
      { type: 'success' }
    );

    return updatedRequest;
  }

  /**
   * Add a communication/message to a consulting request
   */
  public async addCommunication(
    requestId: string,
    senderId: string,
    message: string,
    attachmentIds: string[] = []
  ): Promise<Communication | null> {
    const request = this.consultingRequests.get(requestId);
    if (!request) {
      this.notificationService.showToast('Consulting request not found', {
        type: 'error',
      });
      return null;
    }

    // Check if user has access to the organization
    const userOrgs = await this.enterpriseService.getUserOrganizations();
    const hasAccess = userOrgs.some((org) => org.id === request.organizationId);

    if (!hasAccess) {
      this.notificationService.showToast(
        'You do not have permission to add communications to this request',
        { type: 'error' }
      );
      return null;
    }

    // Get attachments
    const attachments: Attachment[] = [];
    for (const attachmentId of attachmentIds) {
      const attachment = this.attachments.get(attachmentId);
      if (attachment) {
        attachments.push(attachment);
      }
    }

    const communicationId = `comm_${Math.random().toString(36).substring(2, 15)}`;

    const communication: Communication = {
      id: communicationId,
      requestId,
      senderId,
      message,
      attachments,
      createdAt: new Date(),
    };

    // Store the communication
    this.communications.set(communicationId, communication);

    // Update the request with the new communication
    request.communications.push(communication);
    request.updatedAt = new Date();
    this.consultingRequests.set(requestId, request);

    this.notificationService.showToast('Message sent successfully', {
      type: 'success',
    });
    return communication;
  }

  /**
   * Create a consulting session
   */
  public async createConsultingSession(
    requestId: string,
    consultantId: string,
    startTime: Date
  ): Promise<ConsultingSession | null> {
    const request = this.consultingRequests.get(requestId);
    if (!request) {
      this.notificationService.showToast('Consulting request not found', {
        type: 'error',
      });
      return null;
    }

    // Check if user has permission
    const orgAccess = this.enterpriseService.hasOrganizationAdminAccess(
      request.organizationId
    );
    if (!orgAccess) {
      this.notificationService.showToast(
        'You do not have permission to create sessions for this request',
        { type: 'error' }
      );
      return null;
    }

    const sessionId = `session_${Math.random().toString(36).substring(2, 15)}`;

    const session: ConsultingSession = {
      id: sessionId,
      requestId,
      consultantId,
      startTime,
    };

    // Store the session
    this.sessions.set(sessionId, session);

    // Update the request status to in_progress if not already
    if (request.status === 'pending' || request.status === 'accepted') {
      await this.updateRequestStatus(requestId, 'in_progress', consultantId);
    }

    this.notificationService.showToast(
      'Consulting session created successfully',
      {
        type: 'success',
      }
    );
    return session;
  }

  /**
   * End a consulting session
   */
  public async endConsultingSession(
    sessionId: string,
    notes?: string,
    outcome?: string
  ): Promise<ConsultingSession | null> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      this.notificationService.showToast('Consulting session not found', {
        type: 'error',
      });
      return null;
    }

    const request = this.consultingRequests.get(session.requestId);
    if (!request) {
      this.notificationService.showToast(
        'Associated consulting request not found',
        { type: 'error' }
      );
      return null;
    }

    // Check if user has permission
    const orgAccess = this.enterpriseService.hasOrganizationAdminAccess(
      request.organizationId
    );
    if (!orgAccess) {
      this.notificationService.showToast(
        'You do not have permission to update this session',
        { type: 'error' }
      );
      return null;
    }

    // Update the session
    const updatedSession = {
      ...session,
      endTime: new Date(),
      notes,
      outcome,
    };

    this.sessions.set(sessionId, updatedSession);
    this.notificationService.showToast(
      'Consulting session ended successfully',
      {
        type: 'success',
      }
    );

    return updatedSession;
  }

  /**
   * Get all sessions for a consulting request
   */
  public async getRequestSessions(
    requestId: string
  ): Promise<ConsultingSession[]> {
    const request = this.consultingRequests.get(requestId);
    if (!request) {
      this.notificationService.showToast('Consulting request not found', {
        type: 'error',
      });
      return [];
    }

    // Check if user has access to the organization
    const userOrgs = await this.enterpriseService.getUserOrganizations();
    const hasAccess = userOrgs.some((org) => org.id === request.organizationId);

    if (!hasAccess) {
      this.notificationService.showToast(
        'You do not have permission to view sessions for this request',
        { type: 'error' }
      );
      return [];
    }

    const requestSessions: ConsultingSession[] = [];
    for (const session of this.sessions.values()) {
      if (session.requestId === requestId) {
        requestSessions.push(session);
      }
    }

    return requestSessions.sort(
      (a, b) => b.startTime.getTime() - a.startTime.getTime()
    );
  }

  /**
   * Upload an attachment for a consulting request
   */
  public async uploadAttachment(
    filename: string,
    fileSize: number,
    fileType: string,
    uploadedBy: string
  ): Promise<Attachment> {
    const attachmentId = `attachment_${Math.random().toString(36).substring(2, 15)}`;

    const attachment: Attachment = {
      id: attachmentId,
      filename,
      fileUrl: `https://example.com/files/${attachmentId}/${filename}`, // Simulated URL
      fileSize,
      fileType,
      createdAt: new Date(),
      uploadedBy,
    };

    // Store the attachment
    this.attachments.set(attachmentId, attachment);

    return attachment;
  }

  /**
   * Initialize dummy data for demo purposes
   */
  private initializeDummyData(): void {
    // Create a demo consulting request
    const demoRequestId = 'req_demo';
    const demoRequest: ConsultingRequest = {
      id: demoRequestId,
      organizationId: 'org_demo',
      requesterId: 'user_demo',
      title: 'Custom Template Design',
      description: 'Need help designing a custom template for legal documents',
      requirementDetails:
        'We need a template that can handle complex legal agreements with conditional sections based on jurisdiction.',
      priority: 'high',
      status: 'in_progress',
      assignedConsultantId: 'consultant_1',
      estimatedHours: 10,
      attachments: [],
      communications: [],
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    };

    // Create demo communications
    const demoCommunication1: Communication = {
      id: 'comm_demo1',
      requestId: demoRequestId,
      senderId: 'user_demo',
      message:
        "Hi, I'm looking for help with creating a custom template for legal documents. Can you assist?",
      attachments: [],
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    };

    const demoCommunication2: Communication = {
      id: 'comm_demo2',
      requestId: demoRequestId,
      senderId: 'consultant_1',
      message:
        "Hello! I'd be happy to help with your legal document template. Could you provide more details about your specific requirements?",
      attachments: [],
      createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
    };

    const demoCommunication3: Communication = {
      id: 'comm_demo3',
      requestId: demoRequestId,
      senderId: 'user_demo',
      message:
        'We need a template that can handle different jurisdictions. The sections of the agreement change based on the country selected.',
      attachments: [],
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    };

    // Create a demo session
    const demoSession: ConsultingSession = {
      id: 'session_demo',
      requestId: demoRequestId,
      consultantId: 'consultant_1',
      startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      endTime: new Date(
        Date.now() - 3 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000
      ), // 2 hours after start
      notes:
        'Discussed requirements for the legal document template. Client needs conditional sections based on jurisdiction.',
      outcome:
        'Agreed to create a prototype with conditional logic for US, EU, and Asia-Pacific regions.',
    };

    // Add communications to the request
    demoRequest.communications = [
      demoCommunication1,
      demoCommunication2,
      demoCommunication3,
    ];

    // Store everything
    this.consultingRequests.set(demoRequestId, demoRequest);
    this.communications.set(demoCommunication1.id, demoCommunication1);
    this.communications.set(demoCommunication2.id, demoCommunication2);
    this.communications.set(demoCommunication3.id, demoCommunication3);
    this.sessions.set(demoSession.id, demoSession);
  }
}

import CryptoJS from 'crypto-js';
import { SignatureOptions } from './SignatureOptions';

export interface SignatureMetadata {
  signedAt: string;
  signedBy: string;
  signatureType: 'digital' | 'electronic';
  certificateInfo?: string;
  validUntil?: string;
}

export interface SignatureResult {
  signature: string;
  metadata: SignatureMetadata;
}

export class SignatureService {
  constructor() {}

  private generateSignatureHash(
    content: string,
    options: SignatureOptions
  ): string {
    const dataToHash = `${content}|${options.signedBy}|${options.signedAt}`;
    return CryptoJS.SHA256(dataToHash).toString();
  }

  private generateCertificateInfo(options: SignatureOptions): string {
    return (
      options.certificateInfo ||
      JSON.stringify({
        signatureType: options.signatureType,
        signedAt: options.signedAt,
        validUntil: options.validUntil,
      })
    );
  }

  public async signDocument(
    content: string,
    options: SignatureOptions
  ): Promise<string> {
    const signatureHash = this.generateSignatureHash(content, options);
    const certificateInfo = this.generateCertificateInfo(options);

    const signature = {
      hash: signatureHash,
      certificate: certificateInfo,
      metadata: {
        signedBy: options.signedBy,
        signedAt: options.signedAt,
        validUntil: options.validUntil,
      },
    };

    return `${content}\n\n---DIGITAL SIGNATURE---\n${JSON.stringify(signature, null, 2)}`;
  }

  public async verifySignature(
    signedContent: string,
    options: SignatureOptions
  ): Promise<boolean> {
    const [content, signatureBlock] = signedContent.split(
      '\n\n---DIGITAL SIGNATURE---\n'
    );
    if (!signatureBlock) return false;

    try {
      const signature = JSON.parse(signatureBlock);
      const expectedHash = this.generateSignatureHash(content, options);
      return signature.hash === expectedHash;
    } catch (error) {
      return false;
    }
  }
}

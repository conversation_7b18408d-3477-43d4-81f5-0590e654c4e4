import OpenAI from 'openai';
import { notificationService } from './NotificationService';

export interface AICompletion {
  text: string;
  confidence: number;
}

export interface DocumentAnalysis {
  summary: string;
  keyPoints: string[];
  risks: {
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[];
  suggestions: string[];
}

export interface TemplateRecommendation {
  templateId: string;
  name: string;
  description: string;
  confidence: number;
  suitabilityReasons: string[];
}

export interface FormFieldSuggestion {
  value: string;
  explanation: string;
  confidence: number;
}

export interface UserContext {
  userId: string;
  recentTemplates?: string[];
  industry?: string;
  region?: string;
  preferences?: Record<string, any>;
}

export class AIService {
  private static instance: AIService;
  private openai: OpenAI;
  private cache: Map<string, { result: any; timestamp: number; ttl: number }>;
  private notificationService: typeof notificationService;
  private defaultCacheTTL = 1000 * 60 * 60; // 1 hour

  private constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
    });
    this.cache = new Map();
    this.notificationService = notificationService;
  }

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * Generates a cache key for storing and retrieving cached results
   */
  private generateCacheKey(
    method: string,
    params: Record<string, any>
  ): string {
    return `${method}:${JSON.stringify(params)}`;
  }

  /**
   * Gets a value from cache if it exists and is not expired
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.result as T;
  }

  /**
   * Stores a value in the cache with the specified TTL
   */
  private setInCache<T>(
    key: string,
    value: T,
    ttl: number = this.defaultCacheTTL
  ): void {
    this.cache.set(key, {
      result: value,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Makes an API request to the OpenAI API for text completion
   */
  private async makeCompletionRequest(
    prompt: string,
    options: {
      temperature?: number;
      maxTokens?: number;
    } = {}
  ): Promise<string> {
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: prompt }],
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 500,
      });

      return response.choices[0]?.message.content || '';
    } catch (error: any) {
      this.notificationService.showToast(
        `AI request failed: ${error.message}`,
        {
          type: 'error',
        }
      );
      throw new Error(`AI request failed: ${error.message}`);
    }
  }

  /**
   * Suggests a value for a form field based on the field name, type, and form context
   */
  public async suggestFieldValue(
    fieldName: string,
    fieldType: string,
    formContext: Record<string, any>
  ): Promise<FormFieldSuggestion[]> {
    const cacheKey = this.generateCacheKey('suggestFieldValue', {
      fieldName,
      fieldType,
      formContext,
    });

    const cached = this.getFromCache<FormFieldSuggestion[]>(cacheKey);
    if (cached) return cached;

    // Build a prompt for the AI to generate a suggestion
    const contextStr = JSON.stringify(formContext);
    const prompt = `
      Based on the following form context, suggest a value for the field "${fieldName}" of type "${fieldType}".
      
      Form Context:
      ${contextStr}
      
      Please provide 3 possible values for this field in the following JSON format:
      [
        {
          "value": "suggested value",
          "explanation": "brief explanation of why this value is appropriate",
          "confidence": confidence score between 0 and 1
        }
      ]
      
      Only respond with the JSON array, no additional text.
    `;

    try {
      const result = await this.makeCompletionRequest(prompt, {
        temperature: 0.7,
      });
      const suggestions = JSON.parse(result) as FormFieldSuggestion[];

      // Cache the result
      this.setInCache(cacheKey, suggestions);

      return suggestions;
    } catch (error) {
      console.error('Error generating field suggestions:', error);
      return [];
    }
  }

  /**
   * Analyzes a document for compliance, summary, or risks
   */
  public async analyzeDocument(
    documentContent: string,
    analysisType: 'compliance' | 'summary' | 'risks' | 'all'
  ): Promise<DocumentAnalysis> {
    const cacheKey = this.generateCacheKey('analyzeDocument', {
      documentContent: documentContent.slice(0, 100), // Use first 100 chars for cache key
      analysisType,
    });

    const cached = this.getFromCache<DocumentAnalysis>(cacheKey);
    if (cached) return cached;

    let prompt = `
      Analyze the following legal document:
      
      ${documentContent}
      
    `;

    if (analysisType === 'compliance' || analysisType === 'all') {
      prompt += `
        Identify any potential compliance issues, risks, or concerns in this document.
        Provide specific suggestions to improve compliance and reduce legal risks.
      `;
    }

    if (analysisType === 'summary' || analysisType === 'all') {
      prompt += `
        Provide a concise summary of the key points in this document.
        Highlight the most important terms and conditions.
      `;
    }

    if (analysisType === 'risks' || analysisType === 'all') {
      prompt += `
        Identify any potential risks or legal concerns in this document.
        Rate each risk as low, medium, or high severity.
      `;
    }

    prompt += `
      Respond with a JSON object in the following format:
      {
        "summary": "brief document summary",
        "keyPoints": ["key point 1", "key point 2", ...],
        "risks": [
          {
            "description": "description of risk",
            "severity": "low|medium|high"
          }
        ],
        "suggestions": ["suggestion 1", "suggestion 2", ...]
      }
      
      Only respond with the JSON object, no additional text.
    `;

    try {
      const result = await this.makeCompletionRequest(prompt, {
        temperature: 0.3,
        maxTokens: 1000,
      });
      const analysis = JSON.parse(result) as DocumentAnalysis;

      // Cache the result
      this.setInCache(cacheKey, analysis);

      return analysis;
    } catch (error) {
      console.error('Error analyzing document:', error);
      return {
        summary: 'Analysis failed',
        keyPoints: [],
        risks: [],
        suggestions: ['Error processing document analysis'],
      };
    }
  }

  /**
   * Recommends templates based on user context and requirements
   */
  public async recommendTemplate(
    userContext: UserContext,
    requirements: string[]
  ): Promise<TemplateRecommendation[]> {
    const cacheKey = this.generateCacheKey('recommendTemplate', {
      userContext,
      requirements,
    });

    const cached = this.getFromCache<TemplateRecommendation[]>(cacheKey);
    if (cached) return cached;

    // Need to get available templates from database to recommend from
    // This would typically involve a database query, but for now we'll simulate it
    const templates = await this.getAvailableTemplates(userContext.region);

    const userContextStr = JSON.stringify(userContext);
    const requirementsStr = requirements.join(', ');
    const templatesStr = JSON.stringify(templates);

    const prompt = `
      Based on the following user context and requirements, recommend the most suitable legal document templates from the available options.
      
      User Context:
      ${userContextStr}
      
      Requirements:
      ${requirementsStr}
      
      Available Templates:
      ${templatesStr}
      
      Please recommend up to 3 templates in the following JSON format:
      [
        {
          "templateId": "template ID",
          "name": "template name",
          "description": "template description",
          "confidence": confidence score between 0 and 1,
          "suitabilityReasons": ["reason 1", "reason 2", ...]
        }
      ]
      
      Only respond with the JSON array, no additional text.
    `;

    try {
      const result = await this.makeCompletionRequest(prompt, {
        temperature: 0.5,
      });
      const recommendations = JSON.parse(result) as TemplateRecommendation[];

      // Cache the result for a shorter time since user context might change
      this.setInCache(cacheKey, recommendations, 1000 * 60 * 15); // 15 minutes

      return recommendations;
    } catch (error) {
      console.error('Error generating template recommendations:', error);
      return [];
    }
  }

  /**
   * Simulates getting available templates from the database
   * In a real implementation, this would query the database
   */
  private async getAvailableTemplates(
    region?: string
  ): Promise<Array<{ id: string; name: string; description: string }>> {
    // This would be replaced with a real database query
    return [
      {
        id: 'template1',
        name: 'Non-Disclosure Agreement',
        description:
          'A legal contract between at least two parties that outlines confidential information the parties wish to share with one another but restrict access to third parties.',
      },
      {
        id: 'template2',
        name: 'Employment Contract',
        description:
          'A legal agreement between an employer and employee that covers terms of employment including salary, benefits, responsibilities, and termination conditions.',
      },
      {
        id: 'template3',
        name: 'Service Agreement',
        description:
          'A contract outlining services provided by one party to another, including scope, payment terms, timeline, and deliverables.',
      },
      {
        id: 'template4',
        name: 'Real Estate Purchase Agreement',
        description:
          'A contract between buyer and seller that sets forth terms and conditions for the purchase of real property.',
      },
      {
        id: 'template5',
        name: 'LLC Operating Agreement',
        description:
          'A document that establishes the rules and regulations for operating a limited liability company (LLC).',
      },
    ];
  }
}

import { Database } from '@/lib/supabase/database-types';
import { supabaseServer } from '@/lib/supabase/server-client';

type UserRole = Database['public']['Enums']['user_role'];

/**
 * Check if the current user has a specific role
 */
export async function hasRole(role: UserRole): Promise<boolean> {
  const supabase = await supabaseServer();

  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return false;

  const { data: profile } = await supabase
    .from('profiles')
    .select('user_role')
    .eq('id', user.id)
    .single();

  return profile?.user_role === role;
}

/**
 * Check if the current user is a lawyer
 */
export async function isLawyer(): Promise<boolean> {
  return hasRole('lawyer');
}

/**
 * Get the lawyer profile for a user
 */
export async function getLawyerProfile(userId: string) {
  const supabase = await supabaseServer();

  const { data, error } = await supabase.rpc('get_user_lawyer_profile', {
    user_uuid: userId,
  });

  if (error) {
    console.error('Error fetching lawyer profile:', error);
    return null;
  }

  return data;
}

/**
 * Get upcoming consultations for a lawyer
 */
export async function getLawyerUpcomingConsultations(lawyerId: string) {
  const supabase = await supabaseServer();

  const { data, error } = await supabase.rpc(
    'get_lawyer_upcoming_consultations',
    {
      lawyer_uuid: lawyerId,
    }
  );

  if (error) {
    console.error('Error fetching upcoming consultations:', error);
    return [];
  }

  return data || [];
}

/**
 * Switch a user's role between 'user' and 'lawyer'
 */
export async function switchUserRole(
  userId: string,
  newRole: UserRole
): Promise<boolean> {
  const supabase = await supabaseServer();

  const { error } = await supabase
    .from('profiles')
    .update({ user_role: newRole })
    .eq('id', userId);

  if (error) {
    console.error('Error switching user role:', error);
    return false;
  }

  return true;
}

import {
  startOfWeek,
  endOfWeek,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  eachWeekOfInterval,
  isSameDay,
  isSameMonth,
  addDays,
  addMonths,
  subMonths,
  format,
  parseISO,
  isWithinInterval,
  getHours,
  getMinutes,
  setHours,
  setMinutes,
} from 'date-fns';
import { CalendarEvent, TimeSlot } from '../types/database-modules';

// Get days for month view
export function getMonthDays(date: Date) {
  const start = startOfWeek(startOfMonth(date));
  const end = endOfWeek(endOfMonth(date));

  const days = eachDayOfInterval({ start, end });
  const weeks = eachWeekOfInterval({ start, end }).map((week) => {
    const weekStart = startOfWeek(week);
    return eachDayOfInterval({
      start: weekStart,
      end: addDays(weekStart, 6),
    });
  });

  return { days, weeks };
}

// Get days for week view
export function getWeekDays(date: Date) {
  const start = startOfWeek(date);
  const end = endOfWeek(date);

  return eachDayOfInterval({ start, end });
}

// Get hours for day view
export function getDayHours() {
  return Array.from({ length: 24 }, (_, i) => i);
}

// Check if event is on a specific day
export function isEventOnDay(event: CalendarEvent, day: Date) {
  const eventStart = new Date(event.start);
  const eventEnd = new Date(event.end);

  return isWithinInterval(day, {
    start: startOfDay(eventStart),
    end: endOfDay(eventEnd),
  });
}

// Format date for display
export function formatDate(date: Date, formatString: string) {
  return format(date, formatString);
}

// Parse ISO string to Date
export function parseDate(dateString: string) {
  return parseISO(dateString);
}

// Calculate event position and height for week/day view
export function calculateEventPosition(event: CalendarEvent, day: Date) {
  const eventStart = new Date(event.start);
  const eventEnd = new Date(event.end);

  // If all day event or multi-day event
  if (event.allDay || !isSameDay(eventStart, eventEnd)) {
    return {
      top: 0,
      height: 24,
      isAllDay: true,
    };
  }

  const startHour = getHours(eventStart) + getMinutes(eventStart) / 60;
  const endHour = getHours(eventEnd) + getMinutes(eventEnd) / 60;
  const duration = endHour - startHour;

  return {
    top: startHour,
    height: duration,
    isAllDay: false,
  };
}

// Generate time slots for a day based on availability
export function generateTimeSlots(
  date: Date,
  availableStartTime: string,
  availableEndTime: string,
  durationMinutes: number = 60,
  existingEvents: CalendarEvent[] = []
): TimeSlot[] {
  const day = startOfDay(date);
  const slots: TimeSlot[] = [];

  // Parse available hours
  const [startHour, startMinute] = availableStartTime.split(':').map(Number);
  const [endHour, endMinute] = availableEndTime.split(':').map(Number);

  const startTime = setMinutes(setHours(day, startHour), startMinute);
  const endTime = setMinutes(setHours(day, endHour), endMinute);

  // Generate slots in 15-minute increments
  const slotDuration = durationMinutes * 60 * 1000; // Convert to milliseconds
  const incrementDuration = 15 * 60 * 1000; // 15 minutes in milliseconds

  let currentSlotStart = new Date(startTime);

  while (currentSlotStart.getTime() + slotDuration <= endTime.getTime()) {
    const currentSlotEnd = new Date(currentSlotStart.getTime() + slotDuration);

    // Check if slot overlaps with any existing events
    const isAvailable = !existingEvents.some((event) => {
      const eventStart = new Date(event.start);
      const eventEnd = new Date(event.end);

      return (
        (currentSlotStart >= eventStart && currentSlotStart < eventEnd) ||
        (currentSlotEnd > eventStart && currentSlotEnd <= eventEnd) ||
        (currentSlotStart <= eventStart && currentSlotEnd >= eventEnd)
      );
    });

    slots.push({
      start: new Date(currentSlotStart),
      end: new Date(currentSlotEnd),
      isAvailable,
    });

    // Move to next slot
    currentSlotStart = new Date(currentSlotStart.getTime() + incrementDuration);
  }

  return slots;
}

// Get color class based on event color and status
export function getEventColorClass(event: CalendarEvent) {
  // Status-based colors take precedence
  if (event.status === 'cancelled') {
    return 'bg-gray-200 text-gray-700 border-gray-300';
  }

  if (event.status === 'completed') {
    return 'bg-green-100 text-green-800 border-green-200';
  }

  // Then use the event's color property
  switch (event.color) {
    case 'sky':
      return 'bg-sky-100 text-sky-800 border-sky-200';
    case 'amber':
      return 'bg-amber-100 text-amber-800 border-amber-200';
    case 'violet':
      return 'bg-violet-100 text-violet-800 border-violet-200';
    case 'rose':
      return 'bg-rose-100 text-rose-800 border-rose-200';
    case 'emerald':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'orange':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    default:
      return 'bg-blue-100 text-blue-800 border-blue-200';
  }
}

// Convert LawyerConsultation to CalendarEvent
export function consultationToCalendarEvent(consultation: any): CalendarEvent {
  return {
    id: consultation.id,
    title: `Consultation with ${consultation.client?.full_name || consultation.lawyer?.full_name || 'Unknown'}`,
    description: consultation.notes,
    start: new Date(consultation.consultation_date),
    end: new Date(
      new Date(consultation.consultation_date).getTime() +
        consultation.duration_minutes * 60 * 1000
    ),
    allDay: false,
    color: getConsultationColor(consultation.status),
    location: consultation.location,
    consultationType: consultation.document_id ? 'document' : 'video',
    status: consultation.status,
    clientId: consultation.user_id,
    lawyerId: consultation.lawyer_id,
    documentId: consultation.document_id,
    client: consultation.client,
    lawyer: consultation.lawyer,
  };
}

// Get color based on consultation status
function getConsultationColor(status: string): CalendarEvent['color'] {
  switch (status) {
    case 'scheduled':
      return 'sky';
    case 'confirmed':
      return 'emerald';
    case 'completed':
      return 'violet';
    case 'cancelled':
      return 'rose';
    default:
      return 'sky';
  }
}

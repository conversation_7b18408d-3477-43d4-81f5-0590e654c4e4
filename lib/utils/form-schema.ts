import { z } from 'zod';
import { LegalTemplate } from '../constants/schemas/template';

export function createFormSchema(template: LegalTemplate) {
  const schemaFields: Record<string, any> = {};

  template.sections.forEach((section) => {
    const sectionFields: Record<string, any> = {};

    section.variables?.forEach((variable) => {
      let fieldSchema = variable.type === 'date' ? z.coerce.date() : z.string();

      if (variable.isRequired) {
        if (variable.type === 'date') {
          fieldSchema = z.coerce.date({
            required_error: `${variable.description} is required`,
            invalid_type_error: 'Invalid date format',
          });
        } else {
          fieldSchema = z.string().min(1, {
            message: `${variable.description} is required`,
          });
        }
      }

      sectionFields[variable.name] = variable.isRequired
        ? fieldSchema
        : fieldSchema.optional();
    });

    schemaFields[section.id] = z.object(sectionFields);
  });

  return z.object(schemaFields);
}

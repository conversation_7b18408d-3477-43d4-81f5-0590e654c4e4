import { AVATARS, RND_USER_AVATARS } from '@/lib/constants/avatars';

/**
 * Get a random avatar from the available avatars
 */
export function getRandomAvatar(): string {
  const randomIndex = Math.floor(Math.random() * RND_USER_AVATARS.length);
  return RND_USER_AVATARS[randomIndex].src;
}

/**
 * Get an avatar by name (uses a deterministic mapping based on the name)
 * @param name The name to get an avatar for
 */
export function getAvatarByName(name: string): string {
  // Use a simple hash function to map names to avatars
  const hash = name
    .split('')
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);

  // Get all avatars as a flat array
  const allAvatars = [
    ...Object.values(AVATARS.male),
    ...Object.values(AVATARS.female),
  ];

  // Use the hash to select an avatar
  const index = hash % allAvatars.length;
  return allAvatars[index].src;
}

/**
 * Generate a UI Avatars URL for a given name
 * This is used as a fallback when no custom avatar is available
 * @param name The name to generate an avatar for
 * @param options Optional customization options
 */
export function generateUIAvatar(
  name: string,
  options: {
    background?: string;
    color?: string;
    size?: number;
    rounded?: boolean;
  } = {}
): string {
  const {
    background = '0D8ABC',
    color = 'fff',
    size = 150,
    rounded = true,
  } = options;

  const params = new URLSearchParams({
    name: name || 'User',
    background,
    color,
    size: size.toString(),
    ...(rounded && { rounded: 'true' }),
  });

  return `https://ui-avatars.com/api/?${params.toString()}`;
}

/**
 * Get the best available avatar for a user
 * Prioritizes custom avatar URL, then falls back to UI Avatars
 * @param avatarUrl Custom avatar URL (from user profile)
 * @param fallbackName Name to use for UI Avatars fallback
 */
export function getBestAvatar(
  avatarUrl: string | null | undefined,
  fallbackName: string
): string {
  if (avatarUrl && avatarUrl.trim()) {
    return avatarUrl;
  }

  return generateUIAvatar(fallbackName);
}

import { Metadata } from 'next';
import { BASE_URL } from '../constants';

export function constructMetadata({
  title = 'Forms Notamess - Legal Forms Done Simple',
  description = 'Effortlessly Create, Customize  and Share Legal Documents.',
  image = `${BASE_URL}/assets/imgs/og-image.png`,
  video,
  icons = [
    {
      rel: 'apple-touch-icon',
      sizes: '32x32',
      url: '/apple-touch-icon.png',
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '32x32',
      url: '/favicon-32x32.png',
    },
    {
      rel: 'icon',
      type: 'image/png',
      sizes: '16x16',
      url: '/favicon-16x16.png',
    },
  ],
  canonicalUrl,
  noIndex = false,
}: {
  title?: string;
  description?: string;
  image?: string | null;
  video?: string | null;
  icons?: Metadata['icons'];
  canonicalUrl?: string;
  noIndex?: boolean;
} = {}): Metadata {
  return {
    title,
    description,
    openGraph: {
      title,
      description,
      ...(image && {
        images: image,
      }),
      ...(video && {
        videos: video,
      }),
    },
    twitter: {
      title,
      description,
      ...(image && {
        card: 'summary_large_image',
        images: [image],
      }),
      ...(video && {
        player: video,
      }),
      creator: '@dubdotco',
    },
    icons,
    metadataBase: new URL(BASE_URL),
    verification: {
      google: '4Mrxr8H4HzeFcxw_rQ-6av0KbipuRFuzmkX2NOMTLS8',
    },
    ...(canonicalUrl && {
      alternates: {
        canonical: canonicalUrl,
      },
    }),
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
      },
    }),
  };
}

/**
 * Rate Limiting Utilities for API Calls
 * 
 * Provides utilities to handle rate limiting for external APIs like Resend
 * Includes delay mechanisms, sequential execution, and retry logic
 */

export interface RateLimitInfo {
  remaining?: number;
  reset?: number;
  limit?: number;
}

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export class RateLimiter {
  /**
   * Simple delay utility
   */
  static async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Execute an operation with a delay before it
   */
  static async executeWithDelay<T>(
    operation: () => Promise<T>,
    delayMs: number = 600
  ): Promise<T> {
    if (delayMs > 0) {
      await this.delay(delayMs);
    }
    return await operation();
  }

  /**
   * Execute multiple operations sequentially with delays between them
   */
  static async executeSequentially<T>(
    operations: (() => Promise<T>)[],
    delayMs: number = 600
  ): Promise<T[]> {
    const results: T[] = [];
    
    for (let i = 0; i < operations.length; i++) {
      // Add delay before each operation except the first one
      if (i > 0 && delayMs > 0) {
        await this.delay(delayMs);
      }
      
      const result = await operations[i]();
      results.push(result);
    }
    
    return results;
  }

  /**
   * Execute an operation with exponential backoff retry logic
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2
    }
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        // Don't retry on the last attempt
        if (attempt === options.maxRetries) {
          break;
        }
        
        // Check if this is a rate limit error that we should retry
        if (this.isRateLimitError(error)) {
          const delay = Math.min(
            options.baseDelay * Math.pow(options.backoffMultiplier, attempt),
            options.maxDelay
          );
          
          console.log(`Rate limit hit, retrying in ${delay}ms (attempt ${attempt + 1}/${options.maxRetries + 1})`);
          await this.delay(delay);
          continue;
        }
        
        // For non-rate-limit errors, don't retry
        throw error;
      }
    }
    
    throw lastError!;
  }

  /**
   * Check if an error is a rate limit error
   */
  static isRateLimitError(error: any): boolean {
    // Check for Resend rate limit error
    if (error?.response?.status === 429) {
      return true;
    }
    
    // Check for rate limit in error message
    if (error?.message?.includes('rate_limit_exceeded')) {
      return true;
    }
    
    // Check for rate limit in error name
    if (error?.name === 'rate_limit_exceeded') {
      return true;
    }
    
    return false;
  }

  /**
   * Parse rate limit information from error response
   */
  static parseRateLimitInfo(error: any): RateLimitInfo | null {
    try {
      const headers = error?.response?.headers;
      if (!headers) return null;
      
      return {
        remaining: headers['x-ratelimit-remaining'] ? parseInt(headers['x-ratelimit-remaining']) : undefined,
        reset: headers['x-ratelimit-reset'] ? parseInt(headers['x-ratelimit-reset']) : undefined,
        limit: headers['x-ratelimit-limit'] ? parseInt(headers['x-ratelimit-limit']) : undefined,
      };
    } catch {
      return null;
    }
  }

  /**
   * Calculate optimal delay based on rate limit headers
   */
  static calculateOptimalDelay(rateLimitInfo: RateLimitInfo | null, defaultDelay: number = 600): number {
    if (!rateLimitInfo) return defaultDelay;
    
    // If we have remaining requests, use a shorter delay
    if (rateLimitInfo.remaining && rateLimitInfo.remaining > 0) {
      return Math.min(defaultDelay, 300); // Shorter delay if we have remaining requests
    }
    
    // If we have reset time, calculate delay until reset
    if (rateLimitInfo.reset) {
      const now = Math.floor(Date.now() / 1000);
      const resetDelay = (rateLimitInfo.reset - now) * 1000;
      
      // Use reset delay if it's reasonable (less than 60 seconds)
      if (resetDelay > 0 && resetDelay < 60000) {
        return resetDelay + 100; // Add small buffer
      }
    }
    
    return defaultDelay;
  }

  /**
   * Log rate limiting information for debugging
   */
  static logRateLimitInfo(operation: string, rateLimitInfo: RateLimitInfo | null): void {
    if (rateLimitInfo) {
      console.log(`[RATE-LIMIT] ${operation}:`, {
        remaining: rateLimitInfo.remaining,
        reset: rateLimitInfo.reset ? new Date(rateLimitInfo.reset * 1000).toISOString() : undefined,
        limit: rateLimitInfo.limit,
      });
    }
  }
}

/**
 * Specialized rate limiter for email operations
 */
export class EmailRateLimiter extends RateLimiter {
  private static readonly EMAIL_DELAY = 600; // 600ms between email operations
  private static readonly EMAIL_RETRY_OPTIONS: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2
  };

  /**
   * Execute email operation with appropriate delay and retry logic
   */
  static async executeEmailOperation<T>(
    operation: () => Promise<T>,
    operationName: string = 'email-operation'
  ): Promise<T> {
    console.log(`[EMAIL-RATE-LIMITER] Starting ${operationName}`);
    
    try {
      const result = await this.executeWithRetry(operation, this.EMAIL_RETRY_OPTIONS);
      console.log(`[EMAIL-RATE-LIMITER] Completed ${operationName} successfully`);
      return result;
    } catch (error: any) {
      console.error(`[EMAIL-RATE-LIMITER] Failed ${operationName}:`, error.message);
      
      // Log rate limit info if available
      const rateLimitInfo = this.parseRateLimitInfo(error);
      if (rateLimitInfo) {
        this.logRateLimitInfo(operationName, rateLimitInfo);
      }
      
      throw error;
    }
  }

  /**
   * Execute multiple email operations sequentially
   */
  static async executeEmailOperationsSequentially<T>(
    operations: Array<{ operation: () => Promise<T>; name: string }>,
    customDelay?: number
  ): Promise<T[]> {
    const delay = customDelay ?? this.EMAIL_DELAY;
    console.log(`[EMAIL-RATE-LIMITER] Executing ${operations.length} operations sequentially with ${delay}ms delays`);
    
    const results: T[] = [];
    
    for (let i = 0; i < operations.length; i++) {
      const { operation, name } = operations[i];
      
      // Add delay before each operation except the first one
      if (i > 0) {
        console.log(`[EMAIL-RATE-LIMITER] Waiting ${delay}ms before ${name}`);
        await this.delay(delay);
      }
      
      const result = await this.executeEmailOperation(operation, name);
      results.push(result);
    }
    
    console.log(`[EMAIL-RATE-LIMITER] All ${operations.length} operations completed successfully`);
    return results;
  }
}

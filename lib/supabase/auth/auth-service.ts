'use client';

import { BASE_URL } from '@/lib/constants/variables';
import { toast } from 'sonner';
import { supabaseClient } from '../client';

// Auth service for handling authentication operations
export const authService = {
  /**
   * Check if a username is available
   * @param username Username to check
   * @returns Promise<boolean> true if available, false if taken
   */
  checkUsernameAvailability: async (username: string): Promise<boolean> => {
    try {
      const { data, error } = await supabaseClient
        .from('profiles')
        .select('username')
        .eq('username', username)
        .single();

      if (error && error.code === 'PGRST116') {
        // No rows returned means username is available
        return true;
      }

      if (error) {
        console.error('Error checking username availability:', error);
        return false;
      }

      // If data exists, username is taken
      return !data;
    } catch (error) {
      console.error('Error in username availability check:', error);
      return false;
    }
  },

  /**
   * Generate a unique username from a full name
   * @param fullName User's full name
   * @returns A unique username
   */
  generateUniqueUsername: async (fullName: string): Promise<string> => {
    // Convert full name to lowercase and replace spaces with empty string
    let baseUsername = fullName.toLowerCase().replace(/\s+/g, '');

    // Remove special characters
    baseUsername = baseUsername.replace(/[^a-z0-9]/g, '');

    // Ensure minimum length
    if (baseUsername.length < 3) {
      baseUsername = baseUsername + 'user';
    }

    // Truncate if too long
    if (baseUsername.length > 45) {
      baseUsername = baseUsername.substring(0, 45);
    }

    // Check if base username is available
    const isAvailable =
      await authService.checkUsernameAvailability(baseUsername);
    if (isAvailable) {
      return baseUsername;
    }

    // If username exists, add a number to it
    let counter = 1;
    const maxAttempts = 100;

    while (counter <= maxAttempts) {
      const newUsername = `${baseUsername}${counter}`;
      const isAvailable =
        await authService.checkUsernameAvailability(newUsername);

      if (isAvailable) {
        return newUsername;
      }

      counter++;
    }

    // If we couldn't find a unique username after max attempts, use timestamp
    return `${baseUsername}${Date.now().toString().slice(-6)}`;
  },

  /**
   * Handle social login with any provider
   * @param provider The provider to use (e.g., 'google')
   * @returns Promise with the sign-in result
   */
  handleSocialLogin: async (provider: 'google') => {
    try {
      // Use the BASE_URL from constants for production, or window.location.origin for development
      // This ensures the redirect works correctly in both environments
      let origin = '';

      // Check if we're in a production environment by looking at the hostname
      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          // Development environment - use window.location.origin
          origin = window.location.origin;
        } else {
          // Production environment - use the BASE_URL constant
          origin = BASE_URL;
        }
      } else {
        // Fallback to production URL if window is not available (SSR)
        origin = BASE_URL;
      }

      // Get the current URL to redirect back to after authentication
      const currentPath =
        typeof window !== 'undefined' ? window.location.pathname : '/';
      const redirectPath =
        currentPath === '/login' || currentPath === '/create-account'
          ? '/'
          : currentPath;

      // Store the current path in localStorage to redirect back after auth
      if (typeof window !== 'undefined') {
        localStorage.setItem('redirectPath', redirectPath);
      }

      console.log(
        `Using origin for redirect: ${origin}, will redirect back to: ${redirectPath} after auth`
      );

      // Following Supabase documentation for Google OAuth
      // https://supabase.com/docs/guides/auth/social-login/auth-google
      const { data, error } = await supabaseClient.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${origin}/auth/callback`,
          queryParams: {
            // These parameters are recommended by Google
            access_type: 'offline',
            prompt: 'consent',
            // Include profile and email scopes to get user information
            scope: 'profile email',
          },
          // Set this to false to allow the browser to handle the redirect
          skipBrowserRedirect: false,
        },
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error(`Error signing in with ${provider}:`, error);
      toast.error(`Failed to sign in with ${provider}`, {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Sign in with Google
   * @returns Promise with the sign-in result
   */
  signInWithGoogle: async () => {
    return authService.handleSocialLogin('google');
  },

  // GitHub login removed as it's not enabled in Supabase

  /**
   * Sign up with email and password
   * @param email User's email
   * @param password User's password
   * @param userData Additional user data
   * @returns Promise with the sign-up result
   */
  signUp: async (
    email: string,
    password: string,
    userData?: { [key: string]: any }
  ) => {
    try {
      // Use the same origin determination logic as handleSocialLogin
      let origin = '';

      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          origin = window.location.origin;
        } else {
          origin = BASE_URL;
        }
      } else {
        origin = BASE_URL;
      }

      console.log(`Using origin for email redirect: ${origin}`);
      console.log('Sign up data:', { email, userData });

      const { data, error } = await supabaseClient.auth.signUp({
        email,
        password,
        options: {
          data: userData,
          emailRedirectTo: `${origin}/auth/callback?next=/onboarding`,
        },
      });

      if (error) {
        console.error('Supabase auth error details:', {
          message: error.message,
          status: error.status,
          name: error.name,
          cause: error.cause,
        });

        // Provide more specific error messages with debugging info
        let errorMessage = error.message;
        let debugInfo = '';

        if (error.message.includes('Database error saving new user')) {
          errorMessage =
            'There was an issue creating your account. This might be due to a database constraint or configuration issue.';
          debugInfo =
            'This usually indicates a problem with the database trigger for profile creation or RLS policies. Check the database logs for more details.';

          // Log additional debugging information
          console.error('Database error details:', {
            originalError: error.message,
            suggestedFix:
              'Check if the handle_new_user() trigger function exists and RLS policies are correctly configured',
            email: email,
            userData: userData,
          });
        } else if (error.message.includes('User already registered')) {
          errorMessage =
            'An account with this email already exists. Please try logging in instead.';
        } else if (error.message.includes('Invalid email')) {
          errorMessage = 'Please enter a valid email address.';
        } else if (error.message.includes('Password')) {
          errorMessage = 'Password must be at least 6 characters long.';
        }

        // Include debug info in development
        if (process.env.NODE_ENV === 'development' && debugInfo) {
          console.warn('Debug info:', debugInfo);
        }

        throw new Error(errorMessage);
      }

      console.log('Sign up successful:', data);
      return data;
    } catch (error: any) {
      console.error('Error signing up:', error);
      toast.error('Failed to sign up', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Sign in with email and password
   * @param email User's email
   * @param password User's password
   * @returns Promise with the sign-in result
   */
  signInWithPassword: async (email: string, password: string) => {
    try {
      const { data, error } = await supabaseClient.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error signing in:', error);
      toast.error('Failed to sign in', {
        description:
          error.message || 'Please check your credentials and try again',
      });
      throw error;
    }
  },

  /**
   * Send password reset email
   * @param email User's email
   * @returns Promise with the reset result
   */
  resetPassword: async (email: string) => {
    try {
      // Use the same origin determination logic as handleSocialLogin
      let origin = '';

      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          origin = window.location.origin;
        } else {
          origin = BASE_URL;
        }
      } else {
        origin = BASE_URL;
      }

      console.log(`Using origin for password reset: ${origin}`);

      const { data, error } = await supabaseClient.auth.resetPasswordForEmail(
        email,
        {
          redirectTo: `${origin}/auth/reset-password`,
        }
      );

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error resetting password:', error);
      toast.error('Failed to send password reset email', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Update user's password
   * @param newPassword New password
   * @returns Promise with the update result
   */
  updatePassword: async (newPassword: string) => {
    try {
      const { data, error } = await supabaseClient.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error updating password:', error);
      toast.error('Failed to update password', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Resend verification email
   * @param email User's email
   * @returns Promise with the result
   */
  resendVerificationEmail: async (email: string) => {
    try {
      // Use the same origin determination logic as handleSocialLogin
      let origin = '';

      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          origin = window.location.origin;
        } else {
          origin = BASE_URL;
        }
      } else {
        origin = BASE_URL;
      }

      console.log(`Using origin for verification email: ${origin}`);

      const { data, error } = await supabaseClient.auth.resend({
        type: 'signup',
        email,
        options: {
          emailRedirectTo: `${origin}/auth/callback`,
        },
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error resending verification email:', error);
      toast.error('Failed to resend verification email', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Sign out the current user
   * @returns Promise with the sign-out result
   */
  signOut: async () => {
    try {
      // Import the clearAllStores function dynamically to avoid circular dependencies
      const { clearAllStores } = await import('../../store/clear-stores');

      const { error } = await supabaseClient.auth.signOut();

      if (error) {
        throw error;
      }

      // Clear all Zustand stores when signing out
      clearAllStores();

      return true;
    } catch (error: any) {
      console.error('Error signing out:', error);
      toast.error('Failed to sign out', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },
};

'use client';

import { BASE_URL } from '@/lib/constants/variables';
import { toast } from 'sonner';
import { supabaseClient } from '../client';

// Auth service for handling authentication operations
export const authService = {
  /**
   * Generate a unique username from a full name
   * @param fullName User's full name
   * @returns A unique username
   */
  generateUniqueUsername: async (fullName: string): Promise<string> => {
    // Convert full name to lowercase and replace spaces with empty string
    let baseUsername = fullName.toLowerCase().replace(/\s+/g, '');

    // Remove special characters
    baseUsername = baseUsername.replace(/[^a-z0-9]/g, '');

    // Check if username exists
    const { data } = await supabaseClient
      .from('profiles')
      .select('username')
      .eq('username', baseUsername);

    // If username doesn't exist, return it
    if (!data || data.length === 0) {
      return baseUsername;
    }

    // If username exists, add a number to it
    let counter = 1;
    let newUsername = `${baseUsername}${counter}`;

    // Keep checking until we find a unique username
    while (true) {
      const { data } = await supabaseClient
        .from('profiles')
        .select('username')
        .eq('username', newUsername);

      if (!data || data.length === 0) {
        return newUsername;
      }

      counter++;
      newUsername = `${baseUsername}${counter}`;
    }
  },

  /**
   * Handle social login with any provider
   * @param provider The provider to use (e.g., 'google')
   * @returns Promise with the sign-in result
   */
  handleSocialLogin: async (provider: 'google') => {
    try {
      // Use the BASE_URL from constants for production, or window.location.origin for development
      // This ensures the redirect works correctly in both environments
      let origin = '';

      // Check if we're in a production environment by looking at the hostname
      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          // Development environment - use window.location.origin
          origin = window.location.origin;
        } else {
          // Production environment - use the BASE_URL constant
          origin = BASE_URL;
        }
      } else {
        // Fallback to production URL if window is not available (SSR)
        origin = BASE_URL;
      }

      // Get the current URL to redirect back to after authentication
      const currentPath =
        typeof window !== 'undefined' ? window.location.pathname : '/';
      const redirectPath =
        currentPath === '/login' || currentPath === '/create-account'
          ? '/'
          : currentPath;

      // Store the current path in localStorage to redirect back after auth
      if (typeof window !== 'undefined') {
        localStorage.setItem('redirectPath', redirectPath);
      }

      console.log(
        `Using origin for redirect: ${origin}, will redirect back to: ${redirectPath} after auth`
      );

      // Following Supabase documentation for Google OAuth
      // https://supabase.com/docs/guides/auth/social-login/auth-google
      const { data, error } = await supabaseClient.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${origin}/auth/callback`,
          queryParams: {
            // These parameters are recommended by Google
            access_type: 'offline',
            prompt: 'consent',
            // Include profile and email scopes to get user information
            scope: 'profile email',
          },
          // Set this to false to allow the browser to handle the redirect
          skipBrowserRedirect: false,
        },
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error(`Error signing in with ${provider}:`, error);
      toast.error(`Failed to sign in with ${provider}`, {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Sign in with Google
   * @returns Promise with the sign-in result
   */
  signInWithGoogle: async () => {
    return authService.handleSocialLogin('google');
  },

  // GitHub login removed as it's not enabled in Supabase

  /**
   * Sign up with email and password
   * @param email User's email
   * @param password User's password
   * @param userData Additional user data
   * @returns Promise with the sign-up result
   */
  signUp: async (
    email: string,
    password: string,
    userData?: { [key: string]: any }
  ) => {
    try {
      // Use the same origin determination logic as handleSocialLogin
      let origin = '';

      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          origin = window.location.origin;
        } else {
          origin = BASE_URL;
        }
      } else {
        origin = BASE_URL;
      }

      console.log(`Using origin for email redirect: ${origin}`);

      const { data, error } = await supabaseClient.auth.signUp({
        email,
        password,
        options: {
          data: userData,
          emailRedirectTo: `${origin}/auth/callback?next=/onboarding`,
        },
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error signing up:', error);
      toast.error('Failed to sign up', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Sign in with email and password
   * @param email User's email
   * @param password User's password
   * @returns Promise with the sign-in result
   */
  signInWithPassword: async (email: string, password: string) => {
    try {
      const { data, error } = await supabaseClient.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error signing in:', error);
      toast.error('Failed to sign in', {
        description:
          error.message || 'Please check your credentials and try again',
      });
      throw error;
    }
  },

  /**
   * Send password reset email
   * @param email User's email
   * @returns Promise with the reset result
   */
  resetPassword: async (email: string) => {
    try {
      // Use the same origin determination logic as handleSocialLogin
      let origin = '';

      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          origin = window.location.origin;
        } else {
          origin = BASE_URL;
        }
      } else {
        origin = BASE_URL;
      }

      console.log(`Using origin for password reset: ${origin}`);

      const { data, error } = await supabaseClient.auth.resetPasswordForEmail(
        email,
        {
          redirectTo: `${origin}/auth/reset-password`,
        }
      );

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error resetting password:', error);
      toast.error('Failed to send password reset email', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Update user's password
   * @param newPassword New password
   * @returns Promise with the update result
   */
  updatePassword: async (newPassword: string) => {
    try {
      const { data, error } = await supabaseClient.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error updating password:', error);
      toast.error('Failed to update password', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Resend verification email
   * @param email User's email
   * @returns Promise with the result
   */
  resendVerificationEmail: async (email: string) => {
    try {
      // Use the same origin determination logic as handleSocialLogin
      let origin = '';

      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          origin = window.location.origin;
        } else {
          origin = BASE_URL;
        }
      } else {
        origin = BASE_URL;
      }

      console.log(`Using origin for verification email: ${origin}`);

      const { data, error } = await supabaseClient.auth.resend({
        type: 'signup',
        email,
        options: {
          emailRedirectTo: `${origin}/auth/callback`,
        },
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error resending verification email:', error);
      toast.error('Failed to resend verification email', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },

  /**
   * Sign out the current user
   * @returns Promise with the sign-out result
   */
  signOut: async () => {
    try {
      // Import the clearAllStores function dynamically to avoid circular dependencies
      const { clearAllStores } = await import('../../store/clear-stores');

      const { error } = await supabaseClient.auth.signOut();

      if (error) {
        throw error;
      }

      // Clear all Zustand stores when signing out
      clearAllStores();

      return true;
    } catch (error: any) {
      console.error('Error signing out:', error);
      toast.error('Failed to sign out', {
        description: error.message || 'Please try again later',
      });
      throw error;
    }
  },
};

'use client';

import { supabaseClient } from '@/lib/supabase/client';
import { Document, DocumentSummary } from '@/lib/types/database-modules';
import RealtimeService from './realtime-service';

/**
 * Subscribe to changes on a specific document
 */
export function subscribeToDocument(
  documentId: string,
  callback: (document: Document) => void
): () => void {
  return RealtimeService.subscribeToDocument(documentId, async (_payload) => {
    // When we receive a broadcast event, fetch the full document
    const { data, error } = await supabaseClient
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .single();

    if (error) {
      console.error('Error fetching document:', error);
      return;
    }

    if (data) {
      callback(data as unknown as Document);
    }
  });
}

/**
 * Subscribe to changes on all documents for a user
 */
export function subscribeToUserDocuments(
  userId: string,
  callback: (documents: DocumentSummary[]) => void
): () => void {
  // Create a custom channel for user documents
  const channel = supabaseClient
    .channel(`user-documents-${userId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'documents',
        filter: `owner_id=eq.${userId}`,
      },
      async () => {
        // Fetch all documents for the user
        const { data, error } = await supabaseClient
          .from('document_summaries')
          .select('*')
          .eq('owner_id', userId)
          .order('updated_at', { ascending: false });

        if (error) {
          console.error('Error fetching documents:', error);
          return;
        }

        if (data) {
          callback(data as unknown as DocumentSummary[]);
        }
      }
    )
    .subscribe();

  // Return unsubscribe function
  return () => {
    supabaseClient.removeChannel(channel);
  };
}

/**
 * Subscribe to changes on shared documents for a user
 * Note: This is a placeholder implementation as document_shares table structure needs verification
 */
export function subscribeToSharedDocuments(
  userId: string,
  _callback: (documents: DocumentSummary[]) => void
): () => void {
  // For now, return an empty subscription since document_shares table structure is unclear
  // TODO: Implement proper shared documents subscription once table structure is confirmed
  console.log(
    `Shared documents subscription for user ${userId} - not yet implemented`
  );

  // Return a no-op unsubscribe function
  return () => {
    console.log('Unsubscribing from shared documents (no-op)');
  };
}

/**
 * Cleanup all active subscriptions
 */
export function cleanupAllSubscriptions(): void {
  RealtimeService.unsubscribeAll();
}

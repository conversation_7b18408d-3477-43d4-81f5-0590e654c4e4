'use client';

import { supabaseClient } from '@/lib/supabase/singleton-client';
// Import Realtime types from supabase-js as they're not directly exported from @supabase/ssr
import type {
  RealtimeChannel,
  RealtimeChannelOptions,
} from '@supabase/supabase-js';

/**
 * SafeRealtimeClient
 *
 * A wrapper around the Supabase Realtime client that adds error handling
 * for JSON parsing errors and other common issues.
 * Compatible with @supabase/ssr package.
 *
 * Implements Supabase Realtime Broadcast from Database functionality
 * as described in https://supabase.com/blog/realtime-broadcast-from-database
 */
export class SafeRealtimeClient {
  /**
   * Create a channel with error handling
   */
  static createChannel(
    channelName: string,
    options: Partial<RealtimeChannelOptions> = {}
  ): RealtimeChannel {
    try {
      // Set default options for better reliability
      const defaultOptions: Partial<RealtimeChannelOptions> = {
        config: {
          broadcast: { self: true },
          presence: { key: '' },
        },
      };

      // Merge user options with defaults, preserving user settings
      const mergedOptions = {
        ...defaultOptions,
        ...options,
        config: {
          ...defaultOptions.config,
          ...(options.config || {}),
        },
      };

      // Create the channel with merged options
      return supabaseClient.channel(channelName, mergedOptions);
    } catch (error) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`Error creating channel ${channelName}:`, error);
      }
      throw error;
    }
  }

  /**
   * Safely handle a broadcast event with error handling for JSON parsing
   */
  static safeOnBroadcast(
    channel: RealtimeChannel,
    event: string,
    callback: (payload: any) => void
  ): RealtimeChannel {
    try {
      return channel.on('broadcast', { event }, (payload) => {
        try {
          // Validate payload before processing
          if (!payload || typeof payload !== 'object') {
            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              console.error(`Invalid payload for event ${event}:`, payload);
            }
            return;
          }

          // Call the callback with the payload
          callback(payload);
        } catch (error) {
          // Handle JSON parsing errors and other issues
          // Only log in development
          if (process.env.NODE_ENV === 'development') {
            console.error(`Error processing broadcast event ${event}:`, error);
          }
          // Don't propagate the error to prevent app crashes
        }
      });
    } catch (error) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error(
          `Error setting up broadcast handler for event ${event}:`,
          error
        );
      }
      return channel;
    }
  }

  /**
   * Create a complete broadcast channel with handlers for INSERT, UPDATE, DELETE events
   */
  static createBroadcastChannel(
    channelName: string,
    callback: (payload: any) => void,
    options: Partial<RealtimeChannelOptions> = {}
  ): RealtimeChannel {
    try {
      // Ensure private access for authenticated users
      const channelOptions = {
        ...options,
        config: {
          ...(options.config || {}),
          private: true,
        },
      };

      // Create the channel
      const channel = this.createChannel(channelName, channelOptions);

      // Set up handlers for all database events
      channel
        .on('broadcast', { event: 'INSERT' }, (payload) => {
          try {
            callback(payload);
          } catch (error) {
            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              console.error(
                `Error in INSERT handler for ${channelName}:`,
                error
              );
            }
          }
        })
        .on('broadcast', { event: 'UPDATE' }, (payload) => {
          try {
            callback(payload);
          } catch (error) {
            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              console.error(
                `Error in UPDATE handler for ${channelName}:`,
                error
              );
            }
          }
        })
        .on('broadcast', { event: 'DELETE' }, (payload) => {
          try {
            callback(payload);
          } catch (error) {
            // Only log in development
            if (process.env.NODE_ENV === 'development') {
              console.error(
                `Error in DELETE handler for ${channelName}:`,
                error
              );
            }
          }
        });

      return channel;
    } catch (error) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error(
          `Error creating broadcast channel ${channelName}:`,
          error
        );
      }
      throw error;
    }
  }

  /**
   * Safely subscribe to a channel with error handling and retry logic
   */
  static safeSubscribe(
    channel: RealtimeChannel,
    statusCallback?: (status: string) => void
  ): RealtimeChannel {
    try {
      return channel.subscribe((status) => {
        // Call the status callback if provided
        if (statusCallback) {
          statusCallback(status);
        }

        // Implement retry logic for failed subscriptions
        if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          // Store the channel name and options for recreating it
          const channelName = channel.topic;

          // Only log in development
          if (process.env.NODE_ENV === 'development') {
            console.log(
              `Channel ${channelName} encountered an error. Will recreate.`
            );
          }

          // We need to remove the channel before recreating it
          try {
            supabaseClient.removeChannel(channel);
          } catch (error) {
            // Ignore errors when removing the channel
          }

          // Emit a custom event that the RealtimeService can listen for
          // to recreate the channel with the same parameters
          if (typeof window !== 'undefined') {
            const event = new CustomEvent('realtime:channel:retry', {
              detail: { channelName, status },
            });
            window.dispatchEvent(event);
          }
        }
      });
    } catch (error) {
      // Only log critical errors
      if (process.env.NODE_ENV === 'development') {
        console.error('Error subscribing to channel:', error);
      }
      return channel;
    }
  }

  /**
   * Safely remove a channel with error handling
   */
  static safeRemoveChannel(channel: RealtimeChannel): void {
    try {
      supabaseClient.removeChannel(channel);
    } catch (error) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error removing channel:', error);
      }
    }
  }

  /**
   * Set up authentication for Realtime
   * This method should be called after the user is authenticated
   */
  static async setupRealtimeAuth(): Promise<boolean> {
    try {
      // Get the current session
      const {
        data: { session },
      } = await supabaseClient.auth.getSession();

      if (session?.access_token) {
        // Set the auth token for realtime
        supabaseClient.realtime.setAuth(session.access_token);
        return true;
      } else {
        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          console.warn('No access token available for Realtime authentication');
        }
        return false;
      }
    } catch (error) {
      // Only log critical errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error setting up Realtime authentication:', error);
      }
      return false;
    }
  }
}

export default SafeRealtimeClient;

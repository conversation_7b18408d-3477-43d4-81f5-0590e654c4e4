'use client';

import { supabaseClient } from '@/lib/supabase/singleton-client';
// Import Realtime types from supabase-js as they're not directly exported from @supabase/ssr
import { RealtimeChannel, RealtimeChannelOptions } from '@supabase/supabase-js';
import SafeRealtimeClient from './safe-realtime-client';

// Store for active subscriptions
type SubscriptionInfo = {
  channel: RealtimeChannel;
  unsubscribe: () => void;
};

// Map to store active subscriptions by key
const activeSubscriptions: Map<string, SubscriptionInfo> = new Map();

// Map to store channel configurations for retry
const channelConfigurations: Map<
  string,
  {
    callback: (payload: any) => void;
    options?: Partial<RealtimeChannelOptions>;
  }
> = new Map();

/**
 * Realtime Service
 *
 * A centralized service for managing realtime subscriptions using Supabase Broadcast.
 * This service provides methods for subscribing to various types of realtime updates
 * and manages the lifecycle of subscriptions.
 */
export class RealtimeService {
  /**
   * Subscribe to document changes for a specific document
   */
  static subscribeToDocument(
    documentId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `document:${documentId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Store the channel configuration for retry
    channelConfigurations.set(key, {
      callback,
      options: {
        config: {
          private: true,
          broadcast: { self: true },
        },
      },
    });

    // Create a broadcast channel with all event handlers
    const channel = SafeRealtimeClient.createBroadcastChannel(key, callback, {
      config: {
        private: true,
        broadcast: { self: true },
      },
    });

    // Safely subscribe to the channel with retry logic
    SafeRealtimeClient.safeSubscribe(channel);

    // Store the unsubscribe function
    const unsubscribe = () => {
      SafeRealtimeClient.safeRemoveChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to notifications for a specific user
   */
  static subscribeToNotifications(
    userId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `notifications:${userId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    try {
      // Store the channel configuration for retry
      channelConfigurations.set(key, {
        callback,
        options: {
          config: {
            broadcast: { self: true },
            private: true,
          },
        },
      });

      // Create a broadcast channel with all event handlers and proper configuration
      const channel = SafeRealtimeClient.createBroadcastChannel(key, callback, {
        config: {
          broadcast: { self: true },
          private: true,
        },
      });

      // Safely subscribe to the channel with retry logic
      SafeRealtimeClient.safeSubscribe(channel);

      // Store the unsubscribe function
      const unsubscribe = () => {
        try {
          SafeRealtimeClient.safeRemoveChannel(channel);
          activeSubscriptions.delete(key);
        } catch (error) {
          console.error(`Error unsubscribing from ${key}:`, error);
        }
      };

      activeSubscriptions.set(key, { channel, unsubscribe });
      return unsubscribe;
    } catch (error) {
      console.error('Error setting up notification subscription:', error);
      // Return a no-op function if subscription fails
      return () => {};
    }
  }

  /**
   * Subscribe to template changes
   */
  static subscribeToTemplates(callback: (payload: any) => void): () => void {
    const key = 'templates';

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Store the channel configuration for retry
    channelConfigurations.set(key, {
      callback,
      options: {
        config: {
          private: true,
          broadcast: { self: true },
        },
      },
    });

    // Create a broadcast channel with all event handlers
    const channel = SafeRealtimeClient.createBroadcastChannel(key, callback, {
      config: {
        private: true,
        broadcast: { self: true },
      },
    });

    // Safely subscribe to the channel with retry logic
    SafeRealtimeClient.safeSubscribe(channel);

    // Store the unsubscribe function
    const unsubscribe = () => {
      SafeRealtimeClient.safeRemoveChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to booking changes for a client
   */
  static subscribeToClientBookings(
    clientId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `bookings:client:${clientId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Store the channel configuration for retry
    channelConfigurations.set(key, {
      callback,
      options: {
        config: {
          broadcast: { self: true },
          private: true,
        },
      },
    });

    // Create a broadcast channel with all event handlers and proper configuration
    const channel = SafeRealtimeClient.createBroadcastChannel(key, callback, {
      config: {
        broadcast: { self: true },
        private: true,
      },
    });

    // Safely subscribe to the channel with retry logic
    SafeRealtimeClient.safeSubscribe(channel);

    // Store the unsubscribe function
    const unsubscribe = () => {
      SafeRealtimeClient.safeRemoveChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to booking changes for a lawyer
   */
  static subscribeToLawyerBookings(
    lawyerId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `bookings:lawyer:${lawyerId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Store the channel configuration for retry
    channelConfigurations.set(key, {
      callback,
      options: {
        config: {
          broadcast: { self: true },
          private: true,
        },
      },
    });

    // Create a broadcast channel with all event handlers and proper configuration
    const channel = SafeRealtimeClient.createBroadcastChannel(key, callback, {
      config: {
        broadcast: { self: true },
        private: true,
      },
    });

    // Safely subscribe to the channel with retry logic
    SafeRealtimeClient.safeSubscribe(channel);

    // Store the unsubscribe function
    const unsubscribe = () => {
      SafeRealtimeClient.safeRemoveChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to document sharing changes for a document owner
   */
  static subscribeToDocumentSharingAsOwner(
    ownerId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `document_shares:owner:${ownerId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the document shares
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to document sharing changes for a user who has documents shared with them
   */
  static subscribeToDocumentSharingAsUser(
    userId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `document_shares:user:${userId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the document shares
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to all projects
   */
  static subscribeToProjects(callback: (payload: any) => void): () => void {
    const key = 'projects';

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the projects table
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to a specific project
   */
  static subscribeToProject(
    projectId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `project:${projectId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the project
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to project members
   */
  static subscribeToProjectMembers(
    projectId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `project_members:${projectId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the project members
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to project tasks
   */
  static subscribeToProjectTasks(
    projectId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `project_tasks:${projectId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the project tasks
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to project comments
   */
  static subscribeToProjectComments(
    projectId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `project_comments:${projectId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the project comments
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to project documents
   */
  static subscribeToProjectDocuments(
    projectId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `project_documents:${projectId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the project documents
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Subscribe to collaboration changes for a project (legacy method)
   */
  static subscribeToCollaboration(
    projectId: string,
    callback: (payload: any) => void
  ): () => void {
    const key = `collaboration:project:${projectId}`;

    // Clean up any existing subscription with the same key
    this.unsubscribe(key);

    // Authentication is handled by SafeRealtimeClient

    // Subscribe to changes on the project
    const channel = supabaseClient
      .channel(key, {
        config: { private: true },
      })
      .on('broadcast', { event: 'INSERT' }, (payload) => callback(payload))
      .on('broadcast', { event: 'UPDATE' }, (payload) => callback(payload))
      .on('broadcast', { event: 'DELETE' }, (payload) => callback(payload))
      .subscribe();

    // Store the unsubscribe function
    const unsubscribe = () => {
      supabaseClient.removeChannel(channel);
      activeSubscriptions.delete(key);
    };

    activeSubscriptions.set(key, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Unsubscribe from a specific subscription
   */
  static unsubscribe(key: string): void {
    const subscription = activeSubscriptions.get(key);
    if (subscription) {
      try {
        // Use the SafeRealtimeClient to safely remove the channel
        SafeRealtimeClient.safeRemoveChannel(subscription.channel);
        subscription.unsubscribe();
      } catch (error) {
        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          console.error(`Error unsubscribing from ${key}:`, error);
        }
      } finally {
        activeSubscriptions.delete(key);
        // Also remove the channel configuration
        channelConfigurations.delete(key);
      }
    }
  }

  /**
   * Unsubscribe from all subscriptions
   */
  static unsubscribeAll(): void {
    const keys = Array.from(activeSubscriptions.keys());

    // Unsubscribe from each subscription individually to handle errors
    keys.forEach((key) => {
      this.unsubscribe(key);
    });

    // Clear the maps as a final step
    activeSubscriptions.clear();
    channelConfigurations.clear();
  }

  /**
   * Initialize Realtime authentication
   * This should be called when the user logs in
   */
  static async initializeRealtimeAuth(): Promise<boolean> {
    try {
      const success = await SafeRealtimeClient.setupRealtimeAuth();

      // Set up event listener for channel retries
      if (typeof window !== 'undefined') {
        // Remove any existing listeners to avoid duplicates
        window.removeEventListener(
          'realtime:channel:retry',
          this.handleChannelRetry
        );

        // Add the event listener
        window.addEventListener(
          'realtime:channel:retry',
          this.handleChannelRetry
        );
      }

      return success;
    } catch (error) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error initializing Realtime authentication:', error);
      }
      return false;
    }
  }

  /**
   * Handle channel retry events
   */
  private static handleChannelRetry = (event: Event) => {
    const customEvent = event as CustomEvent<{
      channelName: string;
      status: string;
    }>;
    const { channelName } = customEvent.detail;

    // Get the channel configuration
    const config = channelConfigurations.get(channelName);

    if (!config) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.warn(`No configuration found for channel: ${channelName}`);
      }
      return;
    }

    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Recreating channel: ${channelName}`);
    }

    // Wait a bit before recreating the channel
    setTimeout(() => {
      try {
        // Create a new channel with the same configuration
        const channel = SafeRealtimeClient.createBroadcastChannel(
          channelName,
          config.callback,
          config.options
        );

        // Subscribe to the new channel
        SafeRealtimeClient.safeSubscribe(channel);

        // Update the active subscription
        const key = channelName;
        const unsubscribe = () => {
          SafeRealtimeClient.safeRemoveChannel(channel);
          activeSubscriptions.delete(key);
        };

        // Store the new subscription
        activeSubscriptions.set(key, { channel, unsubscribe });
      } catch (error) {
        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          console.error(`Error recreating channel ${channelName}:`, error);
        }
      }
    }, 3000); // Wait 3 seconds before recreating
  };

  /**
   * Subscribe to a custom channel
   */
  static subscribeToCustomChannel(
    channelName: string,
    callback: (payload: any) => void,
    options: Partial<RealtimeChannelOptions> = {}
  ): () => void {
    // Clean up any existing subscription with the same key
    this.unsubscribe(channelName);

    // Store the channel configuration for retry
    channelConfigurations.set(channelName, {
      callback,
      options: {
        config: {
          private: true,
          broadcast: { self: true },
          ...(options.config || {}),
        },
      },
    });

    // Create a broadcast channel with all event handlers
    const channel = SafeRealtimeClient.createBroadcastChannel(
      channelName,
      callback,
      {
        config: {
          private: true,
          broadcast: { self: true },
          ...(options.config || {}),
        },
      }
    );

    // Safely subscribe to the channel with retry logic
    SafeRealtimeClient.safeSubscribe(channel);

    // Store the unsubscribe function
    const unsubscribe = () => {
      SafeRealtimeClient.safeRemoveChannel(channel);
      activeSubscriptions.delete(channelName);
    };

    activeSubscriptions.set(channelName, { channel, unsubscribe });
    return unsubscribe;
  }

  /**
   * Get the count of active subscriptions
   */
  static getActiveSubscriptionCount(): number {
    return activeSubscriptions.size;
  }
}

// Export a singleton instance
export default RealtimeService;

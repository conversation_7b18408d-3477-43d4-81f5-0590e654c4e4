-- Create document_attachments table
CREATE TABLE IF NOT EXISTS public.document_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES public.documents(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_url TEXT NOT NULL,
    uploaded_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    description TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT false
);

-- Add RLS policies
ALTER TABLE public.document_attachments ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view attachments for documents they own
CREATE POLICY "Users can view their own document attachments" 
ON public.document_attachments
FOR SELECT
USING (
    uploaded_by = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.documents d
        WHERE d.id = document_id AND d.owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.document_collaborations dc
        WHERE dc.document_id = document_attachments.document_id AND dc.user_id = auth.uid()
    )
);

-- Policy: Users can insert attachments for documents they own or collaborate on
CREATE POLICY "Users can insert attachments for their documents" 
ON public.document_attachments
FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.documents d
        WHERE d.id = document_id AND d.owner_id = auth.uid()
    ) OR
    EXISTS (
        SELECT 1 FROM public.document_collaborations dc
        WHERE dc.document_id = document_attachments.document_id AND dc.user_id = auth.uid() AND dc.permission IN ('edit', 'admin')
    )
);

-- Policy: Users can update attachments they uploaded
CREATE POLICY "Users can update their own attachments" 
ON public.document_attachments
FOR UPDATE
USING (uploaded_by = auth.uid())
WITH CHECK (uploaded_by = auth.uid());

-- Policy: Users can delete attachments they uploaded or for documents they own
CREATE POLICY "Users can delete their own attachments" 
ON public.document_attachments
FOR DELETE
USING (
    uploaded_by = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.documents d
        WHERE d.id = document_id AND d.owner_id = auth.uid()
    )
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_document_attachments_document_id ON public.document_attachments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_attachments_uploaded_by ON public.document_attachments(uploaded_by);

-- Add document_attachments to realtime publication
BEGIN;
  INSERT INTO supabase_realtime.subscription (publication, name, tables)
  VALUES ('supabase_realtime', 'document_attachments_changes', '{public.document_attachments}')
  ON CONFLICT (publication, name) DO UPDATE
  SET tables = '{public.document_attachments}';
COMMIT;

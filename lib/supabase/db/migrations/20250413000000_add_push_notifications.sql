-- Create push_subscriptions table
CREATE TABLE IF NOT EXISTS public.push_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON public.push_subscriptions(user_id);

-- Set up RLS policies
ALTER TABLE public.push_subscriptions ENABLE ROW LEVEL SECURITY;

-- Users can read their own push subscriptions
CREATE POLICY "Users can read their own push subscriptions" ON public.push_subscriptions
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own push subscriptions
CREATE POLICY "Users can insert their own push subscriptions" ON public.push_subscriptions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own push subscriptions
CREATE POLICY "Users can update their own push subscriptions" ON public.push_subscriptions
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own push subscriptions
CREATE POLICY "Users can delete their own push subscriptions" ON public.push_subscriptions
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create function to send push notification
CREATE OR REPLACE FUNCTION send_push_notification(
  user_uuid UUID,
  title TEXT,
  body TEXT,
  url TEXT DEFAULT NULL,
  icon TEXT DEFAULT NULL,
  tag TEXT DEFAULT NULL,
  data JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  subscription_record RECORD;
  success BOOLEAN := FALSE;
BEGIN
  -- Get all subscriptions for the user
  FOR subscription_record IN
    SELECT subscription
    FROM public.push_subscriptions
    WHERE user_id = user_uuid
  LOOP
    -- In a real implementation, this would make an HTTP request to a push service
    -- For now, we'll just log the notification
    RAISE NOTICE 'Sending push notification to user %: %', user_uuid, body;
    
    -- Mark as successful if we found at least one subscription
    success := TRUE;
  END LOOP;
  
  RETURN success;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to send notification when a consultation is created
CREATE OR REPLACE FUNCTION notify_consultation_created() RETURNS TRIGGER AS $$
BEGIN
  -- Notify the client
  PERFORM send_push_notification(
    NEW.user_id,
    'Consultation Booked',
    'Your consultation has been scheduled',
    NULL,
    NULL,
    'consultation_' || NEW.id,
    jsonb_build_object(
      'consultation_id', NEW.id,
      'consultation_date', NEW.consultation_date
    )
  );
  
  -- Notify the lawyer
  PERFORM send_push_notification(
    (SELECT user_id FROM lawyers WHERE id = NEW.lawyer_id),
    'New Consultation',
    'A new consultation has been scheduled',
    NULL,
    NULL,
    'consultation_' || NEW.id,
    jsonb_build_object(
      'consultation_id', NEW.id,
      'consultation_date', NEW.consultation_date
    )
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to send notification when a consultation is updated
CREATE OR REPLACE FUNCTION notify_consultation_updated() RETURNS TRIGGER AS $$
BEGIN
  -- Only send notification if status changed
  IF OLD.status <> NEW.status THEN
    -- Notify the client
    PERFORM send_push_notification(
      NEW.user_id,
      'Consultation Updated',
      'Your consultation status has been updated to ' || NEW.status,
      NULL,
      NULL,
      'consultation_' || NEW.id,
      jsonb_build_object(
        'consultation_id', NEW.id,
        'consultation_date', NEW.consultation_date,
        'status', NEW.status
      )
    );
    
    -- Notify the lawyer
    PERFORM send_push_notification(
      (SELECT user_id FROM lawyers WHERE id = NEW.lawyer_id),
      'Consultation Updated',
      'A consultation status has been updated to ' || NEW.status,
      NULL,
      NULL,
      'consultation_' || NEW.id,
      jsonb_build_object(
        'consultation_id', NEW.id,
        'consultation_date', NEW.consultation_date,
        'status', NEW.status
      )
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
DROP TRIGGER IF EXISTS consultation_created_trigger ON public.lawyer_consultations;
CREATE TRIGGER consultation_created_trigger
  AFTER INSERT ON public.lawyer_consultations
  FOR EACH ROW
  EXECUTE FUNCTION notify_consultation_created();

DROP TRIGGER IF EXISTS consultation_updated_trigger ON public.lawyer_consultations;
CREATE TRIGGER consultation_updated_trigger
  AFTER UPDATE ON public.lawyer_consultations
  FOR EACH ROW
  EXECUTE FUNCTION notify_consultation_updated();

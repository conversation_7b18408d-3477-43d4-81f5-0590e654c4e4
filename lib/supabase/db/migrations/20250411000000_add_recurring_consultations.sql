-- Create recurring_consultations table
CREATE TABLE IF NOT EXISTS public.recurring_consultations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lawyer_id UUID NOT NULL REFERENCES public.lawyers(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  day_of_week SMALLINT NOT NULL, -- 0-6, where 0 is Sunday
  start_time TIME NOT NULL,
  duration_minutes INTEGER NOT NULL,
  frequency VARCHAR(20) NOT NULL, -- 'weekly', 'biweekly', 'monthly'
  start_date DATE NOT NULL,
  end_date DATE,
  document_id UUID REFERENCES public.documents(id) ON DELETE SET NULL,
  consultation_type VARCHAR(20) NOT NULL DEFAULT 'video', -- 'video', 'document'
  location VARCHAR(255),
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_recurring_consultations_lawyer_id ON public.recurring_consultations(lawyer_id);
CREATE INDEX IF NOT EXISTS idx_recurring_consultations_user_id ON public.recurring_consultations(user_id);
CREATE INDEX IF NOT EXISTS idx_recurring_consultations_is_active ON public.recurring_consultations(is_active);

-- Set up RLS policies
ALTER TABLE public.recurring_consultations ENABLE ROW LEVEL SECURITY;

-- Lawyers can read their own recurring consultations
CREATE POLICY "Lawyers can read their own recurring consultations" ON public.recurring_consultations
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.lawyers WHERE id = lawyer_id
    ) OR auth.uid() = user_id
  );

-- Lawyers can insert their own recurring consultations
CREATE POLICY "Lawyers can insert their own recurring consultations" ON public.recurring_consultations
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.lawyers WHERE id = lawyer_id
    ) OR auth.uid() = user_id
  );

-- Lawyers can update their own recurring consultations
CREATE POLICY "Lawyers can update their own recurring consultations" ON public.recurring_consultations
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.lawyers WHERE id = lawyer_id
    ) OR auth.uid() = user_id
  );

-- Lawyers can delete their own recurring consultations
CREATE POLICY "Lawyers can delete their own recurring consultations" ON public.recurring_consultations
  FOR DELETE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.lawyers WHERE id = lawyer_id
    ) OR auth.uid() = user_id
  );

-- Add a column to lawyer_consultations to track if it was created from a recurring consultation
ALTER TABLE public.lawyer_consultations ADD COLUMN IF NOT EXISTS recurring_consultation_id UUID REFERENCES public.recurring_consultations(id) ON DELETE SET NULL;
CREATE INDEX IF NOT EXISTS idx_lawyer_consultations_recurring_id ON public.lawyer_consultations(recurring_consultation_id);

-- Create a function to generate consultations from recurring consultations
CREATE OR REPLACE FUNCTION generate_recurring_consultations(
  recurring_id UUID,
  generate_until_date DATE DEFAULT NULL
)
RETURNS TABLE (
  consultation_id UUID,
  consultation_date TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  rec_consultation RECORD;
  next_date DATE;
  end_date DATE;
  consultation_id UUID;
  consultation_date TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get the recurring consultation
  SELECT * INTO rec_consultation FROM public.recurring_consultations WHERE id = recurring_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Recurring consultation not found';
  END IF;
  
  -- Set the end date for generation
  IF generate_until_date IS NULL THEN
    -- Default to 3 months from now if not specified
    generate_until_date := CURRENT_DATE + INTERVAL '3 months';
  END IF;
  
  -- If the recurring consultation has an end date, use the earlier of the two
  IF rec_consultation.end_date IS NOT NULL AND rec_consultation.end_date < generate_until_date THEN
    end_date := rec_consultation.end_date;
  ELSE
    end_date := generate_until_date;
  END IF;
  
  -- Start from the start date or today, whichever is later
  next_date := GREATEST(rec_consultation.start_date, CURRENT_DATE);
  
  -- Adjust to the next occurrence of the day of week
  next_date := next_date + ((rec_consultation.day_of_week - EXTRACT(DOW FROM next_date) + 7) % 7) * INTERVAL '1 day';
  
  -- Generate consultations until the end date
  WHILE next_date <= end_date LOOP
    -- Check if a consultation already exists for this date
    SELECT id INTO consultation_id FROM public.lawyer_consultations
    WHERE 
      lawyer_id = rec_consultation.lawyer_id AND
      user_id = rec_consultation.user_id AND
      recurring_consultation_id = recurring_id AND
      DATE(consultation_date) = next_date;
    
    -- If no consultation exists, create one
    IF consultation_id IS NULL THEN
      -- Create the consultation date with time
      consultation_date := next_date + rec_consultation.start_time;
      
      -- Insert the consultation
      INSERT INTO public.lawyer_consultations (
        lawyer_id,
        user_id,
        consultation_date,
        duration_minutes,
        status,
        notes,
        document_id,
        location,
        recurring_consultation_id
      ) VALUES (
        rec_consultation.lawyer_id,
        rec_consultation.user_id,
        consultation_date,
        rec_consultation.duration_minutes,
        'scheduled',
        rec_consultation.description,
        rec_consultation.document_id,
        rec_consultation.location,
        recurring_id
      ) RETURNING id INTO consultation_id;
      
      -- Return the new consultation
      RETURN QUERY SELECT consultation_id, consultation_date;
    END IF;
    
    -- Move to the next occurrence based on frequency
    CASE rec_consultation.frequency
      WHEN 'weekly' THEN
        next_date := next_date + INTERVAL '1 week';
      WHEN 'biweekly' THEN
        next_date := next_date + INTERVAL '2 weeks';
      WHEN 'monthly' THEN
        next_date := next_date + INTERVAL '1 month';
      ELSE
        -- Default to weekly
        next_date := next_date + INTERVAL '1 week';
    END CASE;
  END LOOP;
  
  RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to cancel all future consultations from a recurring consultation
CREATE OR REPLACE FUNCTION cancel_future_recurring_consultations(
  recurring_id UUID,
  from_date DATE DEFAULT CURRENT_DATE
)
RETURNS INTEGER AS $$
DECLARE
  affected_rows INTEGER;
BEGIN
  UPDATE public.lawyer_consultations
  SET status = 'cancelled', updated_at = now()
  WHERE 
    recurring_consultation_id = recurring_id AND
    DATE(consultation_date) >= from_date AND
    status = 'scheduled';
  
  GET DIAGNOSTICS affected_rows = ROW_COUNT;
  RETURN affected_rows;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

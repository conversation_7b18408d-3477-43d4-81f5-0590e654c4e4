-- Fix the infinite recursion in project_members policy
DROP POLICY IF EXISTS "Users can view project members" ON public.project_members;

-- Create a fixed policy that avoids the recursion
CREATE POLICY "Users can view project members fixed"
  ON public.project_members
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.projects
      WHERE id = project_id AND owner_id = auth.uid()
    )
  );

-- Fix other policies that might have the same issue
DROP POLICY IF EXISTS "Project owners and admins can insert project members" ON public.project_members;
CREATE POLICY "Project owners and admins can insert project members fixed"
  ON public.project_members
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.projects
      WHERE id = project_id AND owner_id = auth.uid()
    ) OR
    (
      user_id = auth.uid() AND
      EXISTS (
        SELECT 1 FROM public.project_members
        WHERE project_id = NEW.project_id AND user_id = auth.uid() AND role IN ('owner', 'admin')
      )
    )
  );

DROP POLICY IF EXISTS "Project owners and admins can update project members" ON public.project_members;
CREATE POLICY "Project owners and admins can update project members fixed"
  ON public.project_members
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.projects
      WHERE id = project_id AND owner_id = auth.uid()
    ) OR
    (
      user_id = auth.uid() AND
      EXISTS (
        SELECT 1 FROM public.project_members
        WHERE project_id = project_members.project_id AND user_id = auth.uid() AND role IN ('owner', 'admin')
      )
    )
  );

DROP POLICY IF EXISTS "Project owners and admins can delete project members" ON public.project_members;
CREATE POLICY "Project owners and admins can delete project members fixed"
  ON public.project_members
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.projects
      WHERE id = project_id AND owner_id = auth.uid()
    ) OR
    (
      user_id = auth.uid() AND
      EXISTS (
        SELECT 1 FROM public.project_members
        WHERE project_id = project_members.project_id AND user_id = auth.uid() AND role IN ('owner', 'admin')
      )
    )
  );

-- Add unsubscribe functionality to the database
-- This migration adds unsubscribe fields to waitlist and creates user preferences and unsubscribe tracking tables

-- Add unsubscribe fields to waitlist table
ALTER TABLE public.waitlist 
ADD COLUMN IF NOT EXISTS unsubscribed BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS unsubscribed_at TIMESTAMP WITH TIME ZONE;

-- Create user_preferences table for email preferences
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL UNIQUE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email_notifications BOOLEAN DEFAULT true,
  marketing_emails BOOLEAN DEFAULT true,
  document_notifications BOOLEAN DEFAULT true,
  waitlist_emails BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create email_unsubscribes table for tracking unsubscribe actions
CREATE TABLE IF NOT EXISTS public.email_unsubscribes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('waitlist', 'notifications', 'all')),
  unsubscribed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_waitlist_unsubscribed ON public.waitlist(unsubscribed);
CREATE INDEX IF NOT EXISTS idx_waitlist_email_unsubscribed ON public.waitlist(email, unsubscribed);
CREATE INDEX IF NOT EXISTS idx_user_preferences_email ON public.user_preferences(email);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON public.user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_email_unsubscribes_email ON public.email_unsubscribes(email);
CREATE INDEX IF NOT EXISTS idx_email_unsubscribes_type ON public.email_unsubscribes(type);
CREATE INDEX IF NOT EXISTS idx_email_unsubscribes_created_at ON public.email_unsubscribes(created_at);

-- Set up RLS policies for user_preferences
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Users can read their own preferences
CREATE POLICY "Users can read their own preferences" ON public.user_preferences
  FOR SELECT
  USING (
    auth.uid() = user_id OR 
    auth.jwt() ->> 'email' = email
  );

-- Users can update their own preferences
CREATE POLICY "Users can update their own preferences" ON public.user_preferences
  FOR UPDATE
  USING (
    auth.uid() = user_id OR 
    auth.jwt() ->> 'email' = email
  );

-- Anyone can insert preferences (for unsubscribe functionality)
CREATE POLICY "Anyone can insert preferences" ON public.user_preferences
  FOR INSERT
  WITH CHECK (true);

-- Admins can manage all preferences
CREATE POLICY "Admins can manage all preferences" ON public.user_preferences
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND user_role = 'admin'
    )
  );

-- Set up RLS policies for email_unsubscribes
ALTER TABLE public.email_unsubscribes ENABLE ROW LEVEL SECURITY;

-- Anyone can insert unsubscribe records (for public unsubscribe functionality)
CREATE POLICY "Anyone can insert unsubscribe records" ON public.email_unsubscribes
  FOR INSERT
  WITH CHECK (true);

-- Users can read their own unsubscribe records
CREATE POLICY "Users can read their own unsubscribe records" ON public.email_unsubscribes
  FOR SELECT
  USING (
    auth.jwt() ->> 'email' = email OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND user_role = 'admin'
    )
  );

-- Admins can read all unsubscribe records
CREATE POLICY "Admins can read all unsubscribe records" ON public.email_unsubscribes
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND user_role = 'admin'
    )
  );

-- Update waitlist RLS policies to allow updates for unsubscribe
CREATE POLICY "Anyone can update waitlist unsubscribe status" ON public.waitlist
  FOR UPDATE
  USING (true)
  WITH CHECK (
    -- Only allow updating unsubscribe fields
    OLD.email = NEW.email AND
    OLD.full_name = NEW.full_name AND
    OLD.role = NEW.role AND
    OLD.created_at = NEW.created_at
  );

-- Add comments for documentation
COMMENT ON COLUMN public.waitlist.unsubscribed IS 'Whether the user has unsubscribed from waitlist emails';
COMMENT ON COLUMN public.waitlist.unsubscribed_at IS 'Timestamp when user unsubscribed from waitlist emails';
COMMENT ON TABLE public.user_preferences IS 'User email preferences for different types of communications';
COMMENT ON TABLE public.email_unsubscribes IS 'Log of all unsubscribe actions for audit and compliance';

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add trigger to user_preferences table
CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON public.user_preferences 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

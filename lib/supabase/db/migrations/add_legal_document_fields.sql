-- Add new fields to the documents table for legal document support

-- Add jurisdiction column
ALTER TABLE documents
ADD COLUMN IF NOT EXISTS jurisdiction TEXT;

-- Add category column
ALTER TABLE documents
ADD COLUMN IF NOT EXISTS category TEXT;

-- Add permalink column
ALTER TABLE documents
ADD COLUMN IF NOT EXISTS permalink TEXT UNIQUE;

-- Add legal_metadata column (JSONB)
ALTER TABLE documents
ADD COLUMN IF NOT EXISTS legal_metadata JSONB;

-- Add export tracking columns
ALTER TABLE documents
ADD COLUMN IF NOT EXISTS last_exported_at TIMESTAMPTZ;

ALTER TABLE documents
ADD COLUMN IF NOT EXISTS export_count INTEGER DEFAULT 0;

-- Create index on jurisdiction for faster queries
CREATE INDEX IF NOT EXISTS idx_documents_jurisdiction ON documents(jurisdiction);

-- Create index on category for faster queries
CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category);

-- Create index on permalink for faster queries
CREATE INDEX IF NOT EXISTS idx_documents_permalink ON documents(permalink);

-- Update document_summaries_view to include new fields
DROP VIEW IF EXISTS document_summaries_view;
CREATE VIEW document_summaries_view AS
SELECT 
    d.id,
    d.title,
    d.description,
    d.document_type,
    d.status,
    d.is_template,
    d.created_at,
    d.updated_at,
    d.owner_id,
    u.username as owner_username,
    u.full_name as owner_full_name,
    d.jurisdiction,
    d.category,
    d.permalink,
    d.export_count,
    d.last_exported_at,
    ARRAY(
        SELECT t.name
        FROM document_to_tags dt
        JOIN document_tags t ON dt.tag_id = t.id
        WHERE dt.document_id = d.id
    ) as tags
FROM 
    documents d
LEFT JOIN 
    profiles u ON d.owner_id = u.id;

-- Add function to generate permalink if not provided
CREATE OR REPLACE FUNCTION generate_document_permalink()
RETURNS TRIGGER AS $$
BEGIN
    -- If permalink not provided, generate one
    IF NEW.permalink IS NULL THEN
        -- Convert title to lowercase, replace spaces with hyphens
        NEW.permalink := LOWER(REGEXP_REPLACE(NEW.title, '[^a-zA-Z0-9]', '-', 'g'));
        -- Add timestamp to ensure uniqueness
        NEW.permalink := NEW.permalink || '-' || FLOOR(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP))::TEXT;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to generate permalink automatically
DROP TRIGGER IF EXISTS ensure_document_permalink ON documents;
CREATE TRIGGER ensure_document_permalink
BEFORE INSERT ON documents
FOR EACH ROW
EXECUTE FUNCTION generate_document_permalink();

-- Add sample legal disclaimers for common jurisdictions
CREATE TABLE IF NOT EXISTS legal_disclaimers (
    jurisdiction TEXT PRIMARY KEY,
    disclaimer TEXT NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample disclaimers
INSERT INTO legal_disclaimers (jurisdiction, disclaimer) VALUES
('US', 'This document is provided "as is" without warranty of any kind. The user assumes all risks associated with its use. This document is not a substitute for legal advice. If you require specific legal advice, please consult with a licensed attorney in your jurisdiction.'),
('CA', 'This document is provided for informational purposes only and does not constitute legal advice. It may not reflect current legal developments in Canada. The user assumes all risks associated with its use.'),
('UK', 'This document is not a substitute for professional legal advice. You should consult a solicitor for advice specific to your situation. The user assumes all risks associated with its use.')
ON CONFLICT (jurisdiction) DO NOTHING; 
-- Update document_share_links table to add new sharing options
ALTER TABLE IF EXISTS public.document_share_links 
ADD COLUMN IF NOT EXISTS access_type VARCHAR(20) DEFAULT 'public' NOT NULL,
ADD COLUMN IF NOT EXISTS access_pin VARCHAR(10) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS edit_password VARCHAR(100) DEFAULT NULL;

-- Add comments to explain the new columns
COMMENT ON COLUMN public.document_share_links.access_type IS 'Type of access: public, pin_protected, or password_protected';
COMMENT ON COLUMN public.document_share_links.access_pin IS 'PIN code for pin_protected access (4 digits)';
COMMENT ON COLUMN public.document_share_links.edit_password IS 'Password hash for password_protected editing';

-- Create a function to hash passwords
CREATE OR REPLACE FUNCTION hash_password(password TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN crypt(password, gen_salt('bf'));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to verify passwords
CREATE OR <PERSON>EP<PERSON>CE FUNCTION verify_password(password TEXT, hash TEXT)
RET<PERSON>NS BOOLEAN AS $$
BEGIN
  RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the pgcrypto extension is enabled
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Update the track_share_link_access function to handle PIN verification
CREATE OR REPLACE FUNCTION public.track_share_link_access(link_id UUID, provided_pin TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
  link_record RECORD;
  is_valid BOOLEAN := FALSE;
BEGIN
  -- Get the share link record
  SELECT * INTO link_record FROM public.document_share_links WHERE id = link_id;
  
  -- Check if the link exists and is active
  IF link_record IS NULL OR NOT link_record.is_active THEN
    RETURN FALSE;
  END IF;
  
  -- Check if the link has expired
  IF link_record.expires_at IS NOT NULL AND link_record.expires_at < NOW() THEN
    RETURN FALSE;
  END IF;
  
  -- Check PIN if access type is pin_protected
  IF link_record.access_type = 'pin_protected' THEN
    IF provided_pin IS NULL OR provided_pin != link_record.access_pin THEN
      RETURN FALSE;
    END IF;
  END IF;
  
  -- Update the access count and last accessed timestamp
  UPDATE public.document_share_links
  SET 
    access_count = access_count + 1,
    last_accessed_at = NOW()
  WHERE id = link_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add automatic profile creation trigger for new users
-- This migration creates a trigger that automatically creates a profile when a new user signs up

-- Create a function to automatically create a profile when a new user is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  username_base TEXT;
  username_final TEXT;
  counter INTEGER := 1;
  max_attempts INTEGER := 100;
BEGIN
  -- Generate a base username from email (before @)
  username_base := LOWER(SPLIT_PART(NEW.email, '@', 1));
  
  -- Remove special characters and ensure it's alphanumeric
  username_base := REGEXP_REPLACE(username_base, '[^a-z0-9]', '', 'g');
  
  -- Ensure username is at least 3 characters
  IF LENGTH(username_base) < 3 THEN
    username_base := username_base || 'user';
  END IF;
  
  -- Truncate if too long (max 50 characters for username)
  IF LENGTH(username_base) > 45 THEN
    username_base := LEFT(username_base, 45);
  END IF;
  
  username_final := username_base;
  
  -- Check if username exists and find a unique one
  WHILE EXISTS (SELECT 1 FROM public.profiles WHERE username = username_final) AND counter <= max_attempts LOOP
    username_final := username_base || counter::TEXT;
    counter := counter + 1;
  END LOOP;
  
  -- If we couldn't find a unique username after max attempts, use timestamp
  IF counter > max_attempts THEN
    username_final := username_base || EXTRACT(EPOCH FROM NOW())::INTEGER::TEXT;
  END IF;
  
  -- Insert the new profile with default values
  INSERT INTO public.profiles (
    id,
    username,
    full_name,
    email,
    avatar_url,
    is_onboarded,
    user_role,
    plan,
    updated_at
  ) VALUES (
    NEW.id,
    username_final,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name', 'User'),
    NEW.email,
    NEW.raw_user_meta_data->>'avatar_url',
    false,
    'user',
    'Free',
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on auth.users table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- Add RLS policies for profiles table if they don't exist
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own profile
CREATE POLICY IF NOT EXISTS "Users can view their own profile"
  ON public.profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Policy for users to update their own profile
CREATE POLICY IF NOT EXISTS "Users can update their own profile"
  ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Policy for users to insert their own profile (for manual creation)
CREATE POLICY IF NOT EXISTS "Users can insert their own profile"
  ON public.profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Policy for admins to manage all profiles
CREATE POLICY IF NOT EXISTS "Admins can manage all profiles"
  ON public.profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND user_role = 'admin'
    )
  );

-- Add comment to the function
COMMENT ON FUNCTION public.handle_new_user() IS 'Automatically creates a profile when a new user signs up through Supabase Auth';

-- Add mock collaboration projects and tasks

-- Insert mock data for projects
INSERT INTO public.projects (name, description, owner_id, status, due_date)
VALUES
  ('Contract Management System', 'A project to develop a contract management system', auth.uid(), 'active', (NOW() + INTERVAL '30 days')),
  ('Legal Document Templates', 'Creating standardized legal document templates', auth.uid(), 'active', (NOW() + INTERVAL '15 days'));

-- Insert mock data for project_members
INSERT INTO public.project_members (project_id, user_id, role)
SELECT 
  id, 
  auth.uid(), 
  'owner'
FROM public.projects 
WHERE name IN ('Contract Management System', 'Legal Document Templates');

-- Insert mock data for project_tasks
INSERT INTO public.project_tasks (project_id, title, description, status, priority, assigned_to, due_date, created_by)
SELECT 
  id, 
  'Design Database Schema', 
  'Create the database schema for the contract management system', 
  'completed', 
  'high', 
  auth.uid(), 
  (NOW() - INTERVAL '5 days'), 
  auth.uid()
FROM public.projects 
WHERE name = 'Contract Management System';

INSERT INTO public.project_tasks (project_id, title, description, status, priority, assigned_to, due_date, created_by)
SELECT 
  id, 
  'Implement User Authentication', 
  'Set up user authentication and authorization', 
  'in_progress', 
  'high', 
  auth.uid(), 
  (NOW() + INTERVAL '3 days'), 
  auth.uid()
FROM public.projects 
WHERE name = 'Contract Management System';

INSERT INTO public.project_tasks (project_id, title, description, status, priority, assigned_to, due_date, created_by)
SELECT 
  id, 
  'Research Legal Requirements', 
  'Research legal requirements for different jurisdictions', 
  'in_progress', 
  'medium', 
  auth.uid(), 
  (NOW() + INTERVAL '2 days'), 
  auth.uid()
FROM public.projects 
WHERE name = 'Legal Document Templates';

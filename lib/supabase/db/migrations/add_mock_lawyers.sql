-- Add mock lawyers and consultations

-- Insert mock data for lawyers
INSERT INTO public.lawyers (user_id, full_name, email, phone, specialization, bio, years_experience, hourly_rate, availability, avatar_url, is_verified, status)
VALUES
  ('00000000-0000-0000-0000-000000000001', '<PERSON>', '<EMAIL>', '+1234567890', ARRAY['Contract Law', 'Corporate Law'], 'Experienced corporate lawyer with over 15 years of practice.', 15, 250, '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "15:00"}}', 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=0D8ABC&color=fff', true, 'active'),
  
  ('00000000-0000-0000-0000-000000000002', '<PERSON>', '<EMAIL>', '+1987654321', ARRAY['Intellectual Property', 'Technology Law'], 'Specialized in intellectual property and technology law with focus on startups.', 8, 200, '{"monday": {"start": "10:00", "end": "18:00"}, "tuesday": {"start": "10:00", "end": "18:00"}, "wednesday": {"start": "10:00", "end": "18:00"}, "thursday": {"start": "10:00", "end": "18:00"}, "friday": {"start": "10:00", "end": "16:00"}}', 'https://ui-avatars.com/api/?name=Sarah+Johnson&background=0D8ABC&color=fff', true, 'active'),
  
  ('00000000-0000-0000-0000-000000000003', 'Michael Chen', '<EMAIL>', '+1122334455', ARRAY['Real Estate Law', 'Contract Law'], 'Expert in real estate transactions and contract negotiations.', 12, 225, '{"monday": {"start": "08:00", "end": "16:00"}, "tuesday": {"start": "08:00", "end": "16:00"}, "wednesday": {"start": "08:00", "end": "16:00"}, "thursday": {"start": "08:00", "end": "16:00"}, "friday": {"start": "08:00", "end": "14:00"}}', 'https://ui-avatars.com/api/?name=Michael+Chen&background=0D8ABC&color=fff', true, 'active')
ON CONFLICT DO NOTHING;

-- Insert mock data for lawyer_consultations
INSERT INTO public.lawyer_consultations (lawyer_id, client_id, status, scheduled_at, duration_minutes, consultation_type, meeting_link, notes, price, payment_status)
SELECT 
  id, 
  auth.uid(), 
  'scheduled', 
  (NOW() + INTERVAL '3 days'), 
  60, 
  'video', 
  'https://meet.example.com/abc123', 
  'Discuss employment agreement terms', 
  250, 
  'paid'
FROM public.lawyers 
WHERE full_name = 'John Smith';

-- Add sample data for collaboration features

-- Helper function to get user IDs
CREATE OR <PERSON>EPLACE FUNCTION get_user_id_by_email(email_param TEXT)
RETURNS UUID AS $$
DECLARE
  user_id UUID;
BEGIN
  SELECT id INTO user_id FROM auth.users WHERE email = email_param;
  RETURN user_id;
END;
$$ LANGUAGE plpgsql;

-- Insert sample projects
INSERT INTO public.projects (id, name, description, owner_id, status, start_date, end_date)
VALUES
  (
    '11111111-1111-1111-1111-111111111111',
    'Contract Management System',
    'A project to develop a comprehensive contract management system for legal documents',
    get_user_id_by_email('<EMAIL>'),
    'active',
    NOW() - INTERVAL '30 days',
    NOW() + INTERVAL '60 days'
  ),
  (
    '*************-2222-2222-************',
    'Legal Document Templates',
    'Creating standardized legal document templates for common use cases',
    get_user_id_by_email('<EMAIL>'),
    'active',
    NOW() - INTERVAL '15 days',
    NOW() + INTERVAL '45 days'
  ),
  (
    '*************-3333-3333-************',
    'Client Onboarding Process',
    'Streamlining the client onboarding process with automated document generation',
    get_user_id_by_email('<EMAIL>'),
    'active',
    NOW() - INTERVAL '10 days',
    NOW() + INTERVAL '20 days'
  ),
  (
    '44444444-4444-4444-4444-444444444444',
    'Compliance Documentation',
    'Developing compliance documentation for regulatory requirements',
    get_user_id_by_email('<EMAIL>'),
    'completed',
    NOW() - INTERVAL '60 days',
    NOW() - INTERVAL '10 days'
  ),
  (
    '55555555-5555-5555-5555-555555555555',
    'Legal Research Database',
    'Creating a searchable database of legal precedents and research materials',
    get_user_id_by_email('<EMAIL>'),
    'archived',
    NOW() - INTERVAL '90 days',
    NOW() - INTERVAL '30 days'
  );

-- Insert project members
INSERT INTO public.project_members (project_id, user_id, role)
VALUES
  -- Contract Management System
  (
    '11111111-1111-1111-1111-111111111111',
    get_user_id_by_email('<EMAIL>'),
    'owner'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    get_user_id_by_email('<EMAIL>'),
    'admin'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    get_user_id_by_email('<EMAIL>'),
    'member'
  ),
  
  -- Legal Document Templates
  (
    '*************-2222-2222-************',
    get_user_id_by_email('<EMAIL>'),
    'owner'
  ),
  (
    '*************-2222-2222-************',
    get_user_id_by_email('<EMAIL>'),
    'member'
  ),
  
  -- Client Onboarding Process
  (
    '*************-3333-3333-************',
    get_user_id_by_email('<EMAIL>'),
    'owner'
  ),
  (
    '*************-3333-3333-************',
    get_user_id_by_email('<EMAIL>'),
    'member'
  ),
  
  -- Compliance Documentation
  (
    '44444444-4444-4444-4444-444444444444',
    get_user_id_by_email('<EMAIL>'),
    'owner'
  ),
  (
    '44444444-4444-4444-4444-444444444444',
    get_user_id_by_email('<EMAIL>'),
    'viewer'
  ),
  
  -- Legal Research Database
  (
    '55555555-5555-5555-5555-555555555555',
    get_user_id_by_email('<EMAIL>'),
    'owner'
  );

-- Insert project tasks
INSERT INTO public.project_tasks (project_id, title, description, status, priority, assigned_to, due_date, created_by)
VALUES
  -- Contract Management System Tasks
  (
    '11111111-1111-1111-1111-111111111111',
    'Design Database Schema',
    'Create the database schema for the contract management system',
    'completed',
    'high',
    get_user_id_by_email('<EMAIL>'),
    NOW() - INTERVAL '20 days',
    get_user_id_by_email('<EMAIL>')
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    'Implement User Authentication',
    'Set up user authentication and authorization for the system',
    'in_progress',
    'high',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '5 days',
    get_user_id_by_email('<EMAIL>')
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    'Create Contract Templates',
    'Design and implement reusable contract templates',
    'todo',
    'medium',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '10 days',
    get_user_id_by_email('<EMAIL>')
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    'Implement Document Versioning',
    'Add support for document version history and comparison',
    'todo',
    'medium',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '15 days',
    get_user_id_by_email('<EMAIL>')
  ),
  
  -- Legal Document Templates Tasks
  (
    '*************-2222-2222-************',
    'Research Legal Requirements',
    'Research legal requirements for different jurisdictions',
    'completed',
    'high',
    get_user_id_by_email('<EMAIL>'),
    NOW() - INTERVAL '10 days',
    get_user_id_by_email('<EMAIL>')
  ),
  (
    '*************-2222-2222-************',
    'Draft Template Structure',
    'Create the structure for legal document templates',
    'in_progress',
    'high',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '3 days',
    get_user_id_by_email('<EMAIL>')
  ),
  (
    '*************-2222-2222-************',
    'Implement Variable Placeholders',
    'Add support for variable placeholders in templates',
    'todo',
    'medium',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '7 days',
    get_user_id_by_email('<EMAIL>')
  ),
  
  -- Client Onboarding Process Tasks
  (
    '*************-3333-3333-************',
    'Design Onboarding Workflow',
    'Create the workflow for client onboarding',
    'in_progress',
    'high',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '2 days',
    get_user_id_by_email('<EMAIL>')
  ),
  (
    '*************-3333-3333-************',
    'Create Client Intake Forms',
    'Design and implement client intake forms',
    'todo',
    'medium',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '5 days',
    get_user_id_by_email('<EMAIL>')
  ),
  (
    '*************-3333-3333-************',
    'Implement Document Generation',
    'Add support for automatic document generation based on client information',
    'todo',
    'medium',
    get_user_id_by_email('<EMAIL>'),
    NOW() + INTERVAL '10 days',
    get_user_id_by_email('<EMAIL>')
  );

-- Insert project comments
INSERT INTO public.project_comments (project_id, task_id, user_id, content)
VALUES
  -- Comments for Contract Management System
  (
    '11111111-1111-1111-1111-111111111111',
    (SELECT id FROM public.project_tasks WHERE project_id = '11111111-1111-1111-1111-111111111111' AND title = 'Design Database Schema'),
    get_user_id_by_email('<EMAIL>'),
    'I''ve completed the initial database schema design. Please review and provide feedback.'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    (SELECT id FROM public.project_tasks WHERE project_id = '11111111-1111-1111-1111-111111111111' AND title = 'Design Database Schema'),
    get_user_id_by_email('<EMAIL>'),
    'The schema looks good. Make sure we have fields for document expiration dates and renewal reminders.'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    (SELECT id FROM public.project_tasks WHERE project_id = '11111111-1111-1111-1111-111111111111' AND title = 'Implement User Authentication'),
    get_user_id_by_email('<EMAIL>'),
    'I''m working on the authentication system. Should we implement role-based access control?'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    (SELECT id FROM public.project_tasks WHERE project_id = '11111111-1111-1111-1111-111111111111' AND title = 'Implement User Authentication'),
    get_user_id_by_email('<EMAIL>'),
    'Yes, we definitely need role-based access control. Let''s discuss the specific roles we need.'
  ),
  
  -- Comments for Legal Document Templates
  (
    '*************-2222-2222-************',
    (SELECT id FROM public.project_tasks WHERE project_id = '*************-2222-2222-************' AND title = 'Research Legal Requirements'),
    get_user_id_by_email('<EMAIL>'),
    'I''ve completed the research for US and EU jurisdictions. We should focus on these regions first.'
  ),
  (
    '*************-2222-2222-************',
    (SELECT id FROM public.project_tasks WHERE project_id = '*************-2222-2222-************' AND title = 'Draft Template Structure'),
    get_user_id_by_email('<EMAIL>'),
    'Starting work on the template structure. I''ll focus on NDAs and service agreements first.'
  );

-- Insert project messages
INSERT INTO public.project_messages (project_id, user_id, content)
VALUES
  -- Messages for Contract Management System
  (
    '11111111-1111-1111-1111-111111111111',
    get_user_id_by_email('<EMAIL>'),
    'Welcome to the Contract Management System project! Let''s work together to build an amazing system.'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    get_user_id_by_email('<EMAIL>'),
    'Thanks for setting this up. I''m excited to contribute my legal expertise to this project.'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    get_user_id_by_email('<EMAIL>'),
    'Looking forward to working on this. I''ll focus on the technical implementation.'
  ),
  (
    '11111111-1111-1111-1111-111111111111',
    get_user_id_by_email('<EMAIL>'),
    'Great! Let''s schedule a kickoff meeting to discuss the project in detail.'
  ),
  
  -- Messages for Legal Document Templates
  (
    '*************-2222-2222-************',
    get_user_id_by_email('<EMAIL>'),
    'I''ve started working on the legal document templates. Let me know if you have any specific requirements.'
  ),
  (
    '*************-2222-2222-************',
    get_user_id_by_email('<EMAIL>'),
    'Thanks for taking the lead on this. I think we should prioritize NDAs, service agreements, and employment contracts.'
  ),
  (
    '*************-2222-2222-************',
    get_user_id_by_email('<EMAIL>'),
    'Agreed. I''ll start with those three types of documents.'
  );

-- Insert project invitations
INSERT INTO public.project_invitations (project_id, email, role, status, invited_by, message)
VALUES
  (
    '11111111-1111-1111-1111-111111111111',
    '<EMAIL>',
    'member',
    'pending',
    get_user_id_by_email('<EMAIL>'),
    'I''d like to invite you to join our Contract Management System project. Your expertise would be valuable.'
  ),
  (
    '*************-2222-2222-************',
    '<EMAIL>',
    'member',
    'pending',
    get_user_id_by_email('<EMAIL>'),
    'We need your legal expertise for our document templates project. Please join us!'
  );

-- Create sample documents if they don't exist
DO $$
DECLARE
  doc1_id UUID;
  doc2_id UUID;
  doc3_id UUID;
BEGIN
  -- Check if documents table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'documents') THEN
    -- Insert sample documents if they don't exist
    IF NOT EXISTS (SELECT FROM public.documents WHERE title = 'Non-Disclosure Agreement Template') THEN
      INSERT INTO public.documents (title, description, document_type, status, owner_id, content)
      VALUES (
        'Non-Disclosure Agreement Template',
        'Standard NDA template for business discussions',
        'agreement',
        'published',
        get_user_id_by_email('<EMAIL>'),
        '{"sections":[{"title":"Parties","content":"This Non-Disclosure Agreement is made between [Party A] and [Party B]."},{"title":"Confidential Information","content":"Both parties agree to keep confidential any proprietary information disclosed during business discussions."},{"title":"Term","content":"This Agreement shall remain in effect for [Duration] from the date of signing."},{"title":"Governing Law","content":"This Agreement shall be governed by the laws of [Jurisdiction]."}]}'
      )
      RETURNING id INTO doc1_id;
    ELSE
      SELECT id INTO doc1_id FROM public.documents WHERE title = 'Non-Disclosure Agreement Template' LIMIT 1;
    END IF;

    IF NOT EXISTS (SELECT FROM public.documents WHERE title = 'Service Agreement Template') THEN
      INSERT INTO public.documents (title, description, document_type, status, owner_id, content)
      VALUES (
        'Service Agreement Template',
        'Template for service provider agreements',
        'agreement',
        'published',
        get_user_id_by_email('<EMAIL>'),
        '{"sections":[{"title":"Parties","content":"This Service Agreement is made between [Service Provider] and [Client]."},{"title":"Services","content":"The Service Provider agrees to provide the following services: [Services Description]."},{"title":"Compensation","content":"The Client agrees to pay [Amount] for the services provided."},{"title":"Term","content":"This Agreement shall commence on [Start Date] and continue until [End Date]."}]}'
      )
      RETURNING id INTO doc2_id;
    ELSE
      SELECT id INTO doc2_id FROM public.documents WHERE title = 'Service Agreement Template' LIMIT 1;
    END IF;

    IF NOT EXISTS (SELECT FROM public.documents WHERE title = 'Employment Contract Template') THEN
      INSERT INTO public.documents (title, description, document_type, status, owner_id, content)
      VALUES (
        'Employment Contract Template',
        'Standard employment contract template',
        'contract',
        'published',
        get_user_id_by_email('<EMAIL>'),
        '{"sections":[{"title":"Parties","content":"This Employment Contract is made between [Employer] and [Employee]."},{"title":"Position","content":"The Employee is hired for the position of [Position Title]."},{"title":"Compensation","content":"The Employee shall receive a salary of [Amount] per [Period]."},{"title":"Term","content":"This Contract shall commence on [Start Date] and continue until [End Date] or until terminated by either party."}]}'
      )
      RETURNING id INTO doc3_id;
    ELSE
      SELECT id INTO doc3_id FROM public.documents WHERE title = 'Employment Contract Template' LIMIT 1;
    END IF;

    -- Link documents to projects
    IF NOT EXISTS (SELECT FROM public.project_documents WHERE project_id = '*************-2222-2222-************' AND document_id = doc1_id) THEN
      INSERT INTO public.project_documents (project_id, document_id, added_by)
      VALUES (
        '*************-2222-2222-************',
        doc1_id,
        get_user_id_by_email('<EMAIL>')
      );
    END IF;

    IF NOT EXISTS (SELECT FROM public.project_documents WHERE project_id = '*************-2222-2222-************' AND document_id = doc2_id) THEN
      INSERT INTO public.project_documents (project_id, document_id, added_by)
      VALUES (
        '*************-2222-2222-************',
        doc2_id,
        get_user_id_by_email('<EMAIL>')
      );
    END IF;

    IF NOT EXISTS (SELECT FROM public.project_documents WHERE project_id = '*************-3333-3333-************' AND document_id = doc3_id) THEN
      INSERT INTO public.project_documents (project_id, document_id, added_by)
      VALUES (
        '*************-3333-3333-************',
        doc3_id,
        get_user_id_by_email('<EMAIL>')
      );
    END IF;
  END IF;
END $$;

-- Clean up
DROP FUNCTION IF EXISTS get_user_id_by_email(TEXT);

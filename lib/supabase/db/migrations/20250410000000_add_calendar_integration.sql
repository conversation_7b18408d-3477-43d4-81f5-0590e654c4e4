-- Create calendar_integrations table to store user calendar integration data
CREATE TABLE IF NOT EXISTS public.calendar_integrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL, -- 'google', 'outlook', etc.
  access_token TEXT,
  refresh_token TEXT,
  token_expires_at TIMESTAMP WITH TIME ZONE,
  calendar_id TEXT, -- The ID of the calendar to use for events
  sync_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, provider)
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_calendar_integrations_user_id ON public.calendar_integrations(user_id);

-- Set up RLS policies
ALTER TABLE public.calendar_integrations ENABLE ROW LEVEL SECURITY;

-- Users can read their own calendar integrations
CREATE POLICY "Users can read their own calendar integrations" ON public.calendar_integrations
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own calendar integrations
CREATE POLICY "Users can insert their own calendar integrations" ON public.calendar_integrations
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own calendar integrations
CREATE POLICY "Users can update their own calendar integrations" ON public.calendar_integrations
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own calendar integrations
CREATE POLICY "Users can delete their own calendar integrations" ON public.calendar_integrations
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create consultation_calendar_events table to track external calendar events
CREATE TABLE IF NOT EXISTS public.consultation_calendar_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  consultation_id UUID NOT NULL REFERENCES public.lawyer_consultations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  external_event_id TEXT NOT NULL, -- ID of the event in the external calendar
  provider VARCHAR(50) NOT NULL, -- 'google', 'outlook', etc.
  last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(consultation_id, user_id, provider)
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_consultation_calendar_events_consultation_id ON public.consultation_calendar_events(consultation_id);
CREATE INDEX IF NOT EXISTS idx_consultation_calendar_events_user_id ON public.consultation_calendar_events(user_id);

-- Set up RLS policies
ALTER TABLE public.consultation_calendar_events ENABLE ROW LEVEL SECURITY;

-- Users can read their own calendar events
CREATE POLICY "Users can read their own calendar events" ON public.consultation_calendar_events
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own calendar events
CREATE POLICY "Users can insert their own calendar events" ON public.consultation_calendar_events
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own calendar events
CREATE POLICY "Users can update their own calendar events" ON public.consultation_calendar_events
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own calendar events
CREATE POLICY "Users can delete their own calendar events" ON public.consultation_calendar_events
  FOR DELETE
  USING (auth.uid() = user_id);

-- Add a function to get a user's calendar integrations
CREATE OR REPLACE FUNCTION get_user_calendar_integrations(user_uuid UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  provider VARCHAR(50),
  access_token TEXT,
  refresh_token TEXT,
  token_expires_at TIMESTAMP WITH TIME ZONE,
  calendar_id TEXT,
  sync_enabled BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ci.id,
    ci.user_id,
    ci.provider,
    ci.access_token,
    ci.refresh_token,
    ci.token_expires_at,
    ci.calendar_id,
    ci.sync_enabled,
    ci.created_at,
    ci.updated_at
  FROM 
    public.calendar_integrations ci
  WHERE 
    ci.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

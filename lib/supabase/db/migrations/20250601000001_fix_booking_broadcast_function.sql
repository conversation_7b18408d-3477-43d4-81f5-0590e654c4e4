-- Fix the broadcast_booking_changes function to use user_id instead of client_id
-- This migration fixes the realtime broadcast function to match the correct database schema

-- Drop and recreate the function with the correct column names
DROP FUNCTION IF EXISTS public.broadcast_booking_changes();

CREATE OR REPLACE FUNCTION public.broadcast_booking_changes()
R<PERSON><PERSON><PERSON> trigger
AS $$
BEGIN
  -- Broadcast to the client (using user_id instead of client_id)
  PERFORM realtime.broadcast_changes(
    'bookings:client:' || new.user_id::text, -- topic for client
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'lawyer_id', new.lawyer_id,
      'user_id', new.user_id,
      'document_id', new.document_id,
      'status', new.status,
      'consultation_date', new.consultation_date,
      'duration_minutes', new.duration_minutes,
      'created_at', new.created_at,
      'updated_at', new.updated_at
    ), -- record (only sending necessary fields)
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );

  -- Broadcast to the lawyer
  PERFORM realtime.broadcast_changes(
    'bookings:lawyer:' || new.lawyer_id::text, -- topic for lawyer
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'lawyer_id', new.lawyer_id,
      'user_id', new.user_id,
      'document_id', new.document_id,
      'status', new.status,
      'consultation_date', new.consultation_date,
      'duration_minutes', new.duration_minutes,
      'created_at', new.created_at,
      'updated_at', new.updated_at
    ), -- record (only sending necessary fields)
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger to ensure it uses the updated function
DROP TRIGGER IF EXISTS on_booking_changes ON public.lawyer_consultations;
CREATE TRIGGER on_booking_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.lawyer_consultations
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_booking_changes();

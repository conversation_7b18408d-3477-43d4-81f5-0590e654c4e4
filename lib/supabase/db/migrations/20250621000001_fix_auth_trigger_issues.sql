-- Fix authentication trigger issues
-- This migration addresses the persistent "Database error saving new user" issue

-- First, let's check and fix the profiles table structure
-- The issue might be column name mismatches or missing columns

-- Check if the profiles table has the correct structure
-- Based on the database types, we need to ensure column names match

-- Drop the existing trigger and function to recreate them properly
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create an improved function that handles the actual database schema
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  username_base TEXT;
  username_final TEXT;
  counter INTEGER := 1;
  max_attempts INTEGER := 100;
BEGIN
  -- Generate a base username from email (before @)
  username_base := LOWER(SPLIT_PART(NEW.email, '@', 1));
  
  -- Remove special characters and ensure it's alphanumeric
  username_base := REGEXP_REPLACE(username_base, '[^a-z0-9]', '', 'g');
  
  -- Ensure username is at least 3 characters
  IF LENGTH(username_base) < 3 THEN
    username_base := username_base || 'user';
  END IF;
  
  -- Truncate if too long (max 50 characters for username)
  IF LENGTH(username_base) > 45 THEN
    username_base := LEFT(username_base, 45);
  END IF;
  
  username_final := username_base;
  
  -- Check if username exists and find a unique one
  WHILE EXISTS (SELECT 1 FROM public.profiles WHERE username = username_final) AND counter <= max_attempts LOOP
    username_final := username_base || counter::TEXT;
    counter := counter + 1;
  END LOOP;
  
  -- If we couldn't find a unique username after max attempts, use timestamp
  IF counter > max_attempts THEN
    username_final := username_base || EXTRACT(EPOCH FROM NOW())::INTEGER::TEXT;
  END IF;
  
  -- Insert the new profile with correct column names based on database schema
  -- Note: Using user_role instead of role, and plan instead of plan_new
  INSERT INTO public.profiles (
    id,
    username,
    full_name,
    email,
    avatar_url,
    is_onboarded,
    user_role,
    plan,
    updated_at
  ) VALUES (
    NEW.id,
    username_final,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name', 'User'),
    NEW.email,
    NEW.raw_user_meta_data->>'avatar_url',
    false,
    'user'::user_role,  -- Cast to enum type
    'Free'::plan_new,   -- Cast to enum type
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error with more details
    RAISE WARNING 'Failed to create profile for user % (email: %): % - %', NEW.id, NEW.email, SQLSTATE, SQLERRM;
    -- Don't fail the user creation, just log the error
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on auth.users table
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- Ensure RLS is properly configured
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop and recreate RLS policies to ensure they're correct
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can manage all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON public.profiles;

-- Policy for users to view their own profile
CREATE POLICY "Users can view their own profile"
  ON public.profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Policy for users to update their own profile
CREATE POLICY "Users can update their own profile"
  ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Policy for users to insert their own profile (for manual creation)
CREATE POLICY "Users can insert their own profile"
  ON public.profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Policy for service role to insert profiles (for trigger)
CREATE POLICY "Service role can manage all profiles"
  ON public.profiles
  FOR ALL
  USING (auth.role() = 'service_role');

-- Policy for admins to manage all profiles
CREATE POLICY "Admins can manage all profiles"
  ON public.profiles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND user_role = 'admin'
    )
  );

-- Add helpful comments
COMMENT ON FUNCTION public.handle_new_user() IS 'Automatically creates a profile when a new user signs up through Supabase Auth. Fixed version with proper enum casting and RLS policies.';
COMMENT ON TRIGGER on_auth_user_created ON auth.users IS 'Trigger to automatically create user profiles on signup';

-- Create a test function to verify the trigger works
CREATE OR REPLACE FUNCTION public.test_profile_creation()
RETURNS TEXT AS $$
DECLARE
  test_result TEXT;
BEGIN
  -- Check if the trigger function exists
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'handle_new_user') THEN
    RETURN 'FAIL: handle_new_user function does not exist';
  END IF;
  
  -- Check if the trigger exists
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created') THEN
    RETURN 'FAIL: on_auth_user_created trigger does not exist';
  END IF;
  
  -- Check if RLS is enabled
  IF NOT EXISTS (SELECT 1 FROM pg_class WHERE relname = 'profiles' AND relrowsecurity = true) THEN
    RETURN 'FAIL: RLS is not enabled on profiles table';
  END IF;
  
  RETURN 'SUCCESS: All components are properly configured';
END;
$$ LANGUAGE plpgsql;

-- Run the test
SELECT public.test_profile_creation();

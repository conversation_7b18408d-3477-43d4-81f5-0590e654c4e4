-- Add mock documents for the current user

-- Insert mock data for documents
INSERT INTO public.documents (title, description, content, document_type, status, is_template, owner_id, metadata)
VALUES
  ('Employment Agreement', 'Standard employment agreement template', '{"sections":[{"title":"Introduction","content":"This Employment Agreement is made and entered into on [Date] by and between [Company Name] and [Employee Name]."},{"title":"Terms of Employment","content":"The Employee agrees to be employed on the terms and conditions set out in this Agreement."},{"title":"Compensation","content":"The Employee shall be paid a salary of [Amount] per [Period]."},{"title":"Termination","content":"Either party may terminate this Agreement by providing [Notice Period] notice."}]}', 'contract', 'draft', false, auth.uid(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'),
  
  ('Non-Disclosure Agreement', 'Confidentiality agreement for business discussions', '{"sections":[{"title":"Parties","content":"This Non-Disclosure Agreement is made between [Party A] and [Party B]."},{"title":"Confidential Information","content":"Both parties agree to keep confidential any proprietary information disclosed during business discussions."},{"title":"Term","content":"This Agreement shall remain in effect for [Duration] from the date of signing."},{"title":"Governing Law","content":"This Agreement shall be governed by the laws of [Jurisdiction]."}]}', 'agreement', 'published', false, auth.uid(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'),
  
  ('Rental Agreement Template', 'Template for property rental agreements', '{"sections":[{"title":"Property Details","content":"The property located at [Address] is being rented to [Tenant Name]."},{"title":"Rent","content":"The monthly rent is [Amount] due on the [Day] of each month."},{"title":"Term","content":"The lease term is [Duration] beginning on [Start Date]."},{"title":"Security Deposit","content":"A security deposit of [Amount] is required."}]}', 'contract', 'template', true, auth.uid(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'),
  
  ('Business Proposal', 'Proposal for new business venture', '{"sections":[{"title":"Executive Summary","content":"This proposal outlines a business opportunity for [Project Name]."},{"title":"Market Analysis","content":"The target market consists of [Market Description]."},{"title":"Financial Projections","content":"We project revenue of [Amount] in the first year."},{"title":"Implementation Plan","content":"The project will be implemented in [Number] phases."}]}', 'report', 'draft', false, auth.uid(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'),
  
  ('Service Agreement', 'Agreement for providing professional services', '{"sections":[{"title":"Services","content":"The Provider agrees to provide [Service Description] to the Client."},{"title":"Compensation","content":"The Client agrees to pay [Amount] for the services."},{"title":"Term","content":"This Agreement shall remain in effect until [End Date]."},{"title":"Termination","content":"Either party may terminate this Agreement with [Notice Period] notice."}]}', 'contract', 'archived', false, auth.uid(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}');

-- Create client_metadata table to store additional client information
CREATE TABLE IF NOT EXISTS public.client_metadata (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'active',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_client_metadata_client_id ON public.client_metadata(client_id);

-- Set up RLS policies
ALTER TABLE public.client_metadata ENABLE ROW LEVEL SECURITY;

-- Lawyers can read client metadata for their clients
CREATE POLICY "Lawyers can read client metadata for their clients" ON public.client_metadata
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.lawyer_consultations lc
      JOIN public.lawyers l ON lc.lawyer_id = l.id
      WHERE lc.user_id = client_id AND l.user_id = auth.uid()
    )
  );

-- Lawyers can update client metadata for their clients
CREATE POLICY "Lawyers can update client metadata for their clients" ON public.client_metadata
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.lawyer_consultations lc
      JOIN public.lawyers l ON lc.lawyer_id = l.id
      WHERE lc.user_id = client_id AND l.user_id = auth.uid()
    )
  );

-- Lawyers can insert client metadata for their clients
CREATE POLICY "Lawyers can insert client metadata for their clients" ON public.client_metadata
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.lawyer_consultations lc
      JOIN public.lawyers l ON lc.lawyer_id = l.id
      WHERE lc.user_id = client_id AND l.user_id = auth.uid()
    )
  );

-- Create a function to get client metadata
CREATE OR REPLACE FUNCTION get_client_metadata(client_uuid UUID)
RETURNS TABLE (
  id UUID,
  client_id UUID,
  status TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cm.id,
    cm.client_id,
    cm.status,
    cm.notes,
    cm.created_at,
    cm.updated_at
  FROM 
    public.client_metadata cm
  WHERE 
    cm.client_id = client_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policies for waitlist table
-- This migration adds Row Level Security policies to allow public access to the waitlist table

-- Ensure RLS is enabled on the waitlist table
ALTER TABLE public.waitlist ENABLE ROW LEVEL SECURITY;

-- Policy to allow anyone to insert into waitlist (for public signup)
CREATE POLICY "Anyone can insert into waitlist" ON public.waitlist
  FOR INSERT
  WITH CHECK (true);

-- Policy to allow anyone to read from waitlist (for checking if email exists)
-- Note: This is needed for the duplicate email check in the API
CREATE POLICY "Anyone can read waitlist" ON public.waitlist
  FOR SELECT
  USING (true);

-- Policy to allow admins to update waitlist entries
-- This allows admins to manage waitlist entries if needed
CREATE POLICY "Admins can update waitlist" ON public.waitlist
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND user_role = 'admin'
    )
  );

-- Policy to allow admins to delete waitlist entries
-- This allows admins to remove entries if needed
CREATE POLICY "Admins can delete waitlist" ON public.waitlist
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND user_role = 'admin'
    )
  );

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_waitlist_email ON public.waitlist(email);
CREATE INDEX IF NOT EXISTS idx_waitlist_role ON public.waitlist(role);
CREATE INDEX IF NOT EXISTS idx_waitlist_created_at ON public.waitlist(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.waitlist IS 'Stores waitlist signups for NotAMess Forms';
COMMENT ON COLUMN public.waitlist.email IS 'User email address (unique)';
COMMENT ON COLUMN public.waitlist.full_name IS 'User full name';
COMMENT ON COLUMN public.waitlist.role IS 'User role preference (user or lawyer)';
COMMENT ON COLUMN public.waitlist.created_at IS 'Timestamp when user joined waitlist';
COMMENT ON COLUMN public.waitlist.updated_at IS 'Timestamp when record was last updated';

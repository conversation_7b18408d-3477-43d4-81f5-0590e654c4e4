-- Create consultation_metrics view for easier reporting
CREATE OR REPLACE VIEW public.consultation_metrics AS
SELECT
  lc.id,
  lc.lawyer_id,
  lc.user_id,
  lc.consultation_date,
  lc.duration_minutes,
  lc.status,
  lc.document_id,
  lc.recurring_consultation_id,
  lc.created_at,
  lc.updated_at,
  EXTRACT(YEAR FROM lc.consultation_date) AS year,
  EXTRACT(MONTH FROM lc.consultation_date) AS month,
  EXTRACT(DOW FROM lc.consultation_date) AS day_of_week,
  EXTRACT(HOUR FROM lc.consultation_date) AS hour,
  CASE
    WHEN lc.document_id IS NOT NULL THEN 'document'
    ELSE 'video'
  END AS consultation_type,
  CASE
    WHEN lc.recurring_consultation_id IS NOT NULL THEN true
    ELSE false
  END AS is_recurring,
  l.full_name AS lawyer_name,
  l.specialization AS lawyer_specialization,
  p.full_name AS client_name,
  p.email AS client_email
FROM
  public.lawyer_consultations lc
JOIN
  public.lawyers l ON lc.lawyer_id = l.id
JOIN
  public.profiles p ON lc.user_id = p.id;

-- Create lawyer_performance_metrics view
CREATE OR REPLACE VIEW public.lawyer_performance_metrics AS
SELECT
  lawyer_id,
  lawyer_name,
  lawyer_specialization,
  COUNT(*) AS total_consultations,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) AS completed_consultations,
  COUNT(CASE WHEN status = 'cancelled' THEN 1 END) AS cancelled_consultations,
  COUNT(CASE WHEN status = 'scheduled' OR status = 'confirmed' THEN 1 END) AS upcoming_consultations,
  COUNT(CASE WHEN consultation_type = 'document' THEN 1 END) AS document_consultations,
  COUNT(CASE WHEN consultation_type = 'video' THEN 1 END) AS video_consultations,
  COUNT(CASE WHEN is_recurring THEN 1 END) AS recurring_consultations,
  SUM(duration_minutes) AS total_minutes,
  AVG(duration_minutes) AS avg_duration_minutes
FROM
  public.consultation_metrics
GROUP BY
  lawyer_id, lawyer_name, lawyer_specialization;

-- Create client_consultation_metrics view
CREATE OR REPLACE VIEW public.client_consultation_metrics AS
SELECT
  user_id AS client_id,
  client_name,
  client_email,
  COUNT(*) AS total_consultations,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) AS completed_consultations,
  COUNT(CASE WHEN status = 'cancelled' THEN 1 END) AS cancelled_consultations,
  COUNT(CASE WHEN status = 'scheduled' OR status = 'confirmed' THEN 1 END) AS upcoming_consultations,
  COUNT(CASE WHEN consultation_type = 'document' THEN 1 END) AS document_consultations,
  COUNT(CASE WHEN consultation_type = 'video' THEN 1 END) AS video_consultations,
  COUNT(CASE WHEN is_recurring THEN 1 END) AS recurring_consultations,
  SUM(duration_minutes) AS total_minutes,
  AVG(duration_minutes) AS avg_duration_minutes
FROM
  public.consultation_metrics
GROUP BY
  user_id, client_name, client_email;

-- Create time_slot_popularity view
CREATE OR REPLACE VIEW public.time_slot_popularity AS
SELECT
  day_of_week,
  hour,
  COUNT(*) AS consultation_count,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) AS completed_count,
  COUNT(CASE WHEN status = 'cancelled' THEN 1 END) AS cancelled_count
FROM
  public.consultation_metrics
GROUP BY
  day_of_week, hour
ORDER BY
  consultation_count DESC;

-- Create monthly_consultation_stats view
CREATE OR REPLACE VIEW public.monthly_consultation_stats AS
SELECT
  lawyer_id,
  lawyer_name,
  year,
  month,
  COUNT(*) AS total_consultations,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) AS completed_consultations,
  COUNT(CASE WHEN status = 'cancelled' THEN 1 END) AS cancelled_consultations,
  SUM(duration_minutes) AS total_minutes,
  SUM(CASE WHEN status = 'completed' THEN duration_minutes ELSE 0 END) AS completed_minutes
FROM
  public.consultation_metrics
GROUP BY
  lawyer_id, lawyer_name, year, month
ORDER BY
  year DESC, month DESC;

-- Create consultation_report_settings table
CREATE TABLE IF NOT EXISTS public.consultation_report_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  report_type VARCHAR(50) NOT NULL, -- 'weekly', 'monthly', 'quarterly'
  is_enabled BOOLEAN DEFAULT true,
  email_recipients TEXT[], -- Array of email addresses
  include_metrics TEXT[], -- Array of metrics to include
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, report_type)
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_consultation_report_settings_user_id ON public.consultation_report_settings(user_id);

-- Set up RLS policies
ALTER TABLE public.consultation_report_settings ENABLE ROW LEVEL SECURITY;

-- Users can read their own report settings
CREATE POLICY "Users can read their own report settings" ON public.consultation_report_settings
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own report settings
CREATE POLICY "Users can insert their own report settings" ON public.consultation_report_settings
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own report settings
CREATE POLICY "Users can update their own report settings" ON public.consultation_report_settings
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own report settings
CREATE POLICY "Users can delete their own report settings" ON public.consultation_report_settings
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create saved_reports table
CREATE TABLE IF NOT EXISTS public.saved_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  report_name VARCHAR(255) NOT NULL,
  report_type VARCHAR(50) NOT NULL, -- 'performance', 'client', 'time_slot', 'monthly'
  report_parameters JSONB,
  report_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_saved_reports_user_id ON public.saved_reports(user_id);

-- Set up RLS policies
ALTER TABLE public.saved_reports ENABLE ROW LEVEL SECURITY;

-- Users can read their own saved reports
CREATE POLICY "Users can read their own saved reports" ON public.saved_reports
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own saved reports
CREATE POLICY "Users can insert their own saved reports" ON public.saved_reports
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own saved reports
CREATE POLICY "Users can update their own saved reports" ON public.saved_reports
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own saved reports
CREATE POLICY "Users can delete their own saved reports" ON public.saved_reports
  FOR DELETE
  USING (auth.uid() = user_id);

-- Create function to generate a performance report for a lawyer
CREATE OR REPLACE FUNCTION generate_lawyer_performance_report(
  lawyer_uuid UUID,
  start_date DATE DEFAULT NULL,
  end_date DATE DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  report_data JSONB;
BEGIN
  -- Set default date range if not provided
  IF start_date IS NULL THEN
    start_date := CURRENT_DATE - INTERVAL '30 days';
  END IF;
  
  IF end_date IS NULL THEN
    end_date := CURRENT_DATE;
  END IF;
  
  -- Generate the report
  WITH performance_data AS (
    SELECT
      lawyer_id,
      lawyer_name,
      lawyer_specialization,
      COUNT(*) AS total_consultations,
      COUNT(CASE WHEN status = 'completed' THEN 1 END) AS completed_consultations,
      COUNT(CASE WHEN status = 'cancelled' THEN 1 END) AS cancelled_consultations,
      COUNT(CASE WHEN status = 'scheduled' OR status = 'confirmed' THEN 1 END) AS upcoming_consultations,
      COUNT(CASE WHEN consultation_type = 'document' THEN 1 END) AS document_consultations,
      COUNT(CASE WHEN consultation_type = 'video' THEN 1 END) AS video_consultations,
      COUNT(CASE WHEN is_recurring THEN 1 END) AS recurring_consultations,
      SUM(duration_minutes) AS total_minutes,
      AVG(duration_minutes) AS avg_duration_minutes,
      CASE 
        WHEN COUNT(*) > 0 THEN 
          ROUND((COUNT(CASE WHEN status = 'completed' THEN 1 END)::NUMERIC / COUNT(*)) * 100, 2)
        ELSE 0
      END AS completion_rate,
      CASE 
        WHEN COUNT(*) > 0 THEN 
          ROUND((COUNT(CASE WHEN status = 'cancelled' THEN 1 END)::NUMERIC / COUNT(*)) * 100, 2)
        ELSE 0
      END AS cancellation_rate
    FROM
      public.consultation_metrics
    WHERE
      lawyer_id = lawyer_uuid
      AND DATE(consultation_date) BETWEEN start_date AND end_date
    GROUP BY
      lawyer_id, lawyer_name, lawyer_specialization
  ),
  monthly_data AS (
    SELECT
      year,
      month,
      COUNT(*) AS total_consultations,
      COUNT(CASE WHEN status = 'completed' THEN 1 END) AS completed_consultations,
      COUNT(CASE WHEN status = 'cancelled' THEN 1 END) AS cancelled_consultations,
      SUM(duration_minutes) AS total_minutes
    FROM
      public.consultation_metrics
    WHERE
      lawyer_id = lawyer_uuid
      AND DATE(consultation_date) BETWEEN start_date AND end_date
    GROUP BY
      year, month
    ORDER BY
      year, month
  ),
  time_slot_data AS (
    SELECT
      day_of_week,
      hour,
      COUNT(*) AS consultation_count
    FROM
      public.consultation_metrics
    WHERE
      lawyer_id = lawyer_uuid
      AND DATE(consultation_date) BETWEEN start_date AND end_date
    GROUP BY
      day_of_week, hour
    ORDER BY
      consultation_count DESC
    LIMIT 5
  ),
  client_data AS (
    SELECT
      client_id,
      client_name,
      COUNT(*) AS consultation_count
    FROM
      public.consultation_metrics
    WHERE
      lawyer_id = lawyer_uuid
      AND DATE(consultation_date) BETWEEN start_date AND end_date
    GROUP BY
      client_id, client_name
    ORDER BY
      consultation_count DESC
    LIMIT 10
  )
  SELECT 
    jsonb_build_object(
      'performance_summary', (SELECT row_to_json(performance_data) FROM performance_data),
      'monthly_trend', (SELECT json_agg(row_to_json(monthly_data)) FROM monthly_data),
      'popular_time_slots', (SELECT json_agg(row_to_json(time_slot_data)) FROM time_slot_data),
      'top_clients', (SELECT json_agg(row_to_json(client_data)) FROM client_data),
      'report_period', jsonb_build_object(
        'start_date', start_date,
        'end_date', end_date
      )
    ) INTO report_data;
  
  RETURN report_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create storage buckets if they don't exist
-- This ensures that all required storage buckets are created with proper permissions

-- Enable the storage extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";

-- Create avatars bucket if it doesn't exist
DO $$
BEGIN
    -- Check if the bucket already exists
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE name = 'avatars'
    ) THEN
        -- Create the avatars bucket
        INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types)
        VALUES ('avatars', 'avatars', true, false, 5242880, -- 5MB limit
        ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']::text[]);
        
        -- Set up security policies for the avatars bucket
        -- Allow public read access
        INSERT INTO storage.policies (name, definition, bucket_id)
        VALUES (
            'Public Read Access for avatars',
            'bucket_id = ''avatars''::text',
            'avatars'
        );
        
        -- Allow authenticated users to upload
        INSERT INTO storage.policies (name, definition, bucket_id, operation)
        VALUES (
            'Authenticated Upload Access for avatars',
            'bucket_id = ''avatars''::text AND auth.role() = ''authenticated''::text',
            'avatars',
            'INSERT'
        );
        
        -- Allow users to update their own avatars
        INSERT INTO storage.policies (name, definition, bucket_id, operation)
        VALUES (
            'Owner Update Access for avatars',
            'bucket_id = ''avatars''::text AND auth.role() = ''authenticated''::text',
            'avatars',
            'UPDATE'
        );
        
        -- Allow users to delete their own avatars
        INSERT INTO storage.policies (name, definition, bucket_id, operation)
        VALUES (
            'Owner Delete Access for avatars',
            'bucket_id = ''avatars''::text AND auth.role() = ''authenticated''::text',
            'avatars',
            'DELETE'
        );
    END IF;
    
    -- Create consultation-attachments bucket if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE name = 'consultation-attachments'
    ) THEN
        -- Create the consultation-attachments bucket
        INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types)
        VALUES ('consultation-attachments', 'consultation-attachments', true, false, 10485760, -- 10MB limit
        ARRAY['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']::text[]);
        
        -- Set up security policies for the consultation-attachments bucket
        -- Allow public read access
        INSERT INTO storage.policies (name, definition, bucket_id)
        VALUES (
            'Public Read Access for consultation-attachments',
            'bucket_id = ''consultation-attachments''::text',
            'consultation-attachments'
        );
        
        -- Allow authenticated users to upload
        INSERT INTO storage.policies (name, definition, bucket_id, operation)
        VALUES (
            'Authenticated Upload Access for consultation-attachments',
            'bucket_id = ''consultation-attachments''::text AND auth.role() = ''authenticated''::text',
            'consultation-attachments',
            'INSERT'
        );
        
        -- Allow users to update their own attachments
        INSERT INTO storage.policies (name, definition, bucket_id, operation)
        VALUES (
            'Owner Update Access for consultation-attachments',
            'bucket_id = ''consultation-attachments''::text AND auth.role() = ''authenticated''::text',
            'consultation-attachments',
            'UPDATE'
        );
        
        -- Allow users to delete their own attachments
        INSERT INTO storage.policies (name, definition, bucket_id, operation)
        VALUES (
            'Owner Delete Access for consultation-attachments',
            'bucket_id = ''consultation-attachments''::text AND auth.role() = ''authenticated''::text',
            'consultation-attachments',
            'DELETE'
        );
    END IF;
END $$;

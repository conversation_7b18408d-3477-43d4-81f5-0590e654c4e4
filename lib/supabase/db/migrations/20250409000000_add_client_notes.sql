-- Create client_notes table to store lawyer notes about clients
CREATE TABLE IF NOT EXISTS public.client_notes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  lawyer_id UUID NOT NULL REFERENCES public.lawyers(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_pinned BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_client_notes_client_id ON public.client_notes(client_id);
CREATE INDEX IF NOT EXISTS idx_client_notes_lawyer_id ON public.client_notes(lawyer_id);

-- Set up RLS policies
ALTER TABLE public.client_notes ENABLE ROW LEVEL SECURITY;

-- Lawyers can read their own notes about clients
CREATE POLICY "Lawyers can read their own notes" ON public.client_notes
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.lawyers l
      WHERE l.id = lawyer_id AND l.user_id = auth.uid()
    )
  );

-- Lawyers can insert notes for their clients
CREATE POLICY "Lawyers can insert notes for their clients" ON public.client_notes
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.lawyers l
      WHERE l.id = lawyer_id AND l.user_id = auth.uid()
    )
  );

-- Lawyers can update their own notes
CREATE POLICY "Lawyers can update their own notes" ON public.client_notes
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.lawyers l
      WHERE l.id = lawyer_id AND l.user_id = auth.uid()
    )
  );

-- Lawyers can delete their own notes
CREATE POLICY "Lawyers can delete their own notes" ON public.client_notes
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.lawyers l
      WHERE l.id = lawyer_id AND l.user_id = auth.uid()
    )
  );

-- Create a function to get client notes
CREATE OR REPLACE FUNCTION get_client_notes(client_uuid UUID, lawyer_uuid UUID)
RETURNS TABLE (
  id UUID,
  client_id UUID,
  lawyer_id UUID,
  content TEXT,
  is_pinned BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cn.id,
    cn.client_id,
    cn.lawyer_id,
    cn.content,
    cn.is_pinned,
    cn.created_at,
    cn.updated_at
  FROM 
    public.client_notes cn
  WHERE 
    cn.client_id = client_uuid AND
    cn.lawyer_id = lawyer_uuid
  ORDER BY
    cn.is_pinned DESC,
    cn.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to add a client note
CREATE OR REPLACE FUNCTION add_client_note(
  client_uuid UUID,
  lawyer_uuid UUID,
  note_content TEXT,
  is_note_pinned BOOLEAN DEFAULT false
)
RETURNS UUID AS $$
DECLARE
  note_id UUID;
BEGIN
  INSERT INTO public.client_notes (
    client_id,
    lawyer_id,
    content,
    is_pinned
  ) VALUES (
    client_uuid,
    lawyer_uuid,
    note_content,
    is_note_pinned
  )
  RETURNING id INTO note_id;
  
  RETURN note_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

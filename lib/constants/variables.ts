// Types
import { SignatureColor, SignatureFont } from '@/types';

// rich editors
// https://shadcn-minimal-tiptap.vercel.app/
// https://novel.sh/
// https://platejs.org/
// https://shadcn-editor.vercel.app/

/**
 * Environment
 */
export const ENV = process.env.NODE_ENV;

/**
 * Websites
 */
export const BASE_URL = 'https://forms.notamess.com';
export const AUTHOR_WEBSITE = 'https://iamjulius.com';
export const AUTHOR_GITHUB = 'https://github.com/1realjulius';
export const APP_NAME = 'Notamess Invoice';
export const APP_SLOGAN =
  'The platform offers a range of services aimed at simplifying the creation, modification, and sharing of legal documents.';
export const APP_SLOGAN_ARRAY = [
  'Effortlessly Customize and Share Legal Documents,',
  'Take control of your contracts and agreements,',
  'Easily find, edit, and manage all your essential documents in one place,',
  'No legal expertise required.',
];

export const APP_LOGO = 'https://forms.notamess.com/logo.png';
export const APP_WORDMARK = 'https://forms.notamess.com/wordmark.png';
export const APP_THUMBNAIL = 'https://forms.notamess.com/thumbnail.jpg';

/**
 * API endpoints
 */
export const GENERATE_PDF_API = '/api/invoice/generate';
export const SEND_PDF_API = '/api/invoice/send';
export const EXPORT_INVOICE_API = '/api/invoice/export';

/**
 * External API endpoints
 */
export const CURRENCIES_API =
  'https://openexchangerates.org/api/currencies.json';

/**
 * Chromium for Puppeteer
 */
export const CHROMIUM_EXECUTABLE_PATH =
  'https://github.com/Sparticuz/chromium/releases/download/v122.0.0/chromium-v122.0.0-pack.tar';

/**
 * Tailwind
 */
export const TAILWIND_CDN =
  'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';

/**
 * Google
 */
export const GOOGLE_SC_VERIFICATION = process.env.GOOGLE_SC_VERIFICATION;

/**
 * Nodemailer
 */
export const NODEMAILER_EMAIL = process.env.NODEMAILER_EMAIL;
export const NODEMAILER_PW = process.env.NODEMAILER_PW;

/**
 * I18N
 */
export const LOCALES = [
  { code: 'en', name: 'English' },
  { code: 'de', name: 'Deutsch' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
];
export const DEFAULT_LOCALE = LOCALES[0].code;

/**
 * Signature variables
 */
export const SIGNATURE_COLORS: SignatureColor[] = [
  { name: 'black', label: 'Black', color: 'rgb(0, 0, 0)' },
  { name: 'dark blue', label: 'Dark Blue', color: 'rgb(0, 0, 128)' },
  {
    name: 'crimson',
    label: 'Crimson',
    color: '#DC143C',
  },
];

export const SIGNATURE_FONTS: SignatureFont[] = [
  {
    name: 'Dancing Script',
    variable: 'var(--font-dancing-script)',
  },
  { name: 'Parisienne', variable: 'var(--font-parisienne)' },
  {
    name: 'Great Vibes',
    variable: 'var(--font-great-vibes)',
  },
  {
    name: 'Alex Brush',
    variable: 'var(--font-alex-brush)',
  },
];

/**
 * Form date options
 */
export const DATE_OPTIONS: Intl.DateTimeFormatOptions = {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
};

export const SHORT_DATE_OPTIONS: Intl.DateTimeFormatOptions = {
  year: 'numeric',
  month: 'short',
  day: 'numeric',
};

/**
 * Form defaults
 */
export const FORM_DEFAULT_VALUES = {
  sender: {
    name: '',
    address: '',
    zipCode: '',
    city: '',
    country: '',
    email: '',
    phone: '',
    customInputs: [],
  },
  receiver: {
    name: '',
    address: '',
    zipCode: '',
    city: '',
    country: '',
    email: '',
    phone: '',
    customInputs: [],
  },
  details: {
    invoiceLogo: '',
    invoiceNumber: '',
    invoiceDate: '',
    dueDate: '',
    items: [
      {
        name: '',
        description: '',
        quantity: 0,
        unitPrice: 0,
        total: 0,
      },
    ],
    currency: 'USD',
    language: 'English',
    taxDetails: {
      amount: 0,
      amountType: 'amount',
      taxID: '',
    },
    discountDetails: {
      amount: 0,
      amountType: 'amount',
    },
    shippingDetails: {
      cost: 0,
      costType: 'amount',
    },
    paymentInformation: {
      bankName: '',
      accountName: '',
      accountNumber: '',
    },
    additionalNotes: '',
    paymentTerms: '',
    totalAmountInWords: '',
    pdfTemplate: 1,
  },
};
/**
 * Form defaults
 */
export const FL = {
  sender: {
    name: 'Sender Name',
    address: 'Sender Address',
    city: 'Sender City',
    country: 'Sender Country',
    email: 'Sender Email',
    phone: 'Sender Phone Number',
    customInputs: [],
  },
  receiver: {
    name: 'Receiver Name',
    address: 'Receiver Address',
    city: 'Receiver City',
    country: 'Receiver Country',
    email: 'Receiver Email',
    phone: 'Receiver Phone Number',
    customInputs: [],
  },
  details: {
    invoiceLogo: 'Invoice Logo',
    invoiceNumber: 'Invoice Number',
    invoiceDate: 'Invoice Date',
    dueDate: 'Due Date',
    items: [
      {
        name: 'Item Name',
        description: 'Item Description',
        quantity: 'Quantity',
        unitPrice: 'Unit Price',
        total: 'Total',
      },
    ],
    currency: 'Currency',
    taxDetails: {
      amount: 'Tax Amount',
      amountType: 'Tax Amount Type',
      taxID: 'Tax Id',
    },
    discountDetails: {
      amount: 'Discount Amount',
      amountType: 'Discount Amount Type',
    },
    shippingDetails: {
      cost: 'Shipping Cost',
      costType: 'Shipping Cost Type',
    },
    paymentInformation: {
      paymentType: 'Payment Type',
      paymentTypeName: 'Payment Type Name',
      accountName: 'Payment Account Name',
      accountNumber: 'Payment Account Number',
    },
    additionalNotes: 'Additional Notes',
    paymentTerms: 'Additional Terms',
    totalAmountInWords: 'Total Amount In Words',
    pdfTemplate: 'PDF Template',
  },
};

/**
 * ? DEV Only
 * Form auto fill values for testing
 */
export const FORM_FILL_VALUES = {
  sender: {
    name: 'John Doe',
    address: '123 Main St',
    zipCode: '12345',
    city: 'Anytown',
    country: 'USA',
    email: '<EMAIL>',
    phone: '************',
  },
  receiver: {
    name: 'Jane Smith',
    address: '456 Elm St',
    zipCode: '54321',
    city: 'Other Town',
    country: 'Canada',
    email: '<EMAIL>',
    phone: '************',
  },
  details: {
    invoiceLogo: '',
    invoiceNumber: 'INV0001',
    invoiceDate: new Date(),
    dueDate: new Date(),
    items: [
      {
        name: 'Product 1',
        description: 'Description of Product 1',
        quantity: 4,
        unitPrice: 50,
        total: 200,
      },
      {
        name: 'Product 2',
        description: 'Description of Product 2',
        quantity: 5,
        unitPrice: 50,
        total: 250,
      },
      {
        name: 'Product 3',
        description: 'Description of Product 3',
        quantity: 5,
        unitPrice: 80,
        total: 400,
      },
    ],
    currency: 'USD',
    language: 'English',
    taxDetails: {
      amount: 15,
      amountType: 'percentage',
      taxID: '*********',
    },
    discountDetails: {
      amount: 5,
      amountType: 'percentage',
    },
    shippingDetails: {
      cost: 5,
      costType: 'percentage',
    },
    paymentInformation: {
      bankName: 'Bank Inc.',
      accountName: 'John Doe',
      accountNumber: '************',
    },
    additionalNotes: 'Thank you for your business',
    paymentTerms: 'Net 30',
    signature: {
      data: '',
    },
    subTotal: '850',
    totalAmount: '850',
    totalAmountInWords: 'Eight Hundred Fifty',
    pdfTemplate: 1,
  },
};

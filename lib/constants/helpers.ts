// Next
import { NextResponse } from 'next/server';

/**
 * Formats a number with commas and decimal places
 *
 * @param {number} number - Number to format
 * @returns {string} A styled number to be displayed on the invoice
 */
const formatNumberWithCommas = (number: number): string => {
  return number.toLocaleString('en-US', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

/**
 * This method flattens a nested object. It is used for xlsx export
 *
 * @param {Record<string, T>} obj - A nested object to flatten
 * @param {string} parentKey - The parent key
 * @returns {Record<string, T>} A flattened object
 */
const flattenObject = <T>(
  obj: Record<string, T>,
  parentKey: string = ''
): Record<string, T> => {
  const result: Record<string, T> = {};

  for (const key in obj) {
    if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
      const flattened = flattenObject(
        obj[key] as Record<string, T>,
        parentKey + key + '_'
      );
      for (const subKey in flattened) {
        result[parentKey + subKey] = flattened[subKey];
      }
    } else {
      result[parentKey + key] = obj[key];
    }
  }

  return result;
};

/**
 * A method to validate an email address
 *
 * @param {string} email - Email to validate
 * @returns {boolean} A boolean indicating if the email is valid
 */
const isValidEmail = (email: string): boolean => {
  // Regular expression for a simple email pattern
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  return emailRegex.test(email);
};

/**
 * A method to check if a string is a data URL
 *
 * @param {string} str - String to check
 * @returns {boolean} Boolean indicating if the string is a data URL
 */
const isDataUrl = (str: string): boolean => str.startsWith('data:');

/**
 * Dynamically imports and retrieves an invoice template React component based on the provided template ID.
 *
 * @param {number} templateId - The ID of the invoice template.
 * @returns {Promise<React.ComponentType<any> | null>} A promise that resolves to the invoice template component or null if not found.
 * @throws {Error} Throws an error if there is an issue with the dynamic import or if a default template is not available.
 */
const getInvoiceTemplate = async (templateId: number) => {
  // Dynamic template component name
  const componentName = `InvoiceTemplate${templateId}`;

  try {
    const module = await import(
      `@/components/templates/invoice-pdf/${componentName}`
    );
    return module.default;
  } catch (err) {
    console.error(`Error importing template ${componentName}: ${err}`);

    // Provide a default template
    return null;
  }
};

/**
 * Convert a file to a buffer. Used for sending invoice as email attachment.
 * @param {File} file - The file to convert to a buffer.
 * @returns {Promise<Buffer>} A promise that resolves to a buffer.
 */
const fileToBuffer = async (file: File): Promise<Buffer> => {
  // Convert Blob to ArrayBuffer
  const arrayBuffer = await new NextResponse(file).arrayBuffer();

  // Convert ArrayBuffer to Buffer
  const pdfBuffer = Buffer.from(arrayBuffer);

  return pdfBuffer;
};

export {
  fileToBuffer,
  flattenObject,
  formatNumberWithCommas,
  getInvoiceTemplate,
  isDataUrl,
  isValidEmail,
};

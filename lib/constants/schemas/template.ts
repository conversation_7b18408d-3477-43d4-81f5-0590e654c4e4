import { z } from 'zod';

// Template metadata schema
const TemplateMetadataSchema = z.object({
  id: z.string().uuid(),
  version: z.string(),
  name: z.string().min(1),
  description: z.string(),
  category: z.enum(['contract', 'agreement', 'declaration', 'notice', 'form']),
  jurisdiction: z.string(),
  language: z.string(),
  tags: z.array(z.string()),
  lastUpdated: z.date(),
  isPublished: z.boolean(),
  baseTemplate: z.string().optional(), // ID of the template to inherit from
});

const ValidationRuleSchema = z.object({
  type: z.enum(['min', 'max', 'regex', 'custom']),
  value: z.union([z.number(), z.string()]),
  message: z.string(),
});

const VariableSchema = z.object({
  name: z.string(),
  type: z.enum(['text', 'number', 'date', 'boolean']),
  description: z.string(),
  isRequired: z.boolean(),
  validationRules: z.array(ValidationRuleSchema).optional(),
});

const ConditionalSchema = z.object({
  if: z.string(), // Variable name to check
  equals: z.union([z.string(), z.number(), z.boolean()]), // Value to compare against
  then: z.boolean(), // Whether to include the section if condition is met
});

const SectionSchema = z.object({
  id: z.string(),
  title: z.string(),
  content: z.string(),
  isRequired: z.boolean(),
  order: z.number(),
  variables: z.array(VariableSchema),
  condition: ConditionalSchema.optional(),
  inheritsFrom: z.string().optional(), // ID of the section to inherit from
});

// Template version schema
const TemplateVersionSchema = z.object({
  id: z.string().uuid(),
  version: z.string(),
  changes: z.array(
    z.object({
      type: z.enum(['added', 'modified', 'removed']),
      description: z.string(),
      date: z.date(),
    })
  ),
  createdAt: z.date(),
  createdBy: z.string().uuid(),
});

export const fieldValidationSchema = z.object({
  min: z.number().optional(),
  max: z.number().optional(),
  pattern: z.string().optional(),
  required: z.boolean().optional(),
});

export const fieldSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'textarea', 'number', 'date', 'boolean']),
  label: z.string(),
  description: z.string().optional(),
  required: z.boolean().default(false),
  validation: fieldValidationSchema.optional(),
});

export const sectionSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  fields: z.array(fieldSchema),
  order: z.number().optional(),
  inheritsFrom: z.string().optional(),
  condition: z
    .object({
      field: z.string(),
      operator: z.enum(['equals', 'notEquals', 'contains', 'notContains']),
      value: z.unknown(),
    })
    .optional(),
});

// Complete template schema
export const LegalTemplateSchema = z.object({
  metadata: TemplateMetadataSchema,
  sections: z.array(SectionSchema),
  legalRequirements: z.array(z.string()),
  formattingGuidelines: z.array(z.string()),
  validationRules: z.array(
    z.object({
      field: z.string(),
      rule: z.string(),
      message: z.string(),
    })
  ),
  versions: z.array(TemplateVersionSchema),
  aiSuggestions: z
    .array(
      z.object({
        section: z.string().uuid(),
        suggestion: z.string(),
        confidence: z.number().min(0).max(1),
      })
    )
    .optional(),
});

export type LegalTemplate = z.infer<typeof LegalTemplateSchema>;
export type TemplateSection = z.infer<typeof SectionSchema>;
export type TemplateMetadata = z.infer<typeof TemplateMetadataSchema>;
export type TemplateVersion = z.infer<typeof TemplateVersionSchema>;
export type ValidationRule = z.infer<typeof ValidationRuleSchema>;
export type Variable = z.infer<typeof VariableSchema>;
export type Section = z.infer<typeof SectionSchema>;
export type Version = z.infer<typeof TemplateVersionSchema>;
export type Field = z.infer<typeof fieldSchema>;
export type FieldValidation = z.infer<typeof fieldValidationSchema>;

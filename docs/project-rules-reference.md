# NotAMess Forms Project Rules Reference

This document serves as a comprehensive reference for the rules and guidelines to be followed in the NotAMess Forms project, with a particular focus on database and Supabase usage.

## Table of Contents

1. [General Project Patterns](#general-project-patterns)
2. [Database Guidelines](#database-guidelines)
   - [Migration Rules](#migration-rules)
   - [Row Level Security (RLS) Policies](#row-level-security-rls-policies)
   - [Database Functions](#database-functions)
   - [SQL Style Guide](#sql-style-guide)
3. [Supabase MCP Integration](#supabase-mcp-integration)
4. [Document Management System](#document-management-system)
5. [Next.js Guidelines](#nextjs-guidelines)

## General Project Patterns

### Package Management

- Use bun for all package installations
- Run `bun add` for adding new dependencies
- Run `bun add -D` for dev dependencies
- Use `bun install` for installing all dependencies

### File Organization

- Use feature-based organization within app directory
- Group related components in feature-specific folders
- Keep shared components in components/ui
- Place hooks in lib/hooks
- Store constants in lib/constants
- Maintain types in types directory

### Naming Conventions

- React components: PascalCase
- Files containing React components: PascalCase
- Hooks: camelCase prefixed with 'use'
- Utilities: camelCase
- Constants: UPPER_SNAKE_CASE
- Types/Interfaces: PascalCase
- CSS classes: kebab-case

### State Management

- Use Zustand for global state
- React Context for component trees
- Local state for component-specific data
- Prefer controlled components
- Implement proper state initialization

## Database Guidelines

### Migration Rules

#### File Naming Convention

- Format: `YYYYMMDDHHmmss_short_description.sql`
- Example: `20240906123045_create_profiles.sql`

#### SQL Guidelines

- Include header comments with metadata about the migration
- Include thorough comments explaining each migration step
- Write all SQL in lowercase
- Add detailed comments for any destructive SQL commands
- Always enable Row Level Security (RLS) when creating new tables
- Create granular RLS policies (separate policies for select, insert, update, delete)
- Ensure policies cover all relevant access scenarios
- Include comments explaining the rationale for each security policy

### Row Level Security (RLS) Policies

#### Policy Creation Rules

- Use only CREATE POLICY or ALTER POLICY queries
- Always use double apostrophe in SQL strings (e.g., `'Night''s watch'`)
- Always use `auth.uid()` instead of `current_user`
- SELECT policies should always have USING but not WITH CHECK
- INSERT policies should always have WITH CHECK but not USING
- UPDATE policies should always have WITH CHECK and most often have USING
- DELETE policies should always have USING but not WITH CHECK
- Don't use `FOR ALL` - create separate policies for select, insert, update, and delete
- Policy names should be descriptive and enclosed in double quotes

#### Performance Recommendations

- Add indexes on columns used within policies
- Avoid joins in policies when possible
- Rewrite policies to use sets instead of joins for better performance

### Database Functions

#### General Guidelines

- Default to `SECURITY INVOKER` for functions
- Set the `search_path` configuration parameter to an empty string
- Use fully qualified names for all database objects
- Minimize side effects in functions
- Use explicit typing for parameters and return values
- Default to IMMUTABLE or STABLE functions when possible

#### Function Templates

```sql
-- Simple function with SECURITY INVOKER
create or replace function my_schema.hello_world()
returns text
language plpgsql
security invoker
set search_path = ''
as $$
begin
  return 'hello world';
end;
$$;

-- Function with parameters and fully qualified object names
create or replace function public.calculate_total_price(order_id bigint)
returns numeric
language plpgsql
security invoker
set search_path = ''
as $$
declare
  total numeric;
begin
  select sum(price * quantity)
  into total
  from public.order_items
  where order_id = calculate_total_price.order_id;

  return total;
end;
$$;
```

### SQL Style Guide

#### General

- Use lowercase for SQL reserved words
- Use consistent, descriptive identifiers
- Use white space and indentation for readability
- Store dates in ISO 8601 format
- Include comments for complex logic

#### Naming Conventions

- Use snake_case for tables and columns
- Prefer plurals for table names
- Prefer singular names for columns
- Avoid SQL reserved words
- Ensure names are unique and under 63 characters

#### Tables

- Always add an `id` column of type `identity generated always` unless otherwise specified
- Create tables in the `public` schema unless otherwise specified
- Always add the schema to SQL queries
- Add a comment to describe what the table does

#### Columns

- Use singular names and avoid generic names
- For foreign keys, use the singular of the table name with the `_id` suffix
- Always use lowercase except for acronyms

#### Queries

- Format queries for readability based on complexity
- Add spaces for readability
- Prefer full table names when referencing tables
- Use meaningful aliases with the `as` keyword
- Use CTEs for complex queries

## Supabase MCP Integration

The project uses Supabase MCP (Model-Controlled Programming) to interact with the database. The configuration is in `.cursor/mcp.json`.

### Key Components

1. **MCP Utilities (`lib/utils/mcp.ts`)**

   - `createMCPClient`: Creates a typed client for chaining Supabase operations
   - `mcpCall`: A simpler function for making one-off MCP calls
   - Error handling and fallback mechanisms

2. **Supabase MCP Service (`lib/services/supabase-mcp.ts`)**

   - `getProfile`: Get the current user's profile
   - `updateProfile`: Update the current user's profile
   - `getProfileByUsername`: Get a user's profile by username
   - `getRoles`: Get all roles with their permissions

3. **React Hook (`lib/hooks/useSupabaseMcp.ts`)**
   - Provides loading and error states
   - Auto-loads user profile
   - Wraps service methods for component use

### Best Practices

- Always provide fallbacks with mock data
- Check MCP status before operations
- Handle errors gracefully
- Keep mock data up to date with schema changes

## Document Management System

The document management system is a core feature of the application that enables users to create, edit, view, and organize various types of documents.

### Database Schema

- **documents**: Main table storing document metadata and content
- **document_tags**: Lookup table for document tags
- **document_to_tags**: Junction table for many-to-many relationships
- **document_summaries**: View that provides summarized document information

### Architecture

```
User Interface → React Hooks → Document Service → Supabase Database
                                               → Supabase MCP
                                               → Mock Data Fallback
```

## Next.js Guidelines

### Routing and Parameters

- In Next.js route handlers and page components, the `params` prop is a Promise that must be awaited
- Always use the React `use` hook or async/await to handle params in page components

```tsx
// For client components
import { use } from 'react';

type PageProps = {
  params: Promise<{
    id: string;
    username: string;
  }>;
};

export default function Page({ params }: PageProps) {
  const resolvedParams = use(params);
  // Now use resolvedParams.id, resolvedParams.username, etc.
}
```

# Project Overview

- NotAMess Forms is a legal software platform using AI to simplify document creation and management, focusing on the documents phase.
- The platform uses React/Next.js frontend, Node.js/Python backend, and Supabase for the database.
- Key features include document creation, management, sharing, template selection, customization, and optional lawyer consultation.
- The platform supports two user roles: standard users (document creation) and lawyers (client management).
- The project is removing contracts, blockchain functionality, and admin roles. Document creators have deletion power.

# Project Configuration

- Use Sonner for toast notifications instead of @radix-ui/react-toast.
- Use @supabase/ssr for Supabase authentication instead of @supabase/auth-helpers-nextjs.
- The ui-avatars.com domain needs to be added to the images.domains array in next.config.js.
- Use bun for package management and Tailwind CSS with direct imports.
- Restructure Supabase server and client implementations based on the auth folder functions.
- Use Supabase MCP from the settings to update the database for database operations.
- Avoid initializing new Supabase clients.

# UI Preferences

- Use sheets instead of dialogs for creating, sharing, and onboarding.
- Use Sonner toast notifications for document actions and promise states.
- Use skeleton loaders instead of spinning icons for loading states.
- Use Lucide icons instead of Nucleo icons.
- Implement the right sidebar (collaborations, document details) as a sheet using Vaul with a toggle button.
- In the lawyer dashboard, replace tabs with actual pages for Client Consultations, Document Reviews, and Lawyer Profile sections.

# Development Guidelines

- Follow strict error checking protocols, organize files by feature, and use functional components with TypeScript.
- Use Zustand for global state and Zod for form validation.
- Follow accessibility guidelines, implement proper error handling, and write tests following the arrange-act-assert pattern.
- Optimize for performance and follow security best practices.
- Always define interfaces for untyped data structures and use explicit type annotations.
- In Next.js route handlers and page components, the params prop is a Promise that must be awaited using React.use() or async/await. URL structure should include username parameter in document routes with format: /${username}/documents/${documentId}.
- Document operations (create, edit, delete) should be connected to the Supabase database with real-time updates using Supabase's features.
- Use Supabase client functions for database implementation throughout the project.
- Prioritize completing all features over spending too much time on individual fixes.
- The 'use client' directive must be placed at the very top of the file before any other expressions in Next.js components.
- Keep a dev server running persistently for testing and debugging purposes.
- Don't use the userStore for user data; instead create a global function to update the userStore with real-time data.
- The userStore should be implemented as a global provider that maintains real-time synchronization with the database to ensure user profile data is always up-to-date.
- Keep role-based functionality simple - if role is lawyer they get lawyer features, if user they get user features, and fetch real-time data directly from the database without complicated processes.
- When a user is a lawyer, their dashboard and sidebar should be different from standard users, and data should be fetched directly from the database without creating new files.
- Focus on making all settings pages functional with real database integration, and remove settings pages that aren't necessary for the project.

# Database

- Global templates are stored in a separate database table called 'templates'.
- Implement database tables for lawyer account setup, notifications, and collaborations.
- Types for the lawyers table and other database tables need to be added to the Supabase types.
- Focus database content on key user accounts (<EMAIL>, <EMAIL>, <EMAIL>).

# PDF Export Requirements

- PDF exports should always include 'created by NotAMess' and the date of export at the bottom of the document.

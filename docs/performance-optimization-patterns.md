# Performance Optimization Patterns

This document outlines the performance optimization patterns used throughout the application to improve loading speed and overall performance.

## Table of Contents

1. [Component Optimization](#component-optimization)
2. [Data Fetching Optimization](#data-fetching-optimization)
3. [Rendering Optimization](#rendering-optimization)
4. [Caching Strategies](#caching-strategies)
5. [Implementation Examples](#implementation-examples)

## Component Optimization

### Lazy Loading Components

Use lazy loading to defer loading components until they are needed:

```tsx
import { lazy, Suspense } from 'react';

// Lazy load heavy components
const HeavyComponent = lazy(() => 
  import('@/components/HeavyComponent').then(module => ({
    default: module.HeavyComponent
  }))
);

// Use with Suspense
<Suspense fallback={<LoadingState />}>
  <HeavyComponent {...props} />
</Suspense>
```

### Memoizing Components

Prevent unnecessary re-renders by memoizing components:

```tsx
import React from 'react';

// Create a memoized component
const MemoizedComponent = React.memo(({ prop1, prop2 }) => {
  // Component implementation
});
```

### Optimized Loading States

Create efficient loading states that don't cause layout shifts:

```tsx
const LoadingState = React.memo(() => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Array.from({ length: 3 }).map((_, i) => (
      <Skeleton key={i} />
    ))}
  </div>
));
```

## Data Fetching Optimization

### Debounced Search

Implement debounced search to reduce API calls:

```tsx
import { useMemo } from 'react';
import { debounce } from '@/lib/utils/performance-optimizations';

// Create a debounced search handler
const debouncedSearch = useMemo(
  () => debounce((value: string) => setSearchQuery(value), 300),
  []
);

// Use in input handler
const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  debouncedSearch(e.target.value);
};
```

### Cached API Calls

Implement caching for API calls to reduce server load:

```tsx
// Check cache first
if (typeof window !== 'undefined') {
  try {
    const cachedData = localStorage.getItem('cache_key');
    if (cachedData) {
      const { data, timestamp } = JSON.parse(cachedData);
      // Check if cache is still valid (15 minutes)
      if (Date.now() - timestamp < 15 * 60 * 1000) {
        console.log('Using cached data');
        return data;
      }
    }
  } catch (error) {
    console.error('Error reading from cache:', error);
  }
}

// Fetch fresh data
const data = await fetchData();

// Save to cache
if (typeof window !== 'undefined') {
  try {
    localStorage.setItem(
      'cache_key',
      JSON.stringify({
        data,
        timestamp: Date.now()
      })
    );
  } catch (error) {
    console.error('Error saving to cache:', error);
  }
}
```

### Optimistic UI Updates

Implement optimistic UI updates to improve perceived performance:

```tsx
// Update UI immediately
setItems([...items, newItem]);

// Then perform the actual API call
try {
  await api.createItem(newItem);
} catch (error) {
  // Revert the optimistic update on error
  setItems(items);
  showError(error);
}
```

## Rendering Optimization

### Virtualized Lists

Use virtualization for long lists to improve rendering performance:

```tsx
import { useVirtualizer } from '@tanstack/react-virtual';

// Implement virtualized list
const rowVirtualizer = useVirtualizer({
  count: items.length,
  getScrollElement: () => parentRef.current,
  estimateSize: () => 35,
});

// Render only visible items
{rowVirtualizer.getVirtualItems().map((virtualRow) => (
  <div
    key={virtualRow.index}
    style={{
      height: `${virtualRow.size}px`,
      transform: `translateY(${virtualRow.start}px)`,
    }}
  >
    {items[virtualRow.index].name}
  </div>
))}
```

### Conditional Rendering

Use conditional rendering to avoid rendering unnecessary components:

```tsx
{isFeatureEnabled && <FeatureComponent />}
```

## Caching Strategies

### Local Storage Caching

Implement local storage caching for frequently accessed data:

```tsx
// Cache service implementation
class CacheService {
  private static instance: CacheService;
  private cache: Map<string, CacheItem<any>>;
  private defaultTTL: number = 15 * 60 * 1000; // 15 minutes default TTL

  // Get an item from the cache
  public get<T>(key: string): T | null {
    // Only clean expired items occasionally to improve performance
    if (Math.random() < 0.1) { // 10% chance to clean on each get
      this.cleanExpiredItems();
    }
    
    const item = this.cache.get(key);
    if (!item) return null;
    
    // Check if this specific item is expired
    if (item.expiresAt < Date.now()) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data as T;
  }

  // Set an item in the cache
  public set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    this.cache.set(key, {
      data,
      expiresAt: Date.now() + ttl,
    });
    
    this.saveToStorage();
  }
}
```

### Debounced Storage Writes

Implement debounced storage writes to reduce performance impact:

```tsx
private saveToStorage(): void {
  if (typeof window === 'undefined') return;
  
  // Use debounced save to avoid excessive writes
  if (this.debounceSaveTimeout) {
    clearTimeout(this.debounceSaveTimeout);
  }
  
  this.debounceSaveTimeout = setTimeout(() => {
    try {
      const cacheObj: Record<string, CacheItem<any>> = {};
      this.cache.forEach((value, key) => {
        cacheObj[key] = value;
      });
      localStorage.setItem('cache_key', JSON.stringify(cacheObj));
    } catch (error) {
      console.error('Error saving cache to localStorage:', error);
    }
  }, 500); // Debounce for 500ms
}
```

## Implementation Examples

### Optimized Document Page

The document page has been optimized using the following techniques:

1. Lazy loading for heavy components
2. Memoized components for better rendering performance
3. Optimized loading states with skeletons
4. Cached document data with TTL
5. Debounced search for filtering documents

### Optimized Templates Page

The templates page has been optimized using the following techniques:

1. Extracted TemplateCard to a separate component and memoized it
2. Created a dedicated loading state component
3. Implemented local storage caching for templates and categories
4. Added debounced search for better performance
5. Used useCallback for event handlers to prevent unnecessary re-renders

### Reusable Optimization Utilities

The `lib/utils/performance-optimizations.tsx` file contains reusable optimization utilities:

1. `createLazyComponent` - Creates a lazy-loaded component with a fallback loading state
2. `createMemoizedComponent` - Creates a memoized component to prevent unnecessary re-renders
3. `debounce` - Debounces a function to limit how often it can be called
4. `createCachedFetch` - Creates a cached fetch function that stores results in localStorage

## Best Practices

1. **Measure First**: Always measure performance before and after optimizations
2. **Optimize Critical Paths**: Focus on optimizing the most frequently used pages first
3. **Lazy Load Heavy Components**: Use lazy loading for components that aren't needed immediately
4. **Memoize Pure Components**: Use React.memo for components that render the same output for the same props
5. **Implement Caching**: Cache API responses and expensive calculations
6. **Use Debouncing**: Debounce input handlers and other frequently called functions
7. **Optimize Images**: Use proper image optimization techniques
8. **Code Splitting**: Split your code into smaller chunks that can be loaded on demand
9. **Virtualize Long Lists**: Use virtualization for long lists to improve rendering performance
10. **Avoid Unnecessary Renders**: Use proper dependency arrays in useEffect and useMemo hooks

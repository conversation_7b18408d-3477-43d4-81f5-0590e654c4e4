# NotAMess Forms

NotAMess Forms is a legal software platform using AI to simplify document creation and management.

## Tech Stack

- **Frontend**: React/Next.js with App Router
- **Backend**: Node.js/Python
- **Database**: Supabase (PostgreSQL)
- **API**: Direct Supabase Client
- **Authentication**: Supabase Auth
- **State Management**: Zustand
- **Form Validation**: Zod
- **Email Delivery**: Resend with <PERSON>act Emails
- **Styling**: Tailwind CSS
- **Package Manager**: Bun

## Getting Started

First, install dependencies:

```bash
bun install
```

Then, run the development server:

```bash
bun run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Supabase Implementation

This project uses Supabase for direct database access and authentication. The implementation follows these principles:

- Type-safe database access with TypeScript
- Simplified API development with Supabase client
- Improved error handling and performance
- Optimized data fetching with custom hooks

### Supabase Structure

- `/lib/supabase/`: Supabase client setup
  - `/client.ts`: Client-side Supabase client
  - `/server-client.ts`: Server-side Supabase client
  - `/auth/`: Authentication utilities
- `/lib/hooks/`: Custom hooks for data access
  - `/use-supabase-client.ts`: Custom hooks for using Supabase client

### Using Supabase

To use Supabase in a component:

```tsx
'use client';

import { useDocuments } from '@/lib/hooks/use-supabase-client';
import { useEffect, useState } from 'react';

export function MyComponent() {
  const { getAll, createDocument } = useDocuments();
  const [documents, setDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setIsLoading(true);
        const result = await getAll();
        setDocuments(result.documents);
      } catch (error) {
        console.error('Error fetching documents:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  const handleCreate = async () => {
    try {
      await createDocument.mutate({
        title: 'New Document',
        content: 'Content here',
      });
      // Refetch documents after creation
      const result = await getAll();
      setDocuments(result.documents);
    } catch (error) {
      console.error('Error creating document:', error);
    }
  };

  return (
    <div>
      {isLoading ? (
        <p>Loading...</p>
      ) : (
        <ul>
          {documents.map((doc) => (
            <li key={doc.id}>{doc.title}</li>
          ))}
        </ul>
      )}
      <button onClick={handleCreate}>Create Document</button>
    </div>
  );
}
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

```

```

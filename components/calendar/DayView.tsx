'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UserAvatar } from '@/components/ui/user-avatar';
import { CalendarEvent } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import {
  calculateEventPosition,
  formatDate,
  getDayHours,
  getEventColorClass,
  isEventOnDay,
} from '@/lib/utils/calendar-utils';
import { isToday } from 'date-fns';
import { Calendar, Clock, FileText, MapPin, Video } from 'lucide-react';
import { useState } from 'react';

interface DayViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  onTimeClick: (date: Date) => void;
  isReadOnly?: boolean;
}

export function DayView({
  currentDate,
  events,
  onEventClick,
  onTimeClick,
  isReadOnly = false,
}: DayViewProps) {
  const hours = getDayHours();
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);

  // Handle event click
  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {
    e.stopPropagation();
    if (isReadOnly) {
      setSelectedEvent(event);
      setIsEventDialogOpen(true);
    } else {
      onEventClick(event);
    }
  };

  // Handle time cell click
  const handleTimeClick = (hour: number) => {
    const date = new Date(currentDate);
    date.setHours(hour);
    date.setMinutes(0);
    date.setSeconds(0);
    onTimeClick(date);
  };

  // Get events for the current day
  const dayEvents = events.filter((event) => isEventOnDay(event, currentDate));

  // Separate all-day events
  const allDayEvents = dayEvents.filter((event) => event.allDay);
  const timeEvents = dayEvents.filter((event) => !event.allDay);

  // Render time-based events
  const renderEvents = () => {
    return timeEvents.map((event) => {
      const { top, height } = calculateEventPosition(event, currentDate);

      return (
        <div
          key={event.id}
          onClick={(e) => handleEventClick(event, e)}
          className={cn(
            'absolute left-[80px] right-4 rounded px-2 py-1 text-sm truncate border cursor-pointer',
            getEventColorClass(event)
          )}
          style={{
            top: `${top * 60}px`,
            height: `${height * 60}px`,
          }}
        >
          <div className="font-medium">{event.title}</div>
          <div className="text-xs">
            {formatDate(new Date(event.start), 'h:mm a')} -
            {formatDate(new Date(event.end), 'h:mm a')}
          </div>
        </div>
      );
    });
  };

  return (
    <>
      <div className="overflow-auto max-h-[calc(100vh-200px)]">
        {/* Day header */}
        <div className="bg-background p-4 sticky top-0 z-10 border-b">
          <h2
            className={cn(
              'text-xl font-semibold text-center',
              isToday(currentDate) && 'text-primary'
            )}
          >
            {formatDate(currentDate, 'EEEE, MMMM d, yyyy')}
          </h2>
        </div>

        {/* All-day events */}
        {allDayEvents.length > 0 && (
          <div className="bg-background p-2 border-b">
            <div className="text-xs text-muted-foreground mb-1">All-day</div>
            <div className="space-y-1">
              {allDayEvents.map((event) => (
                <div
                  key={event.id}
                  onClick={(e) => handleEventClick(event, e)}
                  className={cn(
                    'px-2 py-1 rounded text-sm truncate border cursor-pointer',
                    getEventColorClass(event)
                  )}
                >
                  {event.title}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Time grid */}
        <div className="relative">
          {/* Time labels */}
          {hours.map((hour) => (
            <div
              key={hour}
              onClick={() => handleTimeClick(hour)}
              className="h-[60px] border-t flex hover:bg-accent/20 cursor-pointer"
            >
              <div className="w-[80px] text-xs text-muted-foreground pr-2 text-right pt-1">
                {hour === 0
                  ? '12 AM'
                  : hour < 12
                    ? `${hour} AM`
                    : hour === 12
                      ? '12 PM'
                      : `${hour - 12} PM`}
              </div>
              <div className="flex-1"></div>
            </div>
          ))}

          {/* Events */}
          {renderEvents()}
        </div>
      </div>

      {/* Event details dialog */}
      <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{selectedEvent?.title}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedEvent && (
              <>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(
                      new Date(selectedEvent.start),
                      'EEEE, MMMM d, yyyy'
                    )}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(new Date(selectedEvent.start), 'h:mm a')} -
                    {formatDate(new Date(selectedEvent.end), 'h:mm a')}
                  </span>
                </div>

                {selectedEvent.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedEvent.location}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  {selectedEvent.consultationType === 'video' ? (
                    <Video className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span>
                    {selectedEvent.consultationType === 'video'
                      ? 'Video Consultation'
                      : 'Document Review'}
                  </span>
                </div>

                {selectedEvent.status && (
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={
                        selectedEvent.status === 'scheduled'
                          ? 'bg-blue-50 text-blue-700 border-blue-200'
                          : selectedEvent.status === 'confirmed'
                            ? 'bg-green-50 text-green-700 border-green-200'
                            : selectedEvent.status === 'completed'
                              ? 'bg-purple-50 text-purple-700 border-purple-200'
                              : 'bg-red-50 text-red-700 border-red-200'
                      }
                    >
                      {selectedEvent.status.charAt(0).toUpperCase() +
                        selectedEvent.status.slice(1)}
                    </Badge>
                  </div>
                )}

                {(selectedEvent.client || selectedEvent.lawyer) && (
                  <div className="flex items-center gap-2 mt-4">
                    {selectedEvent.client && (
                      <div className="flex items-center gap-2">
                        <UserAvatar
                          userId={selectedEvent.client.id}
                          avatarUrl={selectedEvent.client.avatar_url}
                          fallbackText={selectedEvent.client.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.client.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Client
                          </p>
                        </div>
                      </div>
                    )}

                    {selectedEvent.lawyer && (
                      <div className="flex items-center gap-2 ml-auto">
                        <UserAvatar
                          userId={selectedEvent.lawyer.id}
                          avatarUrl={selectedEvent.lawyer.avatar_url}
                          fallbackText={selectedEvent.lawyer.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.lawyer.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Lawyer
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {selectedEvent.description && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-1">Description</h4>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {selectedEvent.description}
                    </p>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => setIsEventDialogOpen(false)}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

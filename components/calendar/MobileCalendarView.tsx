'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { UserAvatar } from '@/components/ui/user-avatar';
import { CalendarEvent } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { formatDate } from '@/lib/utils/calendar-utils';
import {
  addDays,
  eachDayOfInterval,
  endOfDay,
  endOfWeek,
  format,
  isSameDay,
  isSameMonth,
  isToday,
  startOfDay,
  startOfWeek,
} from 'date-fns';
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  FileText,
  MapPin,
  Plus,
  Video,
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface MobileCalendarViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  onDateClick: (date: Date) => void;
  isReadOnly?: boolean;
}

export function MobileCalendarView({
  currentDate,
  events,
  onEventClick,
  onDateClick,
  isReadOnly = false,
}: MobileCalendarViewProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate);
  const [weekDays, setWeekDays] = useState<Date[]>([]);

  // Update week days when selected date changes
  useEffect(() => {
    const start = startOfWeek(selectedDate, { weekStartsOn: 0 });
    const end = endOfWeek(selectedDate, { weekStartsOn: 0 });
    setWeekDays(eachDayOfInterval({ start, end }));
  }, [selectedDate]);

  // Navigate to previous day
  const handlePreviousDay = () => {
    setSelectedDate(addDays(selectedDate, -1));
  };

  // Navigate to next day
  const handleNextDay = () => {
    setSelectedDate(addDays(selectedDate, 1));
  };

  // Handle date click in week view
  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  // Handle add event
  const handleAddEvent = () => {
    // Create a new date at the current time (rounded to the nearest hour)
    const now = new Date();
    const hours = now.getHours();
    const date = new Date(selectedDate);
    date.setHours(hours, 0, 0, 0);

    onDateClick(date);
  };

  // Get events for the selected date
  const getEventsForDate = (date: Date) => {
    const dayStart = startOfDay(date);
    const dayEnd = endOfDay(date);

    return events
      .filter((event) => {
        const eventStart = new Date(event.start);
        return eventStart >= dayStart && eventStart <= dayEnd;
      })
      .sort(
        (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
      );
  };

  // Get day name
  const getDayName = (date: Date) => {
    return format(date, 'EEE');
  };

  // Get day number
  const getDayNumber = (date: Date) => {
    return format(date, 'd');
  };

  // Check if date is in current month
  const isCurrentMonth = (date: Date) => {
    return isSameMonth(date, currentDate);
  };

  // Get selected date events
  const selectedDateEvents = getEventsForDate(selectedDate);

  return (
    <div className="space-y-4">
      {/* Week view */}
      <div className="flex justify-between items-center">
        <Button variant="ghost" size="icon" onClick={handlePreviousDay}>
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="text-center">
          <h3 className="text-lg font-medium">
            {format(selectedDate, 'MMMM yyyy')}
          </h3>
        </div>

        <Button variant="ghost" size="icon" onClick={handleNextDay}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-7 gap-1">
        {weekDays.map((day) => (
          <Button
            key={day.toISOString()}
            variant="ghost"
            className={cn(
              'flex flex-col h-auto p-2',
              isToday(day) && 'bg-primary/10',
              isSameDay(day, selectedDate) &&
                'bg-primary text-primary-foreground',
              !isCurrentMonth(day) && 'text-muted-foreground opacity-50'
            )}
            onClick={() => handleDateClick(day)}
          >
            <span className="text-xs">{getDayName(day)}</span>
            <span className="text-lg">{getDayNumber(day)}</span>
          </Button>
        ))}
      </div>

      {/* Selected date header */}
      <div className="flex justify-between items-center">
        <h3
          className={cn(
            'text-lg font-medium',
            isToday(selectedDate) && 'text-primary'
          )}
        >
          {formatDate(selectedDate, 'EEEE, MMMM d, yyyy')}
          {isToday(selectedDate) && (
            <Badge className="ml-2 bg-primary text-primary-foreground">
              Today
            </Badge>
          )}
        </h3>

        {!isReadOnly && (
          <Button size="sm" onClick={handleAddEvent}>
            <Plus className="h-4 w-4 mr-2" />
            Add
          </Button>
        )}
      </div>

      {/* Events list */}
      <div className="space-y-2">
        {selectedDateEvents.length === 0 ? (
          <Card>
            <CardContent className="p-4 text-center text-muted-foreground">
              No events scheduled for this day
            </CardContent>
          </Card>
        ) : (
          selectedDateEvents.map((event) => (
            <Card
              key={event.id}
              className={cn(
                'overflow-hidden cursor-pointer hover:shadow-md transition-shadow',
                event.status === 'cancelled' && 'opacity-60'
              )}
              onClick={() => onEventClick(event)}
            >
              <CardContent className="p-4">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <div
                      className={cn(
                        'w-3 h-3 rounded-full',
                        event.status === 'scheduled'
                          ? 'bg-blue-500'
                          : event.status === 'confirmed'
                            ? 'bg-green-500'
                            : event.status === 'completed'
                              ? 'bg-purple-500'
                              : 'bg-red-500'
                      )}
                    ></div>
                    <h4 className="font-medium">{event.title}</h4>
                  </div>

                  <div className="flex flex-wrap gap-x-4 gap-y-1 text-sm">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                      <span>
                        {formatDate(new Date(event.start), 'h:mm a')} -
                        {formatDate(new Date(event.end), 'h:mm a')}
                      </span>
                    </div>

                    {event.location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3.5 w-3.5 text-muted-foreground" />
                        <span>{event.location}</span>
                      </div>
                    )}

                    <div className="flex items-center gap-1">
                      {event.consultationType === 'video' ? (
                        <Video className="h-3.5 w-3.5 text-muted-foreground" />
                      ) : (
                        <FileText className="h-3.5 w-3.5 text-muted-foreground" />
                      )}
                      <span>
                        {event.consultationType === 'video'
                          ? 'Video Consultation'
                          : 'Document Review'}
                      </span>
                    </div>
                  </div>

                  {(event.client || event.lawyer) && (
                    <div className="flex items-center gap-2 mt-1">
                      {event.client && (
                        <div className="flex items-center gap-2">
                          <UserAvatar
                            userId={event.client.id}
                            avatarUrl={event.client.avatar_url}
                            fallbackText={event.client.full_name}
                            className="h-6 w-6"
                          />
                          <span className="text-sm">
                            {event.client.full_name}
                          </span>
                        </div>
                      )}

                      {event.lawyer && (
                        <div className="flex items-center gap-2 ml-auto">
                          <UserAvatar
                            userId={event.lawyer.id}
                            avatarUrl={event.lawyer.avatar_url}
                            fallbackText={event.lawyer.full_name}
                            className="h-6 w-6"
                          />
                          <span className="text-sm">
                            {event.lawyer.full_name}
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {event.status && (
                    <Badge
                      variant="outline"
                      className={
                        event.status === 'scheduled'
                          ? 'bg-blue-50 text-blue-700 border-blue-200'
                          : event.status === 'confirmed'
                            ? 'bg-green-50 text-green-700 border-green-200'
                            : event.status === 'completed'
                              ? 'bg-purple-50 text-purple-700 border-purple-200'
                              : 'bg-red-50 text-red-700 border-red-200'
                      }
                    >
                      {event.status.charAt(0).toUpperCase() +
                        event.status.slice(1)}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}

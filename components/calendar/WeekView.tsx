'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UserAvatar } from '@/components/ui/user-avatar';
import { CalendarEvent } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import {
  calculateEventPosition,
  formatDate,
  getDayHours,
  getEventColorClass,
  getWeekDays,
  isEventOnDay,
} from '@/lib/utils/calendar-utils';
import { isSameDay, isToday } from 'date-fns';
import { Calendar, Clock, FileText, MapPin, Video } from 'lucide-react';
import { useState } from 'react';

interface WeekViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  onDateClick: (date: Date) => void;
  isReadOnly?: boolean;
}

export function WeekView({
  currentDate,
  events,
  onEventClick,
  onDateClick,
  isReadOnly = false,
}: WeekViewProps) {
  const weekDays = getWeekDays(currentDate);
  const hours = getDayHours();
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);

  // Handle event click
  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {
    e.stopPropagation();
    if (isReadOnly) {
      setSelectedEvent(event);
      setIsEventDialogOpen(true);
    } else {
      onEventClick(event);
    }
  };

  // Handle time cell click
  const handleTimeClick = (day: Date, hour: number) => {
    const date = new Date(day);
    date.setHours(hour);
    date.setMinutes(0);
    date.setSeconds(0);
    onDateClick(date);
  };

  // Render events for a day
  const renderEvents = (day: Date) => {
    const dayEvents = events.filter((event) => isEventOnDay(event, day));

    return dayEvents.map((event) => {
      const { top, height, isAllDay } = calculateEventPosition(event, day);

      if (isAllDay) {
        return null; // All-day events are rendered separately
      }

      return (
        <div
          key={event.id}
          onClick={(e) => handleEventClick(event, e)}
          className={cn(
            'absolute left-1 right-1 rounded px-1 py-0.5 text-xs truncate border cursor-pointer',
            getEventColorClass(event)
          )}
          style={{
            top: `${top * 60}px`,
            height: `${height * 60}px`,
          }}
        >
          {formatDate(new Date(event.start), 'h:mm a')} {event.title}
        </div>
      );
    });
  };

  // Render all-day events
  const renderAllDayEvents = () => {
    const allDayEvents = events.filter((event) => event.allDay);

    return (
      <div className="grid grid-cols-7 gap-px bg-muted border-b">
        <div className="bg-background p-2 text-xs text-muted-foreground">
          All-day
        </div>
        {weekDays.map((day, index) => (
          <div key={index} className="bg-background p-1 min-h-[40px] relative">
            {allDayEvents
              .filter((event) => isEventOnDay(event, day))
              .map((event) => (
                <div
                  key={event.id}
                  onClick={(e) => handleEventClick(event, e)}
                  className={cn(
                    'text-xs px-1 py-0.5 rounded truncate mb-1 border cursor-pointer',
                    getEventColorClass(event)
                  )}
                >
                  {event.title}
                </div>
              ))}
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className="overflow-auto max-h-[calc(100vh-200px)]">
        {/* Day headers */}
        <div className="grid grid-cols-8 gap-px bg-muted sticky top-0 z-10">
          <div className="bg-background p-2 text-center font-medium"></div>
          {weekDays.map((day, index) => (
            <div
              key={index}
              className={cn(
                'bg-background p-2 text-center',
                isToday(day) && 'bg-accent'
              )}
            >
              <div className="font-medium">{formatDate(day, 'EEE')}</div>
              <div
                className={cn(
                  'text-2xl',
                  isToday(day) &&
                    'bg-primary text-primary-foreground rounded-full h-8 w-8 mx-auto'
                )}
              >
                {formatDate(day, 'd')}
              </div>
            </div>
          ))}
        </div>

        {/* All-day events */}
        {renderAllDayEvents()}

        {/* Time grid */}
        <div className="grid grid-cols-8 gap-px bg-muted">
          {/* Time labels */}
          <div className="bg-background">
            {hours.map((hour) => (
              <div
                key={hour}
                className="h-[60px] border-t text-xs text-muted-foreground pr-2 text-right relative"
              >
                <span className="absolute -top-2.5 right-2">
                  {hour === 0
                    ? '12 AM'
                    : hour < 12
                      ? `${hour} AM`
                      : hour === 12
                        ? '12 PM'
                        : `${hour - 12} PM`}
                </span>
              </div>
            ))}
          </div>

          {/* Days columns */}
          {weekDays.map((day, dayIndex) => (
            <div key={dayIndex} className="bg-background relative">
              {hours.map((hour) => (
                <div
                  key={hour}
                  onClick={() => handleTimeClick(day, hour)}
                  className={cn(
                    'h-[60px] border-t hover:bg-accent/20 cursor-pointer',
                    isSameDay(day, currentDate) && 'bg-accent/10'
                  )}
                ></div>
              ))}
              {renderEvents(day)}
            </div>
          ))}
        </div>
      </div>

      {/* Event details dialog */}
      <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{selectedEvent?.title}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedEvent && (
              <>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(
                      new Date(selectedEvent.start),
                      'EEEE, MMMM d, yyyy'
                    )}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(new Date(selectedEvent.start), 'h:mm a')} -
                    {formatDate(new Date(selectedEvent.end), 'h:mm a')}
                  </span>
                </div>

                {selectedEvent.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedEvent.location}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  {selectedEvent.consultationType === 'video' ? (
                    <Video className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span>
                    {selectedEvent.consultationType === 'video'
                      ? 'Video Consultation'
                      : 'Document Review'}
                  </span>
                </div>

                {selectedEvent.status && (
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={
                        selectedEvent.status === 'scheduled'
                          ? 'bg-blue-50 text-blue-700 border-blue-200'
                          : selectedEvent.status === 'confirmed'
                            ? 'bg-green-50 text-green-700 border-green-200'
                            : selectedEvent.status === 'completed'
                              ? 'bg-purple-50 text-purple-700 border-purple-200'
                              : 'bg-red-50 text-red-700 border-red-200'
                      }
                    >
                      {selectedEvent.status.charAt(0).toUpperCase() +
                        selectedEvent.status.slice(1)}
                    </Badge>
                  </div>
                )}

                {(selectedEvent.client || selectedEvent.lawyer) && (
                  <div className="flex items-center gap-2 mt-4">
                    {selectedEvent.client && (
                      <div className="flex items-center gap-2">
                        <UserAvatar
                          userId={selectedEvent.client.id}
                          avatarUrl={selectedEvent.client.avatar_url}
                          fallbackText={selectedEvent.client.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.client.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Client
                          </p>
                        </div>
                      </div>
                    )}

                    {selectedEvent.lawyer && (
                      <div className="flex items-center gap-2 ml-auto">
                        <UserAvatar
                          userId={selectedEvent.lawyer.id}
                          avatarUrl={selectedEvent.lawyer.avatar_url}
                          fallbackText={selectedEvent.lawyer.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.lawyer.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Lawyer
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {selectedEvent.description && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-1">Description</h4>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {selectedEvent.description}
                    </p>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => setIsEventDialogOpen(false)}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

'use client';

import { Field } from '@/lib/constants/schemas/template';
import { useFormState } from '@/lib/contexts/FormStateContext';
import { FormField } from './FormField';

export function TemplateFormRenderer() {
  const { currentSection, formState, setFormData } = useFormState();

  const handleFieldChange = (fieldId: string, value: unknown) => {
    setFormData({ [fieldId]: value });
  };

  // Use type assertion to handle the optional description property
  const sectionWithDescription = currentSection as unknown as {
    description?: string;
    title: string;
    fields: Field[];
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">{sectionWithDescription.title}</h2>
      {sectionWithDescription.description && (
        <p className="text-muted-foreground">
          {sectionWithDescription.description}
        </p>
      )}
      <div className="space-y-4">
        {sectionWithDescription.fields.map((field: Field) => (
          <FormField
            key={field.id}
            field={field}
            value={formState.formData[field.id]}
            onChange={(value: unknown) => handleFieldChange(field.id, value)}
          />
        ))}
      </div>
    </div>
  );
}

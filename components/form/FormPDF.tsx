'use client';

import { LegalTemplate } from '@/lib/constants/schemas/template';
import {
  Document,
  Font,
  Page,
  StyleSheet,
  Text,
  View,
} from '@react-pdf/renderer';

interface FormPDFProps {
  template: LegalTemplate;
  formData: Record<string, unknown>;
}

// Register fonts
Font.register({
  family: 'Inter',
  src: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2',
});

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    fontFamily: 'Inter',
  },
  header: {
    marginBottom: 20,
    textAlign: 'center',
  },
  title: {
    fontSize: 24,
    marginBottom: 10,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  content: {
    fontSize: 12,
    lineHeight: 1.5,
  },
  footer: {
    position: 'absolute',
    bottom: 40,
    left: 40,
    right: 40,
    borderTopWidth: 1,
    borderTopColor: '#e5e5e5',
    paddingTop: 10,
  },
  footerText: {
    fontSize: 8,
    color: '#666666',
  },
});

export function FormPDF({ template, formData }: FormPDFProps) {
  const renderSectionContent = (sectionId: string, content: string) => {
    const variables =
      template.sections.find((s) => s.id === sectionId)?.variables || [];

    let renderedContent = content;
    variables.forEach((variable) => {
      const value = formData[variable.name];
      const placeholder = `{${variable.name}}`;
      renderedContent = renderedContent.replace(
        new RegExp(placeholder, 'g'),
        value?.toString() || ''
      );
    });

    return renderedContent;
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>{template.metadata.name}</Text>
          <Text style={styles.description}>
            {template.metadata.description}
          </Text>
        </View>

        {/* Sections */}
        {template.sections.map((section) => (
          <View key={section.id} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <Text style={styles.content}>
              {renderSectionContent(section.id, section.content)}
            </Text>
          </View>
        ))}

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Generated on {new Date().toLocaleDateString()}
          </Text>
          <Text style={styles.footerText}>
            Document ID: {template.metadata.id}
          </Text>
          <Text style={styles.footerText}>
            Version: {template.metadata.version}
          </Text>
        </View>
      </Page>
    </Document>
  );
}

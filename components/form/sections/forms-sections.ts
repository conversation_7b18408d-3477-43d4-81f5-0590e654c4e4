export const ndaWizardSections = [
  {
    id: 'parties',
    title: 'Parties Information',
    description: 'Add parties involved in the NDA',
  },
  {
    id: 'confidentialInfo',
    title: 'Confidential Information',
    description: 'Define confidential information and examples',
  },
  {
    id: 'obligations',
    title: 'Obligations',
    description: 'Set non-disclosure and usage terms',
  },
  {
    id: 'term',
    title: 'Duration & Term',
    description: 'Specify agreement duration and survival period',
  },
  {
    id: 'jurisdiction',
    title: 'Legal Framework',
    description: 'Set governing law and dispute resolution',
  },
  {
    id: 'signatures',
    title: 'Signatures',
    description: 'Collect signatures from all parties',
  },
];

export const employmentWizardSections = [
  {
    id: 'parties',
    title: 'Employer & Employee Details',
    description: 'Add employer and employee information',
  },
  {
    id: 'position',
    title: 'Position Details',
    description: 'Define role, responsibilities and dates',
  },
  {
    id: 'compensation',
    title: 'Compensation & Benefits',
    description: 'Set salary, payment schedule and benefits',
  },
  {
    id: 'terms',
    title: 'Employment Terms',
    description: 'Specify termination, confidentiality and non-compete',
  },
  {
    id: 'resolution',
    title: 'Legal Framework',
    description: 'Set dispute resolution terms',
  },
];

export const leaseWizardSections = [
  {
    id: 'parties',
    title: 'Landlord & Tenant Details',
    description: 'Add landlord and tenant information',
  },
  {
    id: 'property',
    title: 'Property Details',
    description: 'Specify property address and description',
  },
  {
    id: 'terms',
    title: 'Lease Terms',
    description: 'Set duration and renewal options',
  },
  {
    id: 'financial',
    title: 'Financial Terms',
    description: 'Define rent, security deposit and fees',
  },
  {
    id: 'maintenance',
    title: 'Maintenance & Responsibilities',
    description: 'Outline maintenance duties',
  },
  {
    id: 'termination',
    title: 'Termination & Resolution',
    description: 'Set notice periods and dispute handling',
  },
];

export const generalContractWizardSections = [
  {
    id: 'parties',
    title: 'Party Details',
    description: 'Add all contracting parties',
  },
  {
    id: 'scope',
    title: 'Scope of Work',
    description: 'Define project scope and deliverables',
  },
  {
    id: 'payment',
    title: 'Payment Terms',
    description: 'Set payment amount, type and schedule',
  },
  {
    id: 'termination',
    title: 'Termination Terms',
    description: 'Define termination conditions',
  },
  {
    id: 'liability',
    title: 'Liability & Warranties',
    description: 'Set liability caps and warranty terms',
  },
  {
    id: 'resolution',
    title: 'Legal Framework',
    description: 'Specify dispute resolution process',
  },
];

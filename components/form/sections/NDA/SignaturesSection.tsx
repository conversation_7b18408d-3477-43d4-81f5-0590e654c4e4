'use client';

import BaseButton from '@/components/ux/comp/BaseButton';
import FormInput from '@/components/ux/comp/form-fields/FormInput';
import Subheading from '@/components/ux/comp/Subheading';
import { Plus } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

const SignaturesSection = () => {
  const { control } = useFormContext();
  const { fields, append } = useFieldArray({
    control,
    name: 'signatures',
  });

  return (
    <section className="flex w-full flex-col items-center gap-3 md:items-start">
      <Subheading>Signatures</Subheading>
      {fields.map((field, index) => (
        <div key={field.id} className="w-full">
          <FormInput
            vertical
            name={`signatures.${index}.partyId`}
            label="Party"
            placeholder="Select party"
          />
          <FormInput
            vertical
            type="date"
            name={`signatures.${index}.date`}
            label="Date"
          />
          <FormInput
            vertical
            name={`signatures.${index}.method`}
            label="Signature Method"
            placeholder="Select method"
          />
        </div>
      ))}
      <BaseButton variant="shadow" onClick={() => append({})}>
        <Plus className="size-4" />
        <span>Add Signature</span>
      </BaseButton>
    </section>
  );
};

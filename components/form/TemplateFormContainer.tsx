'use client';

import { But<PERSON> } from '@/components/ui/button';
import { LegalTemplate } from '@/lib/constants/schemas/template';
import { FormStateProvider } from '@/lib/contexts/FormStateContext';
import { Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';
import { FormNavigation } from './FormNavigation';
import { FormPreview } from './FormPreview';
import { TemplateFormRenderer } from './TemplateFormRenderer';

interface TemplateFormContainerProps {
  template: LegalTemplate;
  initialData?: Record<string, unknown>;
  onSubmit?: (data: Record<string, unknown>) => Promise<void>;
}

export function TemplateFormContainer({
  template,
  initialData,
  onSubmit,
}: TemplateFormContainerProps) {
  const [showPreview, setShowPreview] = useState(false);

  const handleSubmit = async (data: Record<string, unknown>) => {
    if (onSubmit) {
      await onSubmit(data);
    }
  };

  return (
    <FormStateProvider template={template} initialData={initialData}>
      <div className="flex flex-col gap-8 w-full max-w-4xl mx-auto p-6">
        <div className="flex justify-between items-center">
          <FormNavigation onSubmit={handleSubmit} />
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? (
              <>
                <EyeOff className="h-4 w-4" />
                Hide Preview
              </>
            ) : (
              <>
                <Eye className="h-4 w-4" />
                Show Preview
              </>
            )}
          </Button>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className={showPreview ? 'lg:block' : 'block'}>
            <TemplateFormRenderer />
          </div>
          {showPreview && (
            <div className="lg:block">
              <FormPreview />
            </div>
          )}
        </div>
      </div>
    </FormStateProvider>
  );
}

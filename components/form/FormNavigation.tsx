'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Section } from '@/lib/constants/schemas/template';
import { useFormState } from '@/lib/contexts/FormStateContext';
import { cn } from '@/lib/utils';
import { ArrowLeft, ArrowRight, Save } from 'lucide-react';

interface FormNavigationProps {
  onSubmit?: (data: Record<string, unknown>) => Promise<void>;
}

export function FormNavigation({ onSubmit }: FormNavigationProps) {
  const {
    progress,
    template,
    formState,
    nextSection,
    previousSection,
    goToSection,
    canGoNext,
    canGoPrevious,
    isSaving,
    saveProgress,
    isCurrentSectionValid,
  } = useFormState();

  const handleSubmit = async () => {
    if (onSubmit) {
      await onSubmit(formState.formData);
    }
  };

  return (
    <div className="flex flex-col gap-4 w-full">
      {/* Progress bar */}
      <div className="flex items-center gap-2">
        <Progress value={progress} className="flex-1" />
        <span className="text-sm text-muted-foreground">
          {Math.round(progress)}%
        </span>
      </div>

      {/* Section list */}
      <div className="flex flex-wrap gap-2">
        {template.sections.map((section: Section, index: number) => (
          <Button
            key={section.id}
            variant={formState.currentSection === index ? 'default' : 'outline'}
            size="sm"
            onClick={() => goToSection(index)}
            className={cn(
              'flex-1',
              formState.currentSection === index && 'font-bold'
            )}
          >
            {section.title}
          </Button>
        ))}
      </div>

      {/* Navigation controls */}
      <div className="flex justify-between items-center gap-4 mt-2">
        <Button
          variant="outline"
          onClick={previousSection}
          disabled={!canGoPrevious}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Previous
        </Button>

        <Button
          variant="outline"
          onClick={saveProgress}
          disabled={isSaving || !formState.isDirty}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          {isSaving ? 'Saving...' : 'Save'}
        </Button>

        {canGoNext ? (
          <Button
            variant="default"
            onClick={nextSection}
            disabled={!isCurrentSectionValid}
            className="flex items-center gap-2"
          >
            Next
            <ArrowRight className="h-4 w-4" />
          </Button>
        ) : (
          <Button
            variant="default"
            onClick={handleSubmit}
            disabled={!isCurrentSectionValid}
            className="flex items-center gap-2"
          >
            Submit
            <ArrowRight className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Last saved indicator */}
      {formState.lastSaved && (
        <p className="text-sm text-muted-foreground text-center">
          Last saved: {formState.lastSaved.toLocaleTimeString()}
        </p>
      )}
    </div>
  );
}

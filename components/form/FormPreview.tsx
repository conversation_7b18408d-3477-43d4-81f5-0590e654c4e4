'use client';

import { Button } from '@/components/ui/button';
import { useFormState } from '@/lib/contexts/FormStateContext';
import { cn } from '@/lib/utils';
import { PDFDownloadLink } from '@react-pdf/renderer';
import { Download, Eye, Printer } from 'lucide-react';
import { toast } from 'sonner';
import { FormPDF } from './FormPDF';

interface FormPreviewProps {
  className?: string;
}

export function FormPreview({ className }: FormPreviewProps) {
  const { template, formState } = useFormState();

  const renderSectionContent = (sectionId: string, content: string) => {
    // Replace variables in content with actual values
    const variables =
      template.sections.find((s) => s.id === sectionId)?.variables || [];

    let renderedContent = content;
    variables.forEach((variable) => {
      const value = formState.formData[variable.name];
      const placeholder = `{${variable.name}}`;
      renderedContent = renderedContent.replace(
        new RegExp(placeholder, 'g'),
        value?.toString() || ''
      );
    });

    return renderedContent;
  };

  const handlePrint = () => {
    window.print();
  };

  const fileName = `${template.metadata.name.toLowerCase().replace(/\s+/g, '-')}-${
    template.metadata.version
  }.pdf`;

  return (
    <div className={cn('flex flex-col gap-6', className)}>
      {/* Preview Header */}
      <div className="flex justify-between items-center print:hidden">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Document Preview
        </h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={handlePrint}
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <PDFDownloadLink
            document={
              <FormPDF template={template} formData={formState.formData} />
            }
            fileName={fileName}
          >
            {({ loading, url }) => {
              // Show toast when PDF is being generated
              if (loading) {
                toast.loading('Generating PDF...', {
                  id: 'pdf-generation',
                  duration: Infinity,
                });
              } else if (url) {
                // Dismiss the loading toast when PDF is ready
                toast.dismiss('pdf-generation');
                toast.success('PDF Generated', {
                  description: 'Your PDF is ready to download',
                  duration: 3000,
                });
              }

              return (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download PDF
                </Button>
              );
            }}
          </PDFDownloadLink>
        </div>
      </div>

      {/* Document Preview */}
      <div className="prose max-w-none p-6 border rounded-lg bg-white print:border-none">
        {/* Document Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">{template.metadata.name}</h1>
          <p className="text-muted-foreground">
            {template.metadata.description}
          </p>
        </div>

        {/* Document Sections */}
        <div className="space-y-6">
          {template.sections.map((section) => (
            <section key={section.id} className="break-inside-avoid">
              <h2 className="text-xl font-semibold mb-4">{section.title}</h2>
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{
                  __html: renderSectionContent(section.id, section.content),
                }}
              />
            </section>
          ))}
        </div>

        {/* Document Footer */}
        <footer className="mt-8 pt-8 border-t text-sm text-muted-foreground">
          <p>Generated on {new Date().toLocaleDateString()}</p>
          <p>Document ID: {template.metadata.id}</p>
          <p>Version: {template.metadata.version}</p>
        </footer>
      </div>
    </div>
  );
}

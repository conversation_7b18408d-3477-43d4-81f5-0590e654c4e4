'use client';

import { MobileNavigation } from '@/components/mobile/MobileNavigation';
import { UserAvatar } from '@/components/ui/user-avatar';
import { userStore } from '@/lib/store/user';
import { Bell } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

interface MobileLayoutProps {
  children: React.ReactNode;
}

export function MobileLayout({ children }: MobileLayoutProps) {
  const pathname = usePathname();
  const { user, profile } = userStore();
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [pageTitle, setPageTitle] = useState('');

  // Set page title based on pathname
  useEffect(() => {
    if (!pathname) return;

    // Extract the last part of the path
    const parts = pathname.split('/').filter(Boolean);
    const lastPart = parts[parts.length - 1];

    if (!lastPart) {
      setPageTitle('Dashboard');
      return;
    }

    // Format the title (convert kebab-case to Title Case)
    const formattedTitle = lastPart
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    setPageTitle(formattedTitle);
  }, [pathname]);

  // Get unread notifications count
  useEffect(() => {
    // This would be replaced with actual notification fetching logic
    setUnreadNotifications(3);
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="sticky top-0 z-40 bg-background border-b">
        <div className="container flex h-14 items-center">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <MobileNavigation />
              <h1 className="text-lg font-semibold">{pageTitle}</h1>
            </div>

            <div className="flex items-center gap-2">
              <div className="relative">
                <Bell className="h-5 w-5" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
                    {unreadNotifications}
                  </span>
                )}
              </div>

              {profile && (
                <UserAvatar
                  avatarUrl={profile.avatar_url}
                  fallbackText={profile.full_name || ''}
                  userId={user?.id}
                  className="h-8 w-8"
                />
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1">{children}</main>
    </div>
  );
}

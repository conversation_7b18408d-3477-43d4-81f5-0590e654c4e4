'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { UserAvatar } from '@/components/ui/user-avatar';
import { userStore } from '@/lib/store/user';
import { authService } from '@/lib/supabase/auth/auth-service';
import { cn } from '@/lib/utils';
import {
  BarChart,
  Bell,
  Calendar,
  FileText,
  Home,
  LogOut,
  Menu,
  MessageSquare,
  Repeat,
  Settings,
  Users,
  X,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface MobileNavigationProps {
  className?: string;
}

export function MobileNavigation({ className }: MobileNavigationProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, profile, removeUser, removeProfile } = userStore();
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Set mounted to true on client side
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close sheet when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  // Check if the current route is active
  const isActive = (path: string) => {
    if (!mounted) return false;
    return pathname === path || pathname.startsWith(path);
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await authService.signOut();
      removeProfile(null);
      removeUser(null);
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Get navigation items based on user role
  const getNavItems = () => {
    const username = profile?.username || '';
    const isLawyer = profile?.user_role === 'lawyer';

    const commonItems = [
      {
        name: 'Dashboard',
        href: `/${username}/dashboard`,
        icon: Home,
        active: isActive(`/${username}/dashboard`),
      },
      {
        name: 'Documents',
        href: `/${username}/documents`,
        icon: FileText,
        active: isActive(`/${username}/documents`),
      },
      {
        name: 'Calendar',
        href: `/${username}/calendar`,
        icon: Calendar,
        active: isActive(`/${username}/calendar`),
      },
      {
        name: 'Notifications',
        href: `/${username}/notifications`,
        icon: Bell,
        active: isActive(`/${username}/notifications`),
      },
      {
        name: 'Settings',
        href: `/${username}/settings`,
        icon: Settings,
        active: isActive(`/${username}/settings`),
      },
    ];

    // Add lawyer-specific items
    if (isLawyer) {
      return [
        ...commonItems,
        {
          name: 'Clients',
          href: `/${username}/lawyer/clients`,
          icon: Users,
          active: isActive(`/${username}/lawyer/clients`),
        },
        {
          name: 'Consultations',
          href: `/${username}/lawyer/consultations`,
          icon: MessageSquare,
          active: isActive(`/${username}/lawyer/consultations`),
        },
        {
          name: 'Recurring Consultations',
          href: `/${username}/lawyer/recurring-consultations`,
          icon: Repeat,
          active: isActive(`/${username}/lawyer/recurring-consultations`),
        },
        {
          name: 'Reports',
          href: `/${username}/lawyer/reports`,
          icon: BarChart,
          active: isActive(`/${username}/lawyer/reports`),
        },
      ];
    }

    return commonItems;
  };

  if (!mounted) return null;

  return (
    <div className={cn('md:hidden', className)}>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="h-10 w-10">
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent className="w-[280px] sm:w-[350px] p-0">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center gap-3">
                {profile && (
                  <UserAvatar
                    avatarUrl={profile.avatar_url}
                    fallbackText={profile.full_name || ''}
                    userId={user?.id}
                    className="h-10 w-10"
                  />
                )}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {profile?.full_name}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {user?.email}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-auto py-2">
              <nav className="grid gap-1 px-2">
                {getNavItems().map((item, index) => (
                  <Link
                    key={index}
                    href={item.href}
                    className={cn(
                      'flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium',
                      item.active
                        ? 'bg-primary/10 text-primary'
                        : 'hover:bg-accent'
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </Link>
                ))}
              </nav>
            </div>

            {/* Footer */}
            <div className="p-4 border-t">
              <Button
                variant="ghost"
                className="w-full justify-start"
                onClick={handleSignOut}
              >
                <LogOut className="h-5 w-5 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}

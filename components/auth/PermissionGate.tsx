import { usePermissions } from '@/lib/hooks/usePermissions';
import { Permission } from '@/lib/types/database-modules';
import { ReactNode } from 'react';

interface PermissionGateProps {
  children: ReactNode;
  fallback?: ReactNode;
  permissions?: Permission[];
  requireAll?: boolean;
}

export function PermissionGate({
  children,
  fallback = null,
  permissions = [],
  requireAll = true,
}: PermissionGateProps) {
  const { hasPermission, loading } = usePermissions();

  if (loading) {
    return null; // Or a loading spinner
  }

  // If no permissions are specified, render children
  if (permissions.length === 0) {
    return <>{children}</>;
  }

  // Check if user has required permissions
  const hasAccess = requireAll
    ? permissions.every((permission) => hasPermission(permission))
    : permissions.some((permission) => hasPermission(permission));

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

// Usage example:
/*
<PermissionGate
  permissions={['create_form', 'edit_form']}
  requireAll={false}
  fallback={<p>You don't have permission to view this content.</p>}
>
  <CreateFormButton />
</PermissionGate>
*/

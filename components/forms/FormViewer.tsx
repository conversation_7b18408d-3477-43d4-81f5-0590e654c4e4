'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Download, Printer, Save } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface FormViewerProps {
  htmlContent: string;
  title?: string;
  onDownload?: () => void;
  onSave?: (updatedContent?: string) => void;
  isEditable?: boolean;
}

export function FormViewer({
  htmlContent,
  title,
  onDownload,
  onSave,
  isEditable = true,
}: FormViewerProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeHeight, setIframeHeight] = useState<number>(500);

  useEffect(() => {
    if (!iframeRef.current) return;

    // Create a complete HTML document including styles to render in the iframe
    const completeHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${title || 'Generated Form'}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              margin: 20px;
              padding: 0;
            }
            input, textarea, select {
              ${isEditable ? '' : 'pointer-events: none;'}
              ${isEditable ? '' : 'background-color: #f9f9f9;'}
              width: 100%;
              padding: 8px;
              margin-bottom: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
            }
            button {
              ${isEditable ? '' : 'display: none;'}
            }
            .form-group {
              margin-bottom: 15px;
            }
          </style>
        </head>
        <body>
          ${htmlContent}
          <script>
            // Resize iframe based on content height
            function adjustIframeHeight() {
              const height = document.body.scrollHeight;
              window.parent.postMessage({ type: 'resize', height: height }, '*');
            }
            
            // Adjust on load and when the window resizes
            window.addEventListener('load', adjustIframeHeight);
            window.addEventListener('resize', adjustIframeHeight);
            
            // Make the form not submit normally if editable
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
              form.addEventListener('submit', function(e) {
                e.preventDefault();
                window.parent.postMessage({ type: 'submit', formData: new FormData(form) }, '*');
                return false;
              });
            });
          </script>
        </body>
      </html>
    `;

    // Set iframe content
    const iframe = iframeRef.current;
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

    if (iframeDoc) {
      iframeDoc.open();
      iframeDoc.write(completeHtml);
      iframeDoc.close();
    }

    // Listen for resize messages from the iframe
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'resize') {
        setIframeHeight(event.data.height + 40); // Add some padding
      }
      if (event.data && event.data.type === 'submit' && onSave) {
        // Get the updated HTML content from the iframe
        const iframeHTML = iframeDoc?.body.innerHTML || '';
        onSave(iframeHTML);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [htmlContent, title, isEditable, onSave]);

  const handlePrint = () => {
    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const iframeWin = iframe.contentWindow;

      if (iframeWin) {
        iframeWin.focus();
        iframeWin.print();
      }
    }
  };

  const captureFormContent = () => {
    if (!iframeRef.current) return '';

    const iframe = iframeRef.current;
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDoc) return '';

    return iframeDoc.documentElement.outerHTML;
  };

  const handleSave = () => {
    if (onSave) {
      const content = captureFormContent();
      onSave(content);
    }
  };

  return (
    <div className="form-viewer">
      <Card className="w-full mb-4">
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">
              {title || 'Generated Form'}
            </h2>
            <div className="flex gap-2">
              {isEditable && onSave && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSave}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  <span>Save</span>
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                <span>Print</span>
              </Button>
              {onDownload && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onDownload()}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  <span>Download</span>
                </Button>
              )}
            </div>
          </div>
          <iframe
            ref={iframeRef}
            className="w-full border rounded-md"
            style={{ height: `${iframeHeight}px`, overflow: 'hidden' }}
            frameBorder="0"
            title={title || 'Form Viewer'}
          />
        </CardContent>
      </Card>
    </div>
  );
}

import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AIService, DocumentAnalysis } from '@/lib/services/AIService';
import { AlertCircle, CheckCircle2, Info } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'sonner';

interface AIDocumentAnalyzerProps {
  documentContent: string;
  documentTitle: string;
  onClose: () => void;
  open: boolean;
}

export function AIDocumentAnalyzer({
  documentContent,
  documentTitle,
  onClose,
  open,
}: AIDocumentAnalyzerProps) {
  const [analysis, setAnalysis] = useState<DocumentAnalysis | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<
    'summary' | 'risks' | 'suggestions'
  >('summary');

  const aiService = AIService.getInstance();

  const handleAnalyze = async () => {
    setError(null);

    // Create a promise for the AI analysis
    const analyzePromise = aiService.analyzeDocument(documentContent, 'all');

    // Use toast.promise to handle loading, success, and error states
    toast.promise(analyzePromise, {
      loading: 'Analyzing document...',
      success: (result) => {
        setAnalysis(result);
        return 'Document analysis complete';
      },
      error: (err) => {
        console.error('Error analyzing document:', err);
        setError('Failed to analyze document');
        return 'Failed to analyze document';
      },
    });
  };

  React.useEffect(() => {
    if (open && !analysis) {
      handleAnalyze();
    }
  }, [open, analysis]);

  const getSeverityIcon = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'low':
        return <Info className="h-5 w-5 text-blue-500" />;
      case 'medium':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'high':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>AI Document Analysis</DialogTitle>
          <DialogDescription>
            Smart analysis of your document: {documentTitle}
          </DialogDescription>
        </DialogHeader>

        {error ? (
          <div className="text-red-500 py-4">{error}</div>
        ) : analysis ? (
          <Tabs
            defaultValue="summary"
            value={activeTab}
            onValueChange={(v) => setActiveTab(v as any)}
          >
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="risks">Risks</TabsTrigger>
              <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
            </TabsList>

            <TabsContent value="summary" className="space-y-4 pt-4">
              <div className="text-sm">{analysis.summary}</div>

              <h4 className="text-sm font-medium mt-4">Key Points</h4>
              <ul className="list-disc pl-5 space-y-1">
                {analysis.keyPoints.map((point, index) => (
                  <li key={index} className="text-sm">
                    {point}
                  </li>
                ))}
              </ul>
            </TabsContent>

            <TabsContent value="risks" className="space-y-4 pt-4">
              {analysis.risks.length === 0 ? (
                <div className="flex items-center justify-center p-4 text-sm text-green-500">
                  <CheckCircle2 className="h-5 w-5 mr-2" />
                  No significant risks detected
                </div>
              ) : (
                <div className="space-y-3">
                  {analysis.risks.map((risk, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-2 p-3 rounded-md border"
                    >
                      {getSeverityIcon(risk.severity)}
                      <div>
                        <div className="text-sm font-medium capitalize">
                          {risk.severity} Risk
                        </div>
                        <div className="text-sm">{risk.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="suggestions" className="space-y-4 pt-4">
              {analysis.suggestions.length === 0 ? (
                <div className="text-sm text-center py-4">
                  No suggestions available
                </div>
              ) : (
                <ul className="list-disc pl-5 space-y-1">
                  {analysis.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-sm">
                      {suggestion}
                    </li>
                  ))}
                </ul>
              )}
            </TabsContent>
          </Tabs>
        ) : null}

        <DialogFooter>
          <Button variant="secondary" onClick={handleAnalyze}>
            Refresh Analysis
          </Button>
          <Button variant="default" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

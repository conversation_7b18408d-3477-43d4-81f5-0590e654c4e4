'use client';
import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';

import { Input } from '@/components/ui/input';
import { userStore } from '@/lib/store/user';
import { authService } from '@/lib/supabase/auth/auth-service';
import { supabaseClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { Envelope, Lock } from '@/components/ux/icons';
import { cn } from '@/lib/utils';
import { FONT_GEIST_SANS } from '@/lib/constants';
import { SocialLogin } from '@/components/auth/SocialLogin';
import Link from 'next/link';

const FormSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  password: z.string().min(6),
});

export default function LoginForm() {
  const supabase = supabaseClient;
  const { updateUser, updateProfile } = userStore((state) => state);
  const router = useRouter();
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const [submit, setSubmit] = useState('Login');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Social login is now handled by the SocialLogin component

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = (d: z.infer<typeof FormSchema>) => {
    return new Promise(async (resolve, reject) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: d.email,
        password: d.password,
      });
      if (error || !data) {
        reject(error);
      } else {
        updateUser(data.user);
        await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single()
          .then(({ data, error }) => {
            if (error || !data) {
              reject(error);
              console.log(error);
            } else {
              updateProfile(data);
              resolve(data.username);
              router.push(`/${data.username}`);
            }
          });
      }
    });
  };

  return (
    <div className={cn('w-full')}>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => {
            return toast.promise(onSubmit(data), {
              loading: 'Logging in...',
              success: (data: any) => `Logged in as ${data}`,
              error: (err: any) => `Error: ${err.message}`,
            });
          })}
          className="my-4 flex w-full flex-col items-center space-y-4"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="relative flex w-full">
                    <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <Envelope className="size-5 text-gray-400" />
                    </span>
                    <Input
                      className="w-80"
                      withIcon
                      placeholder="Email"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage className="pl-2" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex justify-between items-center w-80">
                  <FormLabel>Password</FormLabel>
                  <Link
                    href="/auth/forgot-password"
                    className="text-xs text-accent-500 hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
                <FormControl>
                  <div className="relative flex w-full">
                    <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <Lock className="size-5 text-gray-400" />
                    </span>
                    <Input
                      type={showPassword ? 'text' : 'password'}
                      className="w-80 border border-neutral-200"
                      withIcon
                      placeholder="Password"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage className="pl-2" />
                <div className="flex items-center mt-2 pl-2 space-x-2">
                  <Checkbox
                    id="show-password"
                    checked={showPassword}
                    onCheckedChange={togglePasswordVisibility}
                  />
                  <Label
                    htmlFor="show-password"
                    className="text-sm text-gray-600 cursor-pointer"
                  >
                    Show password
                  </Label>
                </div>
              </FormItem>
            )}
          />
          <Button variant={'shadow_accent'} className="w-60" type="submit">
            {submit}
          </Button>
        </form>
      </Form>

      <div className="mt-4 flex flex-col items-center w-80">
        <SocialLogin mode="login" />
      </div>
    </div>
  );
}

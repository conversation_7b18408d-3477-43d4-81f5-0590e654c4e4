'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { SocialLogin } from '@/components/auth/SocialLogin';
import { Input } from '@/components/ui/input';
import { Envelope, Lock, UserFill } from '@/components/ux/icons';
import { FONT_JETBRAINS_MONO } from '@/lib/constants';
import { authService } from '@/lib/supabase/auth/auth-service';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useState } from 'react';
import { toast } from 'sonner';

const FormSchema = z
  .object({
    username: z.string().min(1, { message: 'User Name Is Required' }),
    full_name: z.string().min(1, { message: 'First Name Is Required' }),
    email: z.string().email({ message: 'Please enter a valid email address.' }),
    password: z.string().min(6),
    confirmPassword: z.string().min(6),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

export default function CreateAccountForm() {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      full_name: '',
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const [isVerificationSent, setIsVerificationSent] = useState(false);
  const [userEmail, setUserEmail] = useState('');

  const onSubmit = (d: z.infer<typeof FormSchema>) => {
    return new Promise(async (resolve, reject) => {
      try {
        console.log('Starting seamless sign up process...');

        // Sign up with email verification - triggers will handle profile/settings creation
        const data = await authService.signUp(d.email, d.password, {
          full_name: d.full_name,
          username: d.username,
        });

        if (!data || !data.user) {
          reject(new Error('Failed to create account'));
          return;
        }

        console.log(
          'User created successfully with seamless triggers:',
          data.user.id
        );

        // Set verification sent state immediately - triggers handle the rest
        setIsVerificationSent(true);
        setUserEmail(d.email);
        resolve(data);
      } catch (error: any) {
        console.error('Sign up error:', error);
        reject(error);
      }
    });
  };

  const handleResendVerification = async () => {
    if (!userEmail) return;

    try {
      await authService.resendVerificationEmail(userEmail);
      toast.success('Verification email resent', {
        description: 'Please check your inbox',
      });
    } catch (error) {
      console.error('Error resending verification email:', error);
      toast.error('Failed to resend verification email');
    }
  };

  return (
    <div className="w-full">
      {isVerificationSent ? (
        <div className="flex flex-col items-center mx-auto max-w-sm  space-y-4 p-6 text-center">
          <div className="rounded-full bg-green-100 p-3">
            <Envelope className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-xl font-bold">Verify your email</h2>
          <p className="text-muted-foreground">
            We've sent a verification email to{' '}
            <strong className={cn(FONT_JETBRAINS_MONO.className, '')}>
              {userEmail}
            </strong>
            . Please check your inbox and click the link to verify your account.
          </p>
          <div className="mt-4">
            <Button
              variant="outline"
              onClick={handleResendVerification}
              className="mt-2"
            >
              Resend verification email
            </Button>
          </div>
          <div className="mt-4">
            <Link
              href="/login"
              className="text-sm text-accent-500 hover:underline"
            >
              Back to login
            </Link>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit((data) => {
                return toast.promise(onSubmit(data), {
                  loading: 'Creating Account...',
                  success: (data: any) =>
                    `Verification email sent to ${data.user?.email}`,
                  error: (err) => `Error: ${err.message}`,
                });
              })}
              className="my-2 flex w-full flex-col items-center space-y-4"
            >
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <div className="relative flex w-full">
                        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <UserFill className="size-6 text-gray-400 md:size-5" />
                        </span>
                        <Input
                          className="w-80"
                          withIcon
                          placeholder="Username"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="pl-2" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <div className="relative flex w-full">
                        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <UserFill className="size-6 text-gray-400 md:size-5" />
                        </span>
                        <Input
                          className="w-80"
                          withIcon
                          placeholder="Full Name"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="pl-2" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <div className="relative flex w-full">
                        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <Envelope className="size-6 text-gray-400 md:size-5" />
                        </span>
                        <Input
                          className="w-80"
                          withIcon
                          placeholder="Email"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="pl-2" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <div className="relative flex w-full">
                        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <Lock className="size-6 text-gray-400 md:size-5" />
                        </span>
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          className="w-80"
                          withIcon
                          placeholder="Password"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="pl-2" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative flex w-full">
                        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <Lock className="size-6 text-gray-400 md:size-5" />
                        </span>
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          className="w-80"
                          withIcon
                          placeholder="Confirm Password"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="pl-2" />
                    <div className="flex items-center mt-2 pl-2 space-x-2">
                      <Checkbox
                        id="show-password"
                        checked={showPassword}
                        onCheckedChange={togglePasswordVisibility}
                      />
                      <Label
                        htmlFor="show-password"
                        className="text-sm text-gray-600 cursor-pointer"
                      >
                        Show password
                      </Label>
                    </div>
                  </FormItem>
                )}
              />
              <Button
                variant={'shadow_accent'}
                className="h-10 rounded-3xl"
                type="submit"
              >
                Create Account
              </Button>
              <div className="flex-flex-col items-center text-center text-sm">
                <span className="">
                  By clicking Register, you agree to accept our
                </span>
                <br />
                <Link
                  href={'/terms-and-conditions'}
                  className="text-neutral-500 hover:text-black hover:underline"
                >
                  Terms and Conditions
                </Link>
              </div>
            </form>
          </Form>

          <div className="mt-4 flex flex-col items-center w-80">
            <SocialLogin mode="signup" />
          </div>
        </div>
      )}
    </div>
  );
}

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { AIService, FormFieldSuggestion } from '@/lib/services/AIService';
import { Bot, Sparkles, Stars, Wand2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface AIFormSuggestionsProps {
  fieldName: string;
  fieldType: string;
  formContext: Record<string, any>;
  onSelectSuggestion: (suggestion: string) => void;
}

export function AIFormSuggestions({
  fieldName,
  fieldType,
  formContext,
  onSelectSuggestion,
}: AIFormSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<FormFieldSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const aiService = AIService.getInstance();

  const loadSuggestions = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await aiService.suggestFieldValue(
        fieldName,
        fieldType,
        formContext
      );

      setSuggestions(result);
    } catch (err) {
      console.error('Error fetching suggestions:', err);
      setError('Failed to load suggestions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && suggestions.length === 0 && !loading && !error) {
      loadSuggestions();
    }
  }, [open]);

  const handleSelectSuggestion = (value: string) => {
    onSelectSuggestion(value);
    setOpen(false);
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) {
      return <Badge className="bg-green-500">High Confidence</Badge>;
    } else if (confidence >= 0.5) {
      return <Badge className="bg-yellow-500">Medium Confidence</Badge>;
    } else {
      return <Badge className="bg-red-500">Low Confidence</Badge>;
    }
  };

  const getFieldDisplay = (name: string) => {
    // Convert camelCase or snake_case to Title Case with spaces
    return name
      .replace(/([A-Z])/g, ' $1') // Insert a space before all caps
      .replace(/_/g, ' ') // Replace underscores with spaces
      .replace(/^\w/, (c) => c.toUpperCase()) // Uppercase the first character
      .trim();
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 rounded-full bg-amber-50 text-amber-600 hover:bg-amber-100 hover:text-amber-700"
          title="Get AI suggestions"
        >
          <Sparkles className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-2 bg-amber-50/50">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium flex items-center">
                <Bot className="h-4 w-4 mr-1.5 text-amber-600" />
                AI Suggestions
              </CardTitle>
              <Badge
                variant="outline"
                className="bg-amber-100 text-amber-800 border-amber-200"
              >
                Smart AI
              </Badge>
            </div>
            <CardDescription className="text-xs">
              Smart suggestions for {getFieldDisplay(fieldName)}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-3">
            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : error ? (
              <div className="text-sm text-red-500 p-2 bg-red-50 rounded-md">
                <p className="font-medium mb-1">Error</p>
                <p>{error}</p>
              </div>
            ) : (
              <div className="space-y-2">
                {suggestions.map((suggestion, index) => (
                  <div
                    key={index}
                    className="rounded-md border p-2 cursor-pointer hover:bg-muted transition-colors"
                    onClick={() => handleSelectSuggestion(suggestion.value)}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <div className="font-medium truncate text-sm">
                        {suggestion.value}
                      </div>
                      {getConfidenceBadge(suggestion.confidence)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {suggestion.explanation}
                    </div>
                  </div>
                ))}
                {suggestions.length === 0 && !loading && (
                  <div className="flex flex-col items-center justify-center text-center py-6">
                    <Stars className="h-10 w-10 text-muted-foreground/50 mb-2" />
                    <div className="text-sm text-muted-foreground font-medium">
                      No suggestions available
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Try providing more context in other fields
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
          <CardFooter className="bg-muted/10 p-3 flex justify-between border-t">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setOpen(false)}
              className="text-xs h-8"
            >
              Cancel
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSuggestions}
              disabled={loading}
              className="text-xs h-8"
            >
              <Wand2 className="h-3 w-3 mr-1" />
              Refresh
            </Button>
          </CardFooter>
        </Card>
      </PopoverContent>
    </Popover>
  );
}

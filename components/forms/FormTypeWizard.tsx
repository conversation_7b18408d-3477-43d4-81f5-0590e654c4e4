'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useDocuments } from '@/lib/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Check, FileText, Sparkles } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

const formSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  jurisdiction: z.string().min(1, 'Please select a jurisdiction'),
  language: z.string().min(1, 'Please select a language'),
  additionalContext: z.string().optional(),
  useAI: z.boolean().optional().default(true),
});

const jurisdictions = [
  { id: 'ghana', name: 'Ghana' },
  { id: 'nigeria', name: 'Nigeria' },
  { id: 'kenya', name: 'Kenya' },
  { id: 'us', name: 'United States' },
  { id: 'uk', name: 'United Kingdom' },
  { id: 'canada', name: 'Canada' },
  { id: 'australia', name: 'Australia' },
  { id: 'south_africa', name: 'South Africa' },
];

const languages = [
  { id: 'en', name: 'English' },
  { id: 'fr', name: 'French' },
  { id: 'es', name: 'Spanish' },
  { id: 'pt', name: 'Portuguese' },
  { id: 'ar', name: 'Arabic' },
];

interface FormTypeWizardProps {
  formType: string;
  category: string;
  onBack: () => void;
}

export function FormTypeWizard({
  formType,
  category,
  onBack,
}: FormTypeWizardProps) {
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedForm, setGeneratedForm] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [editedContent, setEditedContent] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [formProgress, setFormProgress] = useState(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [aiActive, setAiActive] = useState(true);

  // Define the form type
  type FormValues = z.infer<typeof formSchema>;

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      description: '',
      jurisdiction: '',
      language: '',
      additionalContext: '',
      useAI: true,
    },
  }) as UseFormReturn<FormValues>;

  const watchJurisdiction = form.watch('jurisdiction');

  // Update progress when fields are filled
  const updateProgress = () => {
    const requiredFields = ['title', 'description', 'jurisdiction', 'language'];
    const filledFields = requiredFields.filter(
      (field) => !!form.getValues(field as any)
    );
    const progress = Math.round(
      (filledFields.length / requiredFields.length) * 100
    );
    setFormProgress(progress);
  };

  // Use the documents hook for form generation and saving
  const { createDocument } = useDocuments();

  const onSubmit = async (values: FormValues) => {
    setIsGenerating(true);
    setValidationErrors([]);

    try {
      // Simulate form generation with AI (in a real implementation, this would call an AI service)
      // For now, we'll create a placeholder content based on the form values
      const generatedContent = `# ${values.title}\n\n${values.description}\n\n## Jurisdiction\n${
        jurisdictions.find((j) => j.id === values.jurisdiction)?.name ||
        values.jurisdiction
      }\n\n## Language\n${
        languages.find((l) => l.id === values.language)?.name || values.language
      }\n\n${values.additionalContext ? `## Additional Context\n${values.additionalContext}` : ''}`;

      // Set the generated form content
      setGeneratedForm(generatedContent);

      // Save the form data to localStorage as a backup
      const formData = {
        content: generatedContent,
        title: values.title,
        description: values.description,
      };
      localStorage.setItem('generatedForm', JSON.stringify(formData));

      // Move to the next step
      setCurrentStep(1);

      toast.success('Form generated successfully');
    } catch (error: any) {
      console.error('Error generating form:', error);
      toast.error(
        error.message || 'Failed to generate form. Please try again.'
      );
      setValidationErrors([
        error.message || 'Failed to generate form. Please try again.',
      ]);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveChanges = async () => {
    if (!editedContent) return;

    setIsSaving(true);
    try {
      // Use the createDocument hook to save the form
      toast.promise(
        createDocument.mutate({
          title: form.getValues().title,
          description: form.getValues().description,
          content: editedContent,
          document_type: 'form',
          is_template: false,
          status: 'draft',
          metadata: {
            formType,
            category,
          },
        }),
        {
          loading: 'Saving form...',
          success: (document) => {
            router.push(`/forms/${document.id}`);
            return 'Form saved successfully';
          },
          error: 'Failed to save form',
        }
      );
    } catch (error) {
      console.error('Error saving form:', error);
      toast.error('Failed to save form');
      setValidationErrors(['Failed to save form. Please try again.']);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAISuggestion = (field: string, value: string) => {
    form.setValue(field as any, value);
    updateProgress();
  };

  const handleContentChange = (content: string) => {
    // This function will be called when the content is edited
    setEditedContent(content);
  };

  // Set editedContent when generatedForm changes
  useEffect(() => {
    if (generatedForm && !editedContent) {
      setEditedContent(generatedForm);
    }
  }, [generatedForm, editedContent]);

  const getTitleSuggestion = () => {
    if (!form.getValues('jurisdiction') || !formType) return null;

    const formTypeMap = {
      contract: 'Legal Contract',
      nda: 'Non-Disclosure Agreement',
      employment: 'Employment Contract',
      lease: 'Property Lease Agreement',
      registration: 'Business Registration',
      invoice: 'Invoice Template',
      proposal: 'Business Proposal',
    };

    return `${formTypeMap[formType as keyof typeof formTypeMap] || formType} - ${jurisdictions.find((j) => j.id === form.getValues('jurisdiction'))?.name || ''}`;
  };

  const getDescriptionSuggestion = () => {
    if (!form.getValues('jurisdiction') || !formType) return null;

    const formTypeMap = {
      contract: `Standard legal contract compliant with ${jurisdictions.find((j) => j.id === form.getValues('jurisdiction'))?.name || ''} law.`,
      nda: `Confidentiality agreement for protecting business information in ${jurisdictions.find((j) => j.id === form.getValues('jurisdiction'))?.name || ''}.`,
      employment: `Employment agreement following ${jurisdictions.find((j) => j.id === form.getValues('jurisdiction'))?.name || ''} labor regulations.`,
      lease: `Property lease agreement conforming to ${jurisdictions.find((j) => j.id === form.getValues('jurisdiction'))?.name || ''} tenancy laws.`,
      registration: `Business registration form for ${jurisdictions.find((j) => j.id === form.getValues('jurisdiction'))?.name || ''} company incorporation.`,
    };

    return formTypeMap[formType as keyof typeof formTypeMap] || '';
  };

  return (
    <div className="space-y-8">
      <Progress value={formProgress} className="h-2" />

      {currentStep === 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Form Details</CardTitle>
                <CardDescription>
                  Provide details about the form you want to create. Our AI will
                  help generate a customized form based on your inputs.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <FormLabel>Form Title</FormLabel>
                        {aiActive && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6"
                                  onClick={() => {
                                    const suggestion = getTitleSuggestion();
                                    if (suggestion)
                                      handleAISuggestion('title', suggestion);
                                  }}
                                >
                                  <Sparkles className="h-3.5 w-3.5 mr-1 text-amber-500" />
                                  <span className="text-xs">Suggest</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Get AI suggestion for title</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                placeholder="Enter form title"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e);
                                  updateProgress();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex items-center justify-between">
                        <FormLabel>Description</FormLabel>
                        {aiActive && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6"
                                  onClick={() => {
                                    const suggestion =
                                      getDescriptionSuggestion();
                                    if (suggestion)
                                      handleAISuggestion(
                                        'description',
                                        suggestion
                                      );
                                  }}
                                >
                                  <Sparkles className="h-3.5 w-3.5 mr-1 text-amber-500" />
                                  <span className="text-xs">Suggest</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Get AI suggestion for description</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Textarea
                                placeholder="Describe the purpose and requirements of your form"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e);
                                  updateProgress();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="jurisdiction"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Jurisdiction</FormLabel>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                updateProgress();
                              }}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select jurisdiction" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {jurisdictions.map((jurisdiction) => (
                                  <SelectItem
                                    key={jurisdiction.id}
                                    value={jurisdiction.id}
                                  >
                                    {jurisdiction.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="language"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Language</FormLabel>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                updateProgress();
                              }}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select language" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {languages.map((language) => (
                                  <SelectItem
                                    key={language.id}
                                    value={language.id}
                                  >
                                    {language.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="additionalContext"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Additional Context (Optional)</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Add any specific requirements or context for your form"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Add any specific clauses, terms, or details you
                              want included in your form.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="useAI"
                        render={({ field }) => (
                          <div className="flex items-center gap-3 pt-2">
                            <Switch
                              checked={field.value}
                              id="useAI"
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                                setAiActive(checked);
                              }}
                            />
                            <label
                              htmlFor="useAI"
                              className="text-sm font-medium flex items-center cursor-pointer"
                            >
                              <Sparkles className="h-4 w-4 mr-1 text-amber-500" />
                              Use AI to enhance form generation
                            </label>
                          </div>
                        )}
                      />
                    </div>

                    {validationErrors.length > 0 && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Error</AlertTitle>
                        <AlertDescription>
                          <ul className="list-disc pl-4">
                            {validationErrors.map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="flex justify-between pt-2">
                      <Button type="button" variant="outline" onClick={onBack}>
                        Back
                      </Button>
                      <Button type="submit" disabled={isGenerating}>
                        {isGenerating ? (
                          <>
                            <span className="animate-spin mr-2">⟳</span>
                            Generating...
                          </>
                        ) : (
                          'Generate Form'
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Form Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium">Selected Form Type</h4>
                    <p className="text-sm text-muted-foreground capitalize">
                      {formType}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium">Category</h4>
                    <p className="text-sm text-muted-foreground capitalize">
                      {category}
                    </p>
                  </div>

                  {watchJurisdiction && (
                    <div>
                      <h4 className="text-sm font-medium">Jurisdiction</h4>
                      <p className="text-sm text-muted-foreground">
                        {
                          jurisdictions.find((j) => j.id === watchJurisdiction)
                            ?.name
                        }
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Template Guidelines</CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible defaultValue="item-1">
                  <AccordionItem value="item-1">
                    <AccordionTrigger>Legal Requirements</AccordionTrigger>
                    <AccordionContent>
                      <ul className="text-sm space-y-1 list-disc pl-4">
                        {watchJurisdiction === 'ghana' && (
                          <>
                            <li>
                              Must comply with Ghana Contract Act, 1960 (Act 25)
                            </li>
                            <li>
                              Must be in writing and signed by both parties
                            </li>
                            <li>Must clearly state the consideration</li>
                            <li>Must be properly dated and witnessed</li>
                          </>
                        )}
                        {(watchJurisdiction === 'us' || !watchJurisdiction) && (
                          <>
                            <li>Must identify all parties clearly</li>
                            <li>Must describe consideration exchanged</li>
                            <li>Must outline rights and obligations</li>
                            <li>Must be signed by authorized parties</li>
                          </>
                        )}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="item-2">
                    <AccordionTrigger>Formatting Guidelines</AccordionTrigger>
                    <AccordionContent>
                      <ul className="text-sm space-y-1 list-disc pl-4">
                        <li>Use clear, simple language</li>
                        <li>Number all paragraphs and sections</li>
                        <li>Define key terms in the document</li>
                        <li>Use consistent formatting</li>
                        <li>Include proper signature blocks</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="item-3">
                    <AccordionTrigger>Best Practices</AccordionTrigger>
                    <AccordionContent>
                      <ul className="text-sm space-y-1 list-disc pl-4">
                        <li>Review document with all parties</li>
                        <li>Consult with legal counsel when needed</li>
                        <li>Keep copies for all signing parties</li>
                        <li>Document any amendments in writing</li>
                        <li>Include date of execution</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <Badge className="mb-2 bg-green-100 text-green-800 border-0">
                    <Check className="h-3.5 w-3.5 mr-1" /> Generated
                    Successfully
                  </Badge>
                  <CardTitle>{form.getValues().title}</CardTitle>
                  <CardDescription>
                    {form.getValues().description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md p-4 bg-muted/20 whitespace-pre-wrap font-mono text-sm">
                <textarea
                  className="w-full h-64 bg-transparent border-none focus:outline-none resize-none"
                  value={editedContent || generatedForm || ''}
                  onChange={(e) => handleContentChange(e.target.value)}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentStep(0)}>
                Back to Editor
              </Button>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    // Create a blob with the form content and download it
                    const blob = new Blob([generatedForm || ''], {
                      type: 'text/plain',
                    });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${form.getValues().title.replace(/\s+/g, '_')}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button onClick={handleSaveChanges} disabled={isSaving}>
                  {isSaving ? 'Saving...' : 'Save Form'}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  );
}

'use client';

import { SidebarMenuButton } from '@/components/ui/sidebar';
import { PulseLink } from '@/components/ui/pulse-link';
import React from 'react';

interface SidebarMenuPulseLinkProps {
  href: string;
  icon: React.ElementType;
  name: string;
}

export function SidebarMenuPulseLink({
  href,
  icon: Icon,
  name,
}: SidebarMenuPulseLinkProps) {
  return (
    <SidebarMenuButton asChild>
      <PulseLink href={href}>
        <Icon className="size-4" />
        <span>{name}</span>
      </PulseLink>
    </SidebarMenuButton>
  );
}

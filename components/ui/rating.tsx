'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

// SVG Icons for Rating
export function SVGStarFill(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M18.75 7.69221L12.5925 6.95272L9.99671 1.25L7.40096 6.95272L1.25 7.69221L5.7975 11.9626L4.59491 18.125L9.99671 15.0538L15.4051 18.125L14.2025 11.9626L18.75 7.69221Z'
        fill='currentColor'
      />
    </svg>
  );
}
 
export function SVGStarHalf(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M18.75 7.69476L12.5925 6.95497L9.99671 1.25L7.40096 6.95497L1.25 7.69476L5.7975 11.9602L4.58834 18.125L9.99671 15.0526L15.4051 18.125L14.1959 11.9602L18.7434 7.69476H18.75ZM10.0033 13.533V4.43572L11.7119 8.19461L15.7665 8.68113L12.7699 11.4936L13.5651 15.5524L10.0033 13.533Z'
        fill='currentColor'
      />
    </svg>
  );
}
 
export function SVGStarLine(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M15.4117 18.125L10.0033 15.0526L4.59491 18.125L5.80407 11.9602L1.25 7.69476L7.40096 6.95498L9.99671 1.25L12.5925 6.95498L18.75 7.69476L14.2025 11.9602L15.4117 18.125ZM10.0033 13.5264L13.5651 15.5458L12.7699 11.487L15.7665 8.67447L11.7119 8.18794L10.0033 4.42906L8.29469 8.18794L4.24005 8.67447L7.23667 11.487L6.44151 15.5458L10.0033 13.5264Z'
        fill='currentColor'
      />
    </svg>
  );
}

interface StarRatingProps extends React.HTMLAttributes<HTMLDivElement> {
  rating: number;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  interactive?: boolean;
  onRatingChange?: (rating: number) => void;
}

export function StarRating({
  rating,
  maxRating = 5,
  size = 'md',
  color = 'text-yellow-500',
  interactive = false,
  onRatingChange,
  className,
  ...props
}: StarRatingProps) {
  const [hoverRating, setHoverRating] = React.useState(0);

  const sizeClass = {
    sm: 'size-4',
    md: 'size-5',
    lg: 'size-6',
  };

  const getStarIcon = (i: number) => {
    const currentRating = hoverRating > 0 ? hoverRating : rating;
    
    if (currentRating >= i + 1) {
      return <SVGStarFill className={cn(sizeClass[size], color)} key={i} />;
    } else if (currentRating >= i + 0.5) {
      return <SVGStarHalf className={cn(sizeClass[size], color)} key={i} />;
    }
    return <SVGStarLine className={cn(sizeClass[size], 'text-neutral-300')} key={i} />;
  };

  const handleClick = (index: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(index + 1);
    }
  };

  const handleMouseEnter = (index: number) => {
    if (interactive) {
      setHoverRating(index + 1);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(0);
    }
  };

  return (
    <div 
      className={cn('flex gap-0.5', interactive && 'cursor-pointer', className)} 
      {...props}
    >
      {Array.from({ length: maxRating }, (_, i) => (
        <span 
          key={i}
          onClick={() => handleClick(i)}
          onMouseEnter={() => handleMouseEnter(i)}
          onMouseLeave={handleMouseLeave}
        >
          {getStarIcon(i)}
        </span>
      ))}
    </div>
  );
}

interface RatingDisplayProps extends React.HTMLAttributes<HTMLDivElement> {
  rating: number;
  reviewCount?: number;
  showCount?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function RatingDisplay({
  rating,
  reviewCount,
  showCount = true,
  size = 'md',
  className,
  ...props
}: RatingDisplayProps) {
  return (
    <div className={cn('flex items-center gap-1', className)} {...props}>
      <StarRating rating={rating} size={size} />
      {showCount && (
        <span className="text-sm text-neutral-500">
          {rating.toFixed(1)} {reviewCount !== undefined && `· ${reviewCount} ${reviewCount === 1 ? 'review' : 'reviews'}`}
        </span>
      )}
    </div>
  );
}

interface RatingInputProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  value: number;
  onChange: (value: number) => void;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  label?: string;
}

export function RatingInput({
  value,
  onChange,
  size = 'md',
  color = 'text-yellow-500',
  label,
  className,
  ...props
}: RatingInputProps) {
  return (
    <div className={cn('flex flex-col gap-1', className)} {...props}>
      {label && <span className="text-sm font-medium">{label}</span>}
      <StarRating 
        rating={value} 
        size={size} 
        color={color} 
        interactive={true} 
        onRatingChange={onChange} 
      />
    </div>
  );
}

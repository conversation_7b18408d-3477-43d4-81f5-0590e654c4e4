'use client';

import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useLinkStatus } from 'next/link';
import React from 'react';

interface PulseLinkProps extends React.ComponentPropsWithoutRef<typeof Link> {
  children: React.ReactNode;
  className?: string;
}

export function PulseLink({ children, className, ...props }: PulseLinkProps) {
  const { pending } = useLinkStatus();

  return (
    <Link
      className={cn(
        pending ? 'animate-pulse-link' : '',
        className
      )}
      {...props}
    >
      {children}
    </Link>
  );
}

'use client';

import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface UserAvatarProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  userId?: string;
  avatarUrl?: string | null;
  fallbackText?: string;
}

export function UserAvatar({
  size = 'md',
  className,
  userId,
  avatarUrl,
  fallbackText,
}: UserAvatarProps) {
  const { profile } = userStore();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [error, setError] = useState(false);

  // Size mapping
  const sizeMap = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  // Generate initials for fallback
  const getInitials = () => {
    const name = fallbackText || 'User';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Determine the avatar URL
  useEffect(() => {
    // Reset error state when URL changes
    setError(false);

    // Use the provided avatarUrl or generate a fallback
    if (avatarUrl) {
      setImageUrl(avatarUrl);
    } else {
      // Generate UI Avatars URL as fallback
      const name = fallbackText || 'User';
      const uiAvatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
        name
      )}&background=0D8ABC&color=fff&size=150`;
      setImageUrl(uiAvatarUrl);
    }
  }, [avatarUrl, fallbackText]);

  return (
    <Avatar className={cn(sizeMap[size], className)}>
      {imageUrl && !error ? (
        <AvatarImage
          src={imageUrl}
          alt="User avatar"
          onError={() => setError(true)}
        />
      ) : (
        <AvatarFallback className="bg-primary text-primary-foreground">
          {getInitials()}
        </AvatarFallback>
      )}
    </Avatar>
  );
}

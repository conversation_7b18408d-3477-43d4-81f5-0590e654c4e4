export function StrippedBg() {
  return (
    <svg
      className='absolute inset-0 size-full [mask-image:linear-gradient(transparent,white_10rem)]'
      // style={{
      //   maskImage:
      //     "linear-gradient(transparent, white 20rem, white calc(100% - 20rem), transparent)",
      // }}
    >
      <defs>
        <pattern
          id='diagonal-feature-pattern'
          patternUnits='userSpaceOnUse'
          width='64'
          height='64'
        >
          {Array.from({ length: 17 }, (_, i) => {
            const offset = i * 8;
            return (
              <path
                key={i}
                d={`M${-106 + offset} 110L${22 + offset} -18`}
                className='stroke-gray-200/70'
                strokeWidth='1'
              />
            );
          })}
        </pattern>
      </defs>
      <rect width='100%' height='100%' fill='url(#diagonal-feature-pattern)' />
    </svg>
  );
}

export function SBg() {
  return (
    <svg
      className='absolute inset-0 size-full'
      // style={{
      //   maskImage:
      //     "linear-gradient(transparent, white 20rem, white calc(100% - 20rem), transparent)",
      // }}
    >
      <defs>
        <pattern
          id='diagonal-feature-pattern'
          patternUnits='userSpaceOnUse'
          width='64'
          height='64'
        >
          {Array.from({ length: 17 }, (_, i) => {
            const offset = i * 8;
            return (
              <path
                key={i}
                d={`M${-106 + offset} 110L${22 + offset} -18`}
                className='stroke-gray-200/70'
                strokeWidth='1'
              />
            );
          })}
        </pattern>
      </defs>
      <rect width='100%' height='100%' fill='url(#diagonal-feature-pattern)' />
    </svg>
  );
}

type PositionedStrippedBgProps = {
  side: 'left' | 'right';
  offset?: number;
  width?: number;
};

export function PositionedStrippedBg({
  side,
  offset = 0,
  width = 18.75,
}: PositionedStrippedBgProps) {
  const offsetValue = `${offset}rem`;
  const widthValue = `${width}rem`;

  return (
    <svg
      className={`absolute inset-0`}
      style={{
        [side]: offsetValue,
        width: widthValue,
      }}
    >
      <defs>
        <pattern
          id='diagonal-feature-pattern'
          patternUnits='userSpaceOnUse'
          width='64'
          height='64'
        >
          {Array.from({ length: 17 }, (_, i) => {
            const offset = i * 8;
            return (
              <path
                key={i}
                d={`M${-106 + offset} 110L${22 + offset} -18`}
                className='stroke-gray-200/70'
                strokeWidth='1'
              />
            );
          })}
        </pattern>
      </defs>
      <rect width='100%' height='100%' fill='url(#diagonal-feature-pattern)' />
    </svg>
  );
}

import { cn } from '@/lib/utils';
import { ChevronDownIcon } from 'lucide-react';
import * as React from 'react';

const SelectNative = ({
  className,
  children,
  ...props
}: React.ComponentProps<'select'>) => {
  return (
    <div className="relative flex">
      <select
        data-slot="select-native"
        className={cn(
          'peer border-neutral-200 text-neutral-950 focus-visible:border-neutral-950 focus-visible:ring-neutral-950/50 has-[option[disabled]:checked]:text-neutral-500 aria-invalid:ring-red-500/20 dark:aria-invalid:ring-red-500/40 aria-invalid:border-red-500 inline-flex w-full cursor-pointer appearance-none items-center rounded-md border text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-neutral-800 dark:text-neutral-50 dark:focus-visible:border-neutral-300 dark:focus-visible:ring-neutral-300/50 dark:has-[option[disabled]:checked]:text-neutral-400 dark:aria-invalid:border-red-900',
          props.multiple
            ? '[&_option:checked]:bg-neutral-100 py-1 *:px-3 *:py-1 dark:[&_option:checked]:bg-neutral-800'
            : 'h-9 ps-3 pe-8',
          className
        )}
        {...props}
      >
        {children}
      </select>
      {!props.multiple && (
        <span className="text-neutral-500/80 peer-aria-invalid:text-red-500/80 pointer-events-none absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center peer-disabled:opacity-50 dark:text-neutral-400/80 dark:peer-aria-invalid:text-red-900/80">
          <ChevronDownIcon size={16} aria-hidden="true" />
        </span>
      )}
    </div>
  );
};

export { SelectNative };

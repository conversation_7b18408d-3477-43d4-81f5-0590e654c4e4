'use client';

import { cn } from '@/lib/utils/';
import * as React from 'react';

// Root component
const Root = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
    children?: React.ReactNode;
  }
>(({ className, children, ...props }, ref) => {
  return (
    <div className="relative">
      <textarea
        className={cn(
          'flex min-h-[80px] w-full items-center rounded-md border border-gray-200 bg-white px-3 py-2 text-base ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-accent-50 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-800 dark:bg-gray-950 dark:ring-offset-gray-950 dark:placeholder:text-gray-400 dark:focus-visible:ring-gray-300',
          className
        )}
        ref={ref}
        {...props}
      />
      {children && (
        <div className="absolute bottom-1 right-2 text-xs text-muted-foreground">
          {children}
        </div>
      )}
    </div>
  );
});
Root.displayName = 'TextareaRoot';

// CharCounter component
interface CharCounterProps {
  current: number;
  max: number;
  className?: string;
}

const CharCounter = ({ current, max, className }: CharCounterProps) => {
  const isOverLimit = current > max;

  return (
    <span
      className={cn(
        'text-xs',
        isOverLimit ? 'text-destructive' : 'text-muted-foreground',
        className
      )}
    >
      {current}/{max}
    </span>
  );
};
CharCounter.displayName = 'TextareaCharCounter';

// Legacy Textarea component for backward compatibility
const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement>
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        'flex min-h-[80px] w-full items-center rounded-md border border-gray-200 bg-white px-3 py-2 text-base ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-accent-50 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-800 dark:bg-gray-950 dark:ring-offset-gray-950 dark:placeholder:text-gray-400 dark:focus-visible:ring-gray-300',
        className
      )}
      ref={ref}
      {...props}
    />
  );
});
Textarea.displayName = 'Textarea';

export { CharCounter, Root, Textarea };

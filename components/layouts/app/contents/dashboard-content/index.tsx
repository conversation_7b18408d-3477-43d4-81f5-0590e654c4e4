'use client';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { SidebarMenuPulseLink } from '@/components/ui/sidebar-menu-pulse-link';
import { Layers2, Users2 } from '@/components/ux/icons';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import {
  Building2,
  Cog,
  FileText,
  Gavel,
  MessageSquare,
  Network,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import { usePathname } from 'next/navigation';

interface DashboardContentProps {
  direction?: number;
}

export default function DashboardContent({
  direction = 0,
}: DashboardContentProps) {
  const { profile } = userStore();
  const pathname = usePathname();

  // Define navigation items based on user role
  const standardUserNavigation = [
    {
      section: 'Workspace',
      pages: [
        {
          name: 'Dashboard',
          url: `/${profile?.username}`,
          icon: Layers2,
          active: pathname === `/${profile?.username}`,
        },
        {
          name: 'Documents',
          url: `/${profile?.username}/documents`,
          icon: FileText,
          active:
            pathname.includes('/documents') && !pathname.includes('/lawyer'),
        },
        {
          name: 'Lawyer Support',
          url: `/${profile?.username}/lawyer`,
          icon: Gavel,
          active: pathname.includes('/lawyer'),
        },
        {
          name: 'Collaboration Hub',
          url: `/${profile?.username}/collaboration`,
          icon: Users2,
          active: pathname.includes('/collaboration'),
        },
        {
          name: 'Organizations',
          url: `/${profile?.username}/organizations`,
          icon: Building2,
          active: pathname.includes('/organizations'),
        },
      ],
    },
  ];

  const lawyerUserNavigation = [
    {
      section: 'Workspace',
      pages: [
        {
          name: 'Dashboard',
          url: `/${profile?.username}`,
          icon: Layers2,
          active: pathname === `/${profile?.username}`,
        },
        {
          name: 'Lawyer Dashboard',
          url: `/${profile?.username}/lawyer/dashboard`,
          icon: Gavel,
          active: pathname.includes('/lawyer/dashboard'),
        },
        {
          name: 'Documents',
          url: `/${profile?.username}/documents`,
          icon: FileText,
          active:
            pathname.includes('/documents') && !pathname.includes('/lawyer'),
        },
        {
          name: 'Client Management',
          url: `/${profile?.username}/lawyer/clients`,
          icon: Users2,
          active: pathname.includes('/lawyer/clients'),
        },
        {
          name: 'Organizations',
          url: `/${profile?.username}/organizations`,
          icon: Building2,
          active: pathname.includes('/organizations'),
        },
      ],
    },
  ];

  // Select navigation based on user role
  const navigationData = [
    ...(profile?.user_role === 'lawyer'
      ? lawyerUserNavigation
      : standardUserNavigation),
    {
      section: 'Settings',
      pages: [
        {
          name: 'Settings',
          url: `/${profile?.username}/settings`,
          icon: Cog,
          active: pathname.includes('/settings'),
        },
      ],
    },
  ];

  return (
    <motion.div
      key="dashboard"
      initial={{
        opacity: 0,
        filter: 'blur(4px)',
        x: direction * 20,
      }}
      animate={{
        opacity: 1,
        filter: 'blur(0px)',
        x: 0,
      }}
      exit={{
        opacity: 0,
        filter: 'blur(4px)',
        x: -20,
      }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}
    >
      {navigationData.map((section) => (
        <SidebarGroup
          key={section.section}
          className="group-data-[collapsible=icon]:hidden"
        >
          <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
            {section.section}
          </SidebarGroupLabel>
          <SidebarMenu>
            {section.pages.map((page) => (
              <SidebarMenuItem
                key={page.name}
                className={cn(
                  'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                  page.active
                    ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                    : ''
                )}
              >
                <SidebarMenuPulseLink
                  href={page.url}
                  icon={page.icon}
                  name={page.name}
                />
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      ))}
    </motion.div>
  );
}

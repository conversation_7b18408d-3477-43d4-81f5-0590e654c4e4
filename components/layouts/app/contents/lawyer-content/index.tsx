'use client';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import {
  Calendar,
  ChevronLeft,
  FileQuestion,
  Gavel,
  History,
  MessageSquare,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface LawyerContentProps {
  direction?: number;
}

export default function LawyerContent({ direction = 0 }: LawyerContentProps) {
  const { profile } = userStore();
  const [lawyerHovered, setLawyerHovered] = useState(false);
  const pathname = usePathname();

  // Standard user legal consultation group
  const standardUserItems = [
    {
      name: 'Find a Lawyer',
      url: `/${profile?.username}/lawyer/find`,
      icon: Gavel,
      active:
        pathname.includes(`/${profile?.username}/lawyer/find`) ||
        pathname === `/${profile?.username}/lawyer`,
    },
    {
      name: 'My Consultations',
      url: `/${profile?.username}/lawyer/consultations`,
      icon: History,
      active: pathname.includes(`/${profile?.username}/lawyer/consultations`),
    },
    {
      name: 'Book Consultation',
      url: `/${profile?.username}/lawyer/book`,
      icon: Calendar,
      active: pathname.includes(`/${profile?.username}/lawyer/book`),
    },
  ];

  // Lawyer dashboard items (only shown to lawyers)
  const lawyerDashboardItems = [
    {
      name: 'Lawyer Dashboard',
      url: `/${profile?.username}/lawyer/dashboard`,
      icon: Gavel,
      active: pathname === `/${profile?.username}/lawyer/dashboard`,
    },
    {
      name: 'Client Management',
      url: `/${profile?.username}/lawyer/clients`,
      icon: Users,
      active: pathname.includes(`/${profile?.username}/lawyer/clients`),
    },
    {
      name: 'Client Consultations',
      url: `/${profile?.username}/lawyer/dashboard/consultations`,
      icon: Calendar,
      active:
        pathname === `/${profile?.username}/lawyer/dashboard/consultations`,
    },
    {
      name: 'Document Reviews',
      url: `/${profile?.username}/lawyer/dashboard/documents`,
      icon: FileQuestion,
      active: pathname.includes(
        `/${profile?.username}/lawyer/dashboard/documents`
      ),
    },
    {
      name: 'Lawyer Profile',
      url: `/${profile?.username}/lawyer/dashboard/profile`,
      icon: Users,
      active: pathname === `/${profile?.username}/lawyer/dashboard/profile`,
    },
  ];

  // Communication group - only show active routes
  const communication = [
    {
      name: 'Messages',
      url: `/${profile?.username}/lawyer/consultations`,
      icon: MessageSquare,
      active: pathname.includes(`/${profile?.username}/lawyer/consultations`),
    },
  ];

  // Resources group - only show active routes
  const resources = [
    {
      name: 'Find a Lawyer',
      url: `/${profile?.username}/lawyer/find`,
      icon: Gavel,
      active: pathname.includes(`/${profile?.username}/lawyer/find`),
    },
    {
      name: 'Book Consultation',
      url: `/${profile?.username}/lawyer/book`,
      icon: Calendar,
      active: pathname.includes(`/${profile?.username}/lawyer/book`),
    },
  ];

  return (
    <motion.div
      key="lawyer"
      initial={{
        opacity: 0,
        filter: 'blur(4px)',
        x: direction * 20,
      }}
      animate={{
        opacity: 1,
        filter: 'blur(0px)',
        x: 0,
      }}
      exit={{
        opacity: 0,
        filter: 'blur(4px)',
        x: 20,
      }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}
      className="w-full overflow-hidden"
    >
      <div className="py-2 px-4 flex items-center gap-2 border-b h-10 border-dashed border-neutral-300/60">
        <Link
          href={`/${profile?.username}`}
          className={cn(
            'flex items-center gap-1 transition-all duration-200 ease-out',
            lawyerHovered ? 'text-neutral-600' : 'text-neutral-400'
          )}
          onMouseEnter={() => setLawyerHovered(true)}
          onMouseLeave={() => setLawyerHovered(false)}
        >
          <motion.span
            initial={{ x: 0 }}
            animate={{ x: lawyerHovered ? -3 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronLeft className="size-4" />
          </motion.span>
          <span className="text-sm font-medium uppercase">Lawyer Support</span>
        </Link>
      </div>
      {/* Show standard user items if not a lawyer */}
      {profile?.user_role !== 'lawyer' && (
        <SidebarGroup className="group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
            Lawyer Support
          </SidebarGroupLabel>
          <SidebarMenu>
            {standardUserItems.map((item) => (
              <SidebarMenuItem
                key={item.name}
                className={cn(
                  'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                  item.active
                    ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                    : ''
                )}
              >
                <SidebarMenuButton asChild>
                  <Link href={item.url}>
                    <item.icon className="size-4" />
                    <span>{item.name}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      )}

      {/* Only show lawyer dashboard items to lawyers */}
      {profile?.user_role === 'lawyer' && (
        <SidebarGroup className="group-data-[collapsible=icon]:hidden">
          <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
            Lawyer Dashboard
          </SidebarGroupLabel>
          <SidebarMenu>
            {lawyerDashboardItems.map((item) => (
              <SidebarMenuItem
                key={item.name}
                className={cn(
                  'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                  item.active
                    ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                    : ''
                )}
              >
                <SidebarMenuButton asChild>
                  <Link href={item.url}>
                    <item.icon className="size-4" />
                    <span>{item.name}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      )}

      <SidebarGroup className="group-data-[collapsible=icon]:hidden mt-4">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Communication
        </SidebarGroupLabel>
        <SidebarMenu>
          {communication.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuButton asChild>
                <Link href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.name}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden mt-4">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Resources
        </SidebarGroupLabel>
        <SidebarMenu>
          {resources.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuButton asChild>
                <Link href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.name}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>
    </motion.div>
  );
}

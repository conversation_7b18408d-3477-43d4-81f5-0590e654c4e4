'use client';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { SidebarMenuPulseLink } from '@/components/ui/sidebar-menu-pulse-link';
import { Folder, WindowChartLine } from '@/components/ux/icons';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import {
  ChevronLeft,
  FileCheck,
  FilePlus,
  FileText,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface DocumentsContentProps {
  direction?: number;
}

export default function DocumentsContent({
  direction = 0,
}: DocumentsContentProps) {
  const { profile } = userStore();
  const [documentsHovered, setDocumentsHovered] = useState(false);
  const pathname = usePathname();

  // Documents management group
  const documentsManagement = [
    {
      name: 'All Documents',
      url: `/${profile?.username}/documents`,
      icon: FileText,
      active: pathname === `/${profile?.username}/documents`,
    },
  ];

  // Document actions group
  const documentActions = [
    {
      name: 'Create Document',
      url: `/${profile?.username}/documents/new`,
      icon: FilePlus,
      active: pathname.includes(`/${profile?.username}/documents/new`),
    },
    {
      name: 'Templates',
      url: `/${profile?.username}/documents/templates`,
      icon: FileCheck,
      active: pathname.includes(`/${profile?.username}/documents/templates`),
    },
  ];

  // Organization group
  const organization = [
    {
      name: 'Analytics',
      url: `/${profile?.username}/documents/analytics`,
      icon: WindowChartLine,
      active: pathname.includes(`/${profile?.username}/documents/analytics`),
    },
    {
      name: 'Folders',
      url: `/${profile?.username}/documents/organize`,
      icon: Folder,
      active:
        pathname.includes(`/${profile?.username}/documents/organize`) &&
        !pathname.includes('tab=tags'),
    },
  ];

  return (
    <motion.div
      key="documents"
      initial={{
        opacity: 0,
        filter: 'blur(4px)',
        x: direction * 20,
      }}
      animate={{
        opacity: 1,
        filter: 'blur(0px)',
        x: 0,
      }}
      exit={{
        opacity: 0,
        filter: 'blur(4px)',
        x: 20,
      }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}
      className="w-full overflow-hidden"
    >
      <div className="py-2 px-4 flex items-center gap-2 border-b h-10 border-dashed border-neutral-300/60">
        <Link
          href={`/${profile?.username}`}
          className={cn(
            'flex items-center gap-1 transition-all duration-200 ease-out',
            documentsHovered ? 'text-neutral-600' : 'text-neutral-400'
          )}
          onMouseEnter={() => setDocumentsHovered(true)}
          onMouseLeave={() => setDocumentsHovered(false)}
        >
          <motion.span
            initial={{ x: 0 }}
            animate={{ x: documentsHovered ? -3 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronLeft className="size-4" />
          </motion.span>
          <span className="text-sm font-medium uppercase">Documents</span>
        </Link>
      </div>
      <SidebarGroup className="group-data-[collapsible=icon]:hidden">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Documents
        </SidebarGroupLabel>
        <SidebarMenu>
          {documentsManagement.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuPulseLink
                href={item.url}
                icon={item.icon}
                name={item.name}
              />
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden mt-4">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Actions
        </SidebarGroupLabel>
        <SidebarMenu>
          {documentActions.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 text-neutral-500 hover:text-accent-100 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuPulseLink
                href={item.url}
                icon={item.icon}
                name={item.name}
              />
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden mt-4">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Organization
        </SidebarGroupLabel>
        <SidebarMenu>
          {organization.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 text-neutral-500 hover:text-accent-100 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuPulseLink
                href={item.url}
                icon={item.icon}
                name={item.name}
              />
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>
    </motion.div>
  );
}

'use client';
import React from 'react';

import { But<PERSON>, buttonVariants } from '@/components/ui/button';
import { BlurIn } from '@/components/ux/animations/blur-in';
import { AVATARS } from '@/lib/constants/avatars';
import { userStore } from '@/lib/store/user';
import { authService } from '@/lib/supabase/auth/auth-service';
import { Database } from '@/lib/supabase/database-types';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

type userPlan = 'Free' | 'Pro' | 'Enterprice';
type profileTypes = Database['public']['Tables']['profiles']['Row'];
type NavMenuProps = {
  handleClose: () => void;
  setAccountOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleMenu: () => void;
  // handlePricing: () => void;
  className?: string;
};

export function AccountMenu({
  setAccountOpen,
  handleClose,
  handleMenu,
  // handlePricing,
  className,
}: NavMenuProps) {
  const router = useRouter();
  const { user, profile, removeProfile, removeUser } = userStore();
  // const [profile, setProfile] = useState<profileTypes>();

  // useEffect(() => {
  //   async function getProfile() {
  //     await supabaseClient.auth.getUser().then(async ({ data: { user } }) => {
  //       await supabaseClient
  //         .from('profiles')
  //         .select('*')
  //         .eq('id', user?.id!)
  //         .single()
  //         .then(({ data, error }) => {
  //           if (!error) {
  //             setProfile(data);
  //           }
  //         });
  //     });
  //   }

  //   getProfile();
  // }, []);

  function handleLogOut() {
    return new Promise(async (resolve, reject) => {
      try {
        await authService.signOut();
        removeProfile(null);
        removeUser(null);
        resolve('Done');
        handleClose();
        router.refresh();
      } catch (error) {
        reject(error);
      }
    });
  }

  const links = [
    {
      id: 1,
      content: (
        <Link
          href={`/${profile?.username}/account/edit-profile`}
          onClick={() => setAccountOpen((s) => !s)}
          className={cn(buttonVariants({ variant: 'shadow', size: 'default' }))}
        >
          Edit Details
        </Link>
      ),
    },
    {
      id: 2,
      content: (
        <Link
          href={`/${profile?.username}/account/payments`}
          onClick={() => setAccountOpen((s) => !s)}
          className={cn(buttonVariants({ variant: 'shadow', size: 'default' }))}
        >
          Payment Details
        </Link>
      ),
    },
    {
      id: 3,
      content: (
        <Button
          variant={'shadow_red'}
          className="w-full"
          onClick={() =>
            toast.promise(handleLogOut(), {
              loading: 'Logging Out',
              success: (data: any) => 'Logged Out',
              error: (err: any) => `Error: ${err.message}`,
            })
          }
        >
          Log Out
        </Button>
      ),
    },
  ];

  // const PlanBtn = ({ plan = 'Free' }: { plan: userPlan }) => {
  //   if (plan === 'Free') {
  //     return (
  //       <Button
  //         variant={'shadow_green'}
  //         size={'sm'}
  //         icon={'right'}
  //         className='items-start space-x-1 py-1 pr-1'
  //         // onClick={handlePricing}
  //       >
  //         <span className='flex h-full w-full items-center'>Free</span>
  //         <span
  //           className={cn(
  //             buttonVariants({ variant: 'shadow', size: 'icon' }),
  //             'flex h-5 w-8 items-center justify-center rounded-full bg-white'
  //           )}
  //         >
  //           <ArrowUp className='size-3 text-green-900' />
  //         </span>
  //       </Button>
  //     );
  //   } else if (plan === 'Pro') {
  //     return (
  //       <Button
  //         variant={'shadow_green'}
  //         size={'sm'}
  //         icon={'right'}
  //         className='items-start space-x-1 py-1 pr-1'
  //         // onClick={handlePricing}
  //       >
  //         <span className='flex h-full w-full items-center'>Pro</span>
  //         <span
  //           className={cn(
  //             buttonVariants({ variant: 'shadow', size: 'icon' }),
  //             'flex h-5 w-8 items-center justify-center rounded-full bg-white'
  //           )}
  //         >
  //           <ArrowUp className='size-3 text-green-900' />
  //         </span>
  //       </Button>
  //     );
  //   } else if (plan === 'Enterprice') {
  //     return (
  //       <Button variant={'shadow_green'} size={'sm'} className=''>
  //         <span className='flex h-full w-full items-center'>Enterprice</span>
  //       </Button>
  //     );
  //   } else {
  //     return null;
  //   }
  // };

  return (
    <BlurIn
      duration={0.3}
      className={cn(
        className,
        'relative flex h-[420px] w-[220px] flex-col items-center justify-end'
      )}
    >
      <div className="flex w-full items-center justify-between p-2">
        <div className="flex items-center space-x-4">
          <Button
            variant={'shadow'}
            size={'sm'}
            className=""
            onClick={handleMenu}
          >
            Back
          </Button>
        </div>
      </div>
      <div className="flex h-full w-full flex-col">
        <div className="flex h-full w-full pb-4 flex-1 flex-col items-center justify-between space-y-4">
          <div className="flex w-full flex-col items-center space-y-2">
            <div className="w-24 overflow-hidden rounded-full">
              <Image alt="user_img" src={AVATARS['male']['M1']} />
            </div>
          </div>
          <div className="flex w-full flex-col items-center space-y-2">
            <h2 className="text-neutral-400">{profile?.email}</h2>
            <h2>
              <span className="text-accent-200">{profile?.username}</span> |{' '}
              {profile?.full_name}
            </h2>
          </div>
          <div className="flex w-full flex-col items-center space-y-3">
            {links.map((m) => (
              <div key={m.id}>{m.content}</div>
            ))}
          </div>
        </div>
      </div>
    </BlurIn>
  );
}

'use client';

import { SidebarTrigger } from '@/components/ui/sidebar';
import NotificationsWrapper from '../notifications-wrapper';

export default function HeaderNav() {
  return (
    <div className="w-full flex justify-between items-center border-b py-1.5 px-2 h-12">
      <div className="flex items-center gap-2">
        <SidebarTrigger className="" />
        <div className="flex items-center gap-1">
          <span className="text-sm font-medium">Dashboard</span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <NotificationsWrapper />
      </div>
    </div>
  );
}

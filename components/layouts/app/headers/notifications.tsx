'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
// Dropdown menu no longer needed
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useNotifications } from '@/lib/hooks';
import { Notification } from '@/lib/types/database-modules';
import { formatDistanceToNow } from 'date-fns';
import {
  Bell,
  Check,
  FileText,
  MessageSquare,
  Network,
  RefreshCw,
  Trash,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

// Extended notification type with transitioning flag
interface ExtendedNotification extends Notification {
  _transitioning?: boolean;
}

// Notification skeleton component for loading state
function NotificationSkeleton({ isUnread = false }: { isUnread?: boolean }) {
  return (
    <div className={`p-4 hover:bg-muted/50 ${isUnread ? 'bg-muted/20' : ''}`}>
      <div className="flex gap-3">
        <div className="flex-shrink-0 mt-1">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 rounded bg-muted animate-pulse"></div>
            <div className="h-4 w-4 rounded-full bg-muted animate-pulse"></div>
          </div>
        </div>
        <div className="flex-1 space-y-2">
          <div className="flex items-start justify-between gap-2">
            <div className="h-4 w-32 bg-muted rounded animate-pulse"></div>
            <div className="h-2 w-2 rounded-full bg-blue-200 animate-pulse"></div>
          </div>
          <div className="h-3 w-full bg-muted rounded animate-pulse"></div>
          <div className="h-3 w-16 bg-muted rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}

export default function Notifications() {
  const {
    notifications: rawNotifications,
    setNotifications: setRawNotifications, // Add setNotifications for direct UI updates
    unreadCount,
    fetchNotifications,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    deleteAllNotifications,
    error,
    loading,
  } = useNotifications();

  // Cast notifications to ExtendedNotification type
  const notifications = rawNotifications as ExtendedNotification[];
  const setNotifications = setRawNotifications as React.Dispatch<
    React.SetStateAction<ExtendedNotification[]>
  >;

  const [isOpen, setIsOpen] = useState(false);
  // No longer using selected notifications state
  const [hasNewNotifications, setHasNewNotifications] = useState(false);

  // Track previous unread count to detect new notifications
  const prevUnreadCountRef = useRef(unreadCount);

  // No need to fetch on component mount - the hook already does this

  // Effect to detect new notifications
  useEffect(() => {
    // If unread count increased, we have new notifications
    if (unreadCount > prevUnreadCountRef.current) {
      setHasNewNotifications(true);
    }

    prevUnreadCountRef.current = unreadCount;
  }, [unreadCount]);

  // Get icon based on notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'project':
        return <Network className="h-4 w-4 text-purple-500" />;
      case 'comment':
        return <MessageSquare className="h-4 w-4 text-green-500" />;
      default:
        return <Bell className="h-4 w-4 text-orange-500" />;
    }
  };

  // Format the notification time
  const formatNotificationTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'recently';
    }
  };

  return (
    <Popover
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (open) {
          fetchNotifications();
          // Reset new notifications flag when opening the popover
          setHasNewNotifications(false);
        }
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 relative"
          aria-label="Notifications"
        >
          <Bell
            className={`h-4 w-4 ${hasNewNotifications ? 'text-primary animate-pulse' : ''}`}
          />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-[10px]"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[350px] p-0" align="end">
        <div className="flex items-center justify-between px-4 py-2 border-b">
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-medium">Notifications</h3>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              disabled={loading}
              onClick={() => {
                fetchNotifications();
                toast.success('Notifications refreshed');
              }}
            >
              <RefreshCw
                className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`}
              />
            </Button>
          </div>
          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 text-xs"
                onClick={() => {
                  // First update the local state to show all notifications as read
                  // but keep them visible with the transitioning flag
                  setNotifications((prev) =>
                    prev.map((n) => ({
                      ...n,
                      read: true,
                      // Add transitioning flag only to previously unread notifications
                      _transitioning: !n.read ? true : n._transitioning,
                    }))
                  );

                  // Delay the database update to allow the animation to be visible
                  setTimeout(() => {
                    // Then mark all as read in the database
                    markAllAsRead();

                    // Fetch notifications after a longer delay to allow seeing the checked state
                    setTimeout(() => fetchNotifications(), 1500);
                  }, 300);
                }}
              >
                <Check className="mr-1 h-3 w-3" />
                Mark all as read
              </Button>
            )}
          </div>
        </div>

        <Tabs defaultValue="unread" className="w-full">
          <TabsList className="w-full grid grid-cols-2 px-4 py-2">
            <TabsTrigger value="all" className="text-xs">
              All
            </TabsTrigger>
            <TabsTrigger value="unread" className="text-xs">
              Unread
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="focus:outline-none">
            <ScrollArea className="h-[300px]">
              {error ? (
                <div className="flex flex-col items-center justify-center h-[300px] text-center p-4">
                  <Bell className="h-8 w-8 text-red-500 mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Error loading notifications
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => fetchNotifications()}
                  >
                    Try again
                  </Button>
                </div>
              ) : loading ? (
                <div className="divide-y">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <NotificationSkeleton key={i} />
                  ))}
                </div>
              ) : notifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[300px] text-center p-4">
                  <Bell className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No notifications yet
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 hover:bg-muted/50 ${!notification.read ? 'bg-muted/20' : 'opacity-70'}`}
                    >
                      <div className="flex gap-3">
                        <div className="flex-shrink-0 mt-1">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id={`notification-${notification.id}`}
                              checked={notification.read}
                              onCheckedChange={(checked) => {
                                // Create a local copy of the notification with updated read status
                                const updatedNotification = {
                                  ...notification,
                                  read: checked === true,
                                };

                                // Update the local state immediately to show the checkbox animation
                                setNotifications((prev) =>
                                  prev.map((n) =>
                                    n.id === notification.id
                                      ? updatedNotification
                                      : n
                                  )
                                );

                                // Use setTimeout to delay the database update and allow the animation to be visible
                                setTimeout(() => {
                                  if (checked) {
                                    markAsRead(notification.id);
                                  } else {
                                    markAsUnread(notification.id);
                                  }

                                  // Fetch notifications after a longer delay
                                  setTimeout(() => fetchNotifications(), 1500);
                                }, 300); // Delay the database update to allow the animation to be visible
                              }}
                              className="mr-1"
                            />
                            {getNotificationIcon(notification.type)}
                          </div>
                        </div>
                        <div
                          className="flex-1 space-y-1 cursor-pointer"
                          onClick={() => {
                            if (notification.action_url) {
                              // If not read, mark as read first
                              if (!notification.read) {
                                // First update the local state to show the notification as read
                                const updatedNotification = {
                                  ...notification,
                                  read: true,
                                };

                                // Update the notifications state to show the notification as read immediately
                                setNotifications((prev) =>
                                  prev.map((n) =>
                                    n.id === notification.id
                                      ? updatedNotification
                                      : n
                                  )
                                );

                                // Delay the database update to allow the animation to be visible
                                setTimeout(() => {
                                  // Then mark as read in the database
                                  markAsRead(notification.id);

                                  // Navigate after a longer delay to allow seeing the read state
                                  setTimeout(() => {
                                    window.location.href =
                                      notification.action_url!;
                                  }, 800);
                                }, 300);
                              } else {
                                window.location.href = notification.action_url;
                              }
                            }
                          }}
                        >
                          <div className="flex items-start justify-between gap-2">
                            <p
                              className={`text-sm font-medium ${notification.read ? 'line-through' : ''}`}
                            >
                              {notification.title}
                            </p>
                            {!notification.read && (
                              <div className="h-2 w-2 rounded-full bg-blue-500 flex-shrink-0 mt-1"></div>
                            )}
                          </div>
                          <p
                            className={`text-xs text-muted-foreground ${notification.read ? 'line-through' : ''}`}
                          >
                            {notification.content}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatNotificationTime(notification.created_at)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="unread" className="focus:outline-none">
            <ScrollArea className="h-[300px]">
              {error ? (
                <div className="flex flex-col items-center justify-center h-[300px] text-center p-4">
                  <Bell className="h-8 w-8 text-red-500 mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Error loading notifications
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => fetchNotifications()}
                  >
                    Try again
                  </Button>
                </div>
              ) : loading ? (
                <div className="divide-y">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <NotificationSkeleton key={i} isUnread={true} />
                  ))}
                </div>
              ) : notifications.filter((n) => !n.read && !n._transitioning)
                  .length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[300px] text-center p-4">
                  <Check className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No unread notifications
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {notifications
                    .filter((n) => !n.read || n._transitioning)
                    .map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 hover:bg-muted/50 bg-muted/20 transition-all duration-500 ${
                          notification._transitioning
                            ? 'opacity-70 border-l-4 border-l-green-500'
                            : ''
                        }`}
                      >
                        <div className="flex gap-3">
                          <div className="flex-shrink-0 mt-1">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                id={`notification-unread-${notification.id}`}
                                checked={notification.read}
                                className={`mr-1 ${notification._transitioning ? 'opacity-100' : ''}`}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    // For marking as read, we need a special approach

                                    // 1. First update the local state to show the checkbox as checked
                                    // but DON'T remove it from the list yet
                                    const updatedNotification = {
                                      ...notification,
                                      read: true,
                                      // Add a flag to indicate this notification is in transition
                                      _transitioning: true,
                                    };

                                    // Update the notifications state to show the checkbox as checked
                                    setNotifications((prev) =>
                                      prev.map((n) =>
                                        n.id === notification.id
                                          ? updatedNotification
                                          : n
                                      )
                                    );

                                    // 2. After a delay to show the checked state, update the database
                                    setTimeout(() => {
                                      markAsRead(notification.id);

                                      // 3. After another delay, refresh the notifications
                                      // This gives time to see the checked state before it disappears
                                      setTimeout(
                                        () => fetchNotifications(),
                                        1500
                                      );
                                    }, 800);
                                  } else {
                                    // For marking as unread, use the normal approach
                                    const updatedNotification = {
                                      ...notification,
                                      read: false,
                                    };

                                    setNotifications((prev) =>
                                      prev.map((n) =>
                                        n.id === notification.id
                                          ? updatedNotification
                                          : n
                                      )
                                    );

                                    setTimeout(() => {
                                      markAsUnread(notification.id);
                                      setTimeout(
                                        () => fetchNotifications(),
                                        500
                                      );
                                    }, 300);
                                  }
                                }}
                              />
                              {getNotificationIcon(notification.type)}
                            </div>
                          </div>
                          <div
                            className="flex-1 space-y-1 cursor-pointer"
                            onClick={() => {
                              if (notification.action_url) {
                                // First update the local state to show the notification as read
                                // but keep it visible with the transitioning flag
                                const updatedNotification = {
                                  ...notification,
                                  read: true,
                                  _transitioning: true,
                                };

                                // Update the notifications state to show the notification as read immediately
                                setNotifications((prev) =>
                                  prev.map((n) =>
                                    n.id === notification.id
                                      ? updatedNotification
                                      : n
                                  )
                                );

                                // Delay the database update to allow the animation to be visible
                                setTimeout(() => {
                                  // Then mark as read in the database
                                  markAsRead(notification.id);

                                  // Navigate after a longer delay to allow seeing the read state
                                  setTimeout(() => {
                                    window.location.href =
                                      notification.action_url!;
                                  }, 800);
                                }, 300);
                              }
                            }}
                          >
                            <div className="flex items-start justify-between gap-2">
                              <p className="text-sm font-medium">
                                {notification.title}
                              </p>
                              <div className="h-2 w-2 rounded-full bg-blue-500 flex-shrink-0 mt-1"></div>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {notification.content}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatNotificationTime(notification.created_at)}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>

        <Separator />

        <div className="p-2 flex justify-between items-center">
          <Button
            variant="link"
            size="sm"
            className="text-xs h-8"
            onClick={() => (window.location.href = '/settings/notifications')}
          >
            Manage notification settings
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="text-xs h-8 text-destructive"
            onClick={() => {
              if (notifications.length === 0) {
                toast.info('No notifications to delete');
                return;
              }

              toast.custom(
                (t) => (
                  <div className="flex flex-col gap-2 p-4 bg-white border rounded-lg shadow-lg">
                    <div className="font-medium">Delete all notifications?</div>
                    <p className="text-sm text-muted-foreground">
                      This action cannot be undone.
                    </p>
                    <div className="flex gap-2 mt-2 justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.dismiss(t)}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="shadow_red"
                        size="sm"
                        onClick={() => {
                          deleteAllNotifications();
                          // Fetch notifications after a short delay to allow the update to complete
                          setTimeout(() => {
                            fetchNotifications();
                            toast.dismiss(t);
                          }, 500);
                        }}
                      >
                        Delete all
                      </Button>
                    </div>
                  </div>
                ),
                {
                  duration: 10000, // 10 seconds
                  position: 'bottom-right',
                }
              );
            }}
          >
            <Trash className="mr-1 h-3 w-3" />
            Delete all
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

'use client';

import { usePathname } from 'next/navigation';
import DashboardHeader from './dashboard/header';
import MembersHeader from './members/header';
import ProjectsHeader from './projects/header';
import TeamsHeader from './teams/header';

export default function Header() {
  const pathname = usePathname();

  // Extract path segments from URL to determine which header to show
  // The URL pattern is: /[username]/[page]
  const segments = pathname.split('/').filter(Boolean);

  // First segment should be the username
  // Second segment (if present) determines which page we're on
  const page = segments.length > 1 ? segments[1] : '';

  // Return the appropriate header based on the page
  switch (page) {
    case 'teams':
      return <TeamsHeader />;
    case 'projects':
      return <ProjectsHeader />;
    case 'members':
      return <MembersHeader />;
    default:
      // Default to dashboard header if no specific page matched or on root [username]/
      return <DashboardHeader />;
  }
}

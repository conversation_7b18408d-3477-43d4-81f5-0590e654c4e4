'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet } from '@/components/ui/sheet';
import { CheckIcon, StarIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

// Define plan types
export type PlanType = 'free' | 'pro' | 'business';

export interface Plan {
  id: PlanType;
  name: string;
  price: string;
  description: string;
  features: string[];
  popular?: boolean;
}

// Sample plan data
const plans: Plan[] = [
  {
    id: 'free',
    name: 'Free',
    price: '$0',
    description: 'For personal use and small projects',
    features: [
      'Up to 5 documents',
      'Basic templates',
      'Export to PDF',
      'Email support',
    ],
  },
  {
    id: 'pro',
    name: 'Pro',
    price: '$19/month',
    description: 'For professionals and growing businesses',
    features: [
      'Unlimited documents',
      'All templates',
      'Export to PDF, Word, and more',
      'Priority support',
      'Document collaboration',
      'Custom branding',
    ],
    popular: true,
  },
  {
    id: 'business',
    name: 'Business',
    price: '$49/month',
    description: 'For teams and organizations',
    features: [
      'Everything in Pro',
      'Team management',
      'Advanced security',
      'API access',
      'Dedicated account manager',
      'Custom templates',
      'Audit logs',
    ],
  },
];

interface PlanSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentPlan: PlanType;
  onUpgrade: (plan: PlanType) => void;
}

export function PlanSheet({
  open,
  onOpenChange,
  currentPlan,
  onUpgrade,
}: PlanSheetProps) {
  const [selectedPlan, setSelectedPlan] = useState<PlanType>(currentPlan);

  const handleUpgrade = () => {
    if (selectedPlan === currentPlan) {
      toast.info('You are already on this plan');
      return;
    }

    onUpgrade(selectedPlan);
    toast.success(`Successfully upgraded to ${selectedPlan} plan`);
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <div className="p-6 h-full flex flex-col">
        <div className="flex flex-col space-y-1.5 mb-6">
          <Sheet.Title>Subscription Plans</Sheet.Title>
          <p className="text-sm text-muted-foreground">
            Choose the plan that works best for you and your team
          </p>
        </div>

        <div className="flex-1 overflow-auto py-2 space-y-4">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative p-4 border rounded-lg cursor-pointer transition-all ${
                selectedPlan === plan.id
                  ? 'border-accent-300 bg-accent-50/20 ring-1 ring-accent-300'
                  : 'hover:border-accent-200'
              }`}
              onClick={() => setSelectedPlan(plan.id)}
            >
              {plan.popular && (
                <Badge className="absolute -top-2 -right-2 bg-accent-300">
                  <StarIcon className="mr-1 h-3 w-3" />
                  Popular
                </Badge>
              )}

              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-medium">{plan.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {plan.description}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-bold">{plan.price}</p>
                  {currentPlan === plan.id && (
                    <Badge variant="outline" className="mt-1">
                      Current Plan
                    </Badge>
                  )}
                </div>
              </div>

              <ul className="mt-4 space-y-2">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start text-sm">
                    <CheckIcon className="h-4 w-4 mr-2 text-accent-300 shrink-0 mt-0.5" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="flex flex-col sm:flex-row sm:justify-between gap-4 pt-6 mt-auto">
          <Sheet.Close asChild>
            <Button variant="outline">Cancel</Button>
          </Sheet.Close>
          <Button onClick={handleUpgrade}>
            {selectedPlan === currentPlan ? 'Current Plan' : 'Upgrade Plan'}
          </Button>
        </div>
      </div>
    </Sheet>
  );
}

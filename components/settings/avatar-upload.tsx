'use client';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { UserAvatar } from '@/components/ui/user-avatar';
import { userService } from '@/lib/services/userService';
import { userStore } from '@/lib/store/user';
import { Camera, RefreshCw, Upload } from 'lucide-react';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

export function AvatarUpload() {
  const { profile, updateProfile } = userStore();
  const [isUploading, setIsUploading] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if avatar is from a social provider
  const isSocialAvatar =
    profile?.avatar_url?.includes('googleusercontent.com') ||
    profile?.avatar_url?.includes('github') ||
    profile?.avatar_url?.includes('facebook') ||
    profile?.avatar_url?.includes('twitter');

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size should be less than 2MB');
      return;
    }

    setIsUploading(true);

    try {
      // Upload the file to Supabase storage
      const result = await userService.uploadAvatar(file, profile?.id || '');

      if (!result.success) {
        throw new Error(result.error || 'Failed to upload avatar');
      }

      // The userService.uploadAvatar already updates the profile
      if (result.success) {
        toast.success('Avatar updated successfully');

        // Update the local profile state
        if (profile) {
          updateProfile({
            ...profile,
            avatar_url: result.avatarUrl || null,
          });
        }
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating avatar:', error);
      toast.error('Failed to update avatar');
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleResetAvatar = async () => {
    setIsResetting(true);

    try {
      // Reset avatar by setting a mapped avatar (avatar-1 as default)
      const result = await userService.setMappedAvatar(
        'avatar-1',
        profile?.id || ''
      );

      if (result.success) {
        toast.success('Avatar reset to default');
      } else {
        throw new Error('Failed to reset avatar');
      }
    } catch (error) {
      console.error('Error resetting avatar:', error);
      toast.error('Failed to reset avatar');
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Picture</CardTitle>
        <CardDescription>
          Update your profile picture. This will be visible to other users.
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center space-y-4">
        <UserAvatar
          size="xl"
          avatarUrl={profile?.avatar_url}
          fallbackText={profile?.full_name || ''}
          className="border-2 border-primary"
        />

        <div className="text-sm text-muted-foreground">
          {isSocialAvatar ? (
            <p>This avatar is from your social login provider.</p>
          ) : (
            <p>Upload a square image for best results.</p>
          )}
        </div>

        <input
          type="file"
          accept="image/*"
          className="hidden"
          ref={fileInputRef}
          onChange={handleFileChange}
        />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleResetAvatar}
          disabled={isUploading || isResetting}
        >
          {isResetting ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Resetting...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset to Default
            </>
          )}
        </Button>

        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading || isResetting}
        >
          {isUploading ? (
            <>
              <Camera className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload New
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

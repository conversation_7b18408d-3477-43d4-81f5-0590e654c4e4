'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useLawyers } from '@/lib/hooks';
import { Lawyer } from '@/lib/types/database-modules';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Upload, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

interface LawyerProfileFormProps {
  lawyerProfile?: Lawyer | null;
  onProfileUpdated?: () => void;
  onPreviewClick?: () => void;
}

const lawyerProfileSchema = z.object({
  full_name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional().nullable(),
  specialization: z
    .array(z.string())
    .min(1, 'Select at least one specialization'),
  bio: z.string().min(10, 'Bio must be at least 10 characters'),
  years_experience: z.coerce.number().min(0, 'Experience cannot be negative'),
  hourly_rate: z.coerce.number().min(0, 'Hourly rate cannot be negative'),
  consultation_fee: z.coerce
    .number()
    .min(0, 'Consultation fee cannot be negative'),
  education: z.string().optional(),
  languages: z.array(z.string()).min(1, 'Select at least one language'),
});

type LawyerProfileFormValues = z.infer<typeof lawyerProfileSchema>;

const specializations = [
  'Contract Law',
  'Corporate Law',
  'Intellectual Property',
  'Real Estate Law',
  'Family Law',
  'Criminal Law',
  'Immigration Law',
  'Tax Law',
  'Employment Law',
  'Environmental Law',
  'Bankruptcy Law',
  'Personal Injury',
  'Estate Planning',
];

const languages = [
  'English',
  'Spanish',
  'French',
  'German',
  'Chinese',
  'Japanese',
  'Korean',
  'Arabic',
  'Russian',
  'Portuguese',
  'Italian',
];

export function LawyerProfileForm({
  lawyerProfile,
  onProfileUpdated,
  onPreviewClick,
}: LawyerProfileFormProps) {
  // Use the useLawyers hook for any lawyer-related operations
  const {} = useLawyers();
  const [uploading, setUploading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(
    lawyerProfile?.avatar_url || null
  );

  const form = useForm<LawyerProfileFormValues>({
    resolver: zodResolver(lawyerProfileSchema),
    defaultValues: {
      full_name: lawyerProfile?.full_name || '',
      email: lawyerProfile?.email || '',
      phone: (lawyerProfile as any)?.phone || '',
      specialization: Array.isArray(lawyerProfile?.specialization)
        ? [...lawyerProfile?.specialization]
        : ['Contract Law'],
      bio: lawyerProfile?.bio || '',
      years_experience: lawyerProfile?.years_experience ?? 0,
      hourly_rate: lawyerProfile?.hourly_rate ?? 0,
      consultation_fee: lawyerProfile?.consultation_fee ?? 0,
      education: lawyerProfile?.education || '',
      languages: Array.isArray(lawyerProfile?.languages)
        ? [...lawyerProfile?.languages]
        : ['English'],
    },
  });

  // Update avatar URL when lawyer profile changes
  useEffect(() => {
    if (lawyerProfile) {
      // Use avatar_url if available, otherwise use profile_image_url
      setAvatarUrl(lawyerProfile.avatar_url || lawyerProfile.profile_image_url);
    }
  }, [lawyerProfile]);

  // Handle avatar upload - redirects to profile settings
  const handleAvatarUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      // Reset the file input
      event.target.value = '';

      // Inform the user that they should update their avatar in profile settings
      toast.info(
        'Please update your avatar in profile settings. Lawyer profiles use the same avatar as your main profile.',
        {
          duration: 5000,
          action: {
            label: 'Go to Profile',
            onClick: () => {
              // Navigate to profile settings
              window.location.href = '/settings/profile';
            },
          },
        }
      );
    } catch (error: any) {
      console.error('Error handling avatar redirect:', error);
    } finally {
      setUploading(false);
    }
  };

  const onSubmit = async (values: LawyerProfileFormValues) => {
    if (!lawyerProfile?.id) {
      toast.error('Cannot update profile', {
        description: 'Lawyer profile not found. Please try again later.',
      });
      return;
    }

    // Only include fields that are known to exist in the database schema
    const formattedValues = {
      // Basic information
      full_name: values.full_name,
      email: values.email,
      bio: values.bio,
      education: values.education,

      // Arrays - ensure they are properly formatted
      specialization: Array.isArray(values.specialization)
        ? values.specialization
        : [],
      languages: Array.isArray(values.languages) ? values.languages : [],

      // Numbers - ensure they are properly formatted
      years_experience: values.years_experience
        ? Number(values.years_experience)
        : null,
      hourly_rate: values.hourly_rate ? Number(values.hourly_rate) : null,
      consultation_fee: values.consultation_fee
        ? Number(values.consultation_fee)
        : null,

      // Metadata
      profile_complete: true,
      updated_at: new Date().toISOString(),
    };

    // Only add phone if it's in the database schema
    // We'll check if the original lawyer profile had this field
    if (lawyerProfile && 'phone' in lawyerProfile) {
      // If the field exists in the database schema, include it
      (formattedValues as any).phone = values.phone || null;
    }

    // Create a promise for updating the lawyer profile
    const updatePromise = async () => {
      try {
        // Use the Supabase client directly through a custom function
        // This is a workaround since there's no updateLawyerProfile in the hook
        const { data, error } = await fetch(
          `/api/lawyers/${lawyerProfile.id}`,
          {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(formattedValues),
          }
        ).then((res) => res.json());

        if (error) {
          throw new Error(error.message || 'Failed to update profile');
        }

        if (onProfileUpdated) {
          onProfileUpdated();
        }

        return data;
      } catch (err) {
        console.error('Error in profile update:', err);
        if (err instanceof Error) {
          throw new Error(err.message);
        } else {
          throw new Error('An unknown error occurred');
        }
      }
    };

    // Use toast.promise for better user feedback
    toast.promise(updatePromise(), {
      loading: 'Updating your lawyer profile...',
      success: 'Profile updated successfully',
      error: (err) =>
        `Failed to update profile: ${err instanceof Error ? err.message : 'Please try again'}`,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Avatar upload section */}
        <div className="flex flex-col items-center gap-4 mb-6">
          <div className="relative">
            <UserAvatar
              fallbackText={lawyerProfile?.full_name || ''}
              avatarUrl={avatarUrl || lawyerProfile?.profile_image_url}
              size="xl"
            />
            {uploading && (
              <div className="absolute inset-0 bg-black/30 rounded-full flex items-center justify-center">
                <Loader2 className="h-5 w-5 text-white animate-spin" />
              </div>
            )}
          </div>
          <div>
            <label htmlFor="avatar-upload" className="cursor-pointer">
              <div className="flex items-center gap-2 px-3 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-md transition-colors">
                <Upload className="h-4 w-4" />
                <span className="text-sm font-medium">Upload Photo</span>
              </div>
              <input
                id="avatar-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleAvatarUpload}
                disabled={uploading}
              />
            </label>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="John Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input
                    placeholder="+****************"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="education"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Education</FormLabel>
                <FormControl>
                  <Input placeholder="Harvard Law School" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="years_experience"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Years of Experience</FormLabel>
                <FormControl>
                  <Input type="number" min="0" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="hourly_rate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Hourly Rate ($)</FormLabel>
                <FormControl>
                  <Input type="number" min="0" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="consultation_fee"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Consultation Fee ($)</FormLabel>
                <FormControl>
                  <Input type="number" min="0" {...field} />
                </FormControl>
                <FormDescription>
                  Fee for initial consultation (if different from hourly rate)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="specialization"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Specializations</FormLabel>
                <div className="flex flex-wrap gap-2 mb-2">
                  {field.value.map((spec) => (
                    <Badge key={spec} variant="secondary" className="gap-1">
                      {spec}
                      <button
                        type="button"
                        className="ml-1 rounded-full outline-none"
                        onClick={() => {
                          const newValue = field.value.filter(
                            (s) => s !== spec
                          );
                          field.onChange(newValue);
                        }}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove {spec}</span>
                      </button>
                    </Badge>
                  ))}
                </div>
                <FormControl>
                  <Select
                    onValueChange={(value) => {
                      if (!field.value.includes(value)) {
                        field.onChange([...field.value, value]);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select specializations" />
                    </SelectTrigger>
                    <SelectContent>
                      {specializations.map((spec) => (
                        <SelectItem key={spec} value={spec}>
                          {spec}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="languages"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Languages</FormLabel>
                <div className="flex flex-wrap gap-2 mb-2">
                  {field.value.map((lang) => (
                    <Badge key={lang} variant="secondary" className="gap-1">
                      {lang}
                      <button
                        type="button"
                        className="ml-1 rounded-full outline-none"
                        onClick={() => {
                          const newValue = field.value.filter(
                            (l) => l !== lang
                          );
                          field.onChange(newValue);
                        }}
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove {lang}</span>
                      </button>
                    </Badge>
                  ))}
                </div>
                <FormControl>
                  <Select
                    onValueChange={(value) => {
                      if (!field.value.includes(value)) {
                        field.onChange([...field.value, value]);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select languages" />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map((lang) => (
                        <SelectItem key={lang} value={lang}>
                          {lang}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="bio"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Professional Bio</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Write about your professional background, expertise, and approach..."
                  className="min-h-32"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-between">
          {onPreviewClick && (
            <Button type="button" variant="outline" onClick={onPreviewClick}>
              Back to Preview
            </Button>
          )}
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? 'Saving...' : 'Save Profile'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

'use client';

import { ClientNotes } from '@/components/lawyer/ClientNotes';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useLawyersExtended } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { LawyerClient } from '@/lib/types/database-modules';
import { format } from 'date-fns';
import {
  Calendar,
  Clock,
  FileText,
  Filter,
  MessageSquare,
  Search,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export function ClientManagement() {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;

  const {
    clients,
    loading,
    error,
    fetchClients,
    clientConsultations,
    fetchClientConsultations,
  } = useLawyersExtended();

  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [selectedClient, setSelectedClient] = useState<LawyerClient | null>(
    null
  );
  const [isLoadingConsultations, setIsLoadingConsultations] = useState(false);

  // Filter clients based on search query and active tab
  const filteredClients = clients.filter((client) => {
    const matchesSearch =
      client.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    // Replace 'status' with a real property, e.g., 'is_active'
    if (activeTab === 'active')
      return matchesSearch && client.status === 'active';
    if (activeTab === 'inactive')
      return matchesSearch && client.status === 'inactive';
    // No status filtering needed since 'status' does not exist
    return matchesSearch;
  });

  // Load client consultations when a client is selected
  useEffect(() => {
    if (selectedClient) {
      setIsLoadingConsultations(true);

      // Create a promise for fetching consultations
      const fetchConsultationsPromise = fetchClientConsultations(
        selectedClient.id
      );

      // Use toast.promise with the fetch promise
      toast.promise(fetchConsultationsPromise, {
        loading: 'Loading client consultations...',
        success: 'Client consultations loaded',
        error: 'Failed to load client consultations',
      });

      // Handle completion
      fetchConsultationsPromise.finally(() => {
        setIsLoadingConsultations(false);
      });
    }
  }, [selectedClient, fetchClientConsultations]);

  // Handle client selection
  const handleClientSelect = (client: LawyerClient) => {
    setSelectedClient(client);
  };

  // Handle back button click
  const handleBackToList = () => {
    setSelectedClient(null);
  };

  // Handle booking a consultation with the selected client
  const handleBookConsultation = () => {
    if (!selectedClient) return;

    router.push(`/${username}/lawyer/book?client_id=${selectedClient.id}`);
  };

  // Handle sending a message to the selected client
  const handleSendMessage = () => {
    if (!selectedClient) return;

    router.push(`/${username}/messages?recipient=${selectedClient.id}`);
  };

  // Client detail view
  if (selectedClient) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={handleBackToList}>
            <Users className="h-4 w-4 mr-2" />
            Back to Client List
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleSendMessage}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Send Message
            </Button>
            <Button onClick={handleBookConsultation}>
              <Calendar className="h-4 w-4 mr-2" />
              Book Consultation
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Client Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex flex-col items-center md:items-start gap-4">
                <UserAvatar
                  fallbackText={selectedClient.full_name}
                  avatarUrl={selectedClient.avatar_url}
                  size="xl"
                  className="h-24 w-24"
                />
                <div className="text-center md:text-left">
                  <h2 className="text-xl font-bold">
                    {selectedClient.full_name}
                  </h2>
                  <p className="text-muted-foreground">
                    {selectedClient.email}
                  </p>
                </div>
              </div>

              <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex flex-col">
                  <span className="text-muted-foreground text-sm">Status</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200"
                  >
                    Active
                  </Badge>
                </div>

                <div className="flex flex-col">
                  <span className="text-muted-foreground text-sm">
                    Consultations
                  </span>
                  <span className="font-medium">
                    {selectedClient.consultation_count}
                  </span>
                </div>

                <div className="flex flex-col">
                  <span className="text-muted-foreground text-sm">
                    Last Consultation
                  </span>
                  <span className="font-medium">
                    {selectedClient.last_consultation_date
                      ? format(
                          new Date(selectedClient.last_consultation_date),
                          'MMM d, yyyy'
                        )
                      : 'None'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Consultation History</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingConsultations ? (
                <div className="space-y-4">
                  <Skeleton className="h-[100px] w-full rounded-md" />
                  <Skeleton className="h-[100px] w-full rounded-md" />
                </div>
              ) : clientConsultations.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Consultations</h3>
                  <p className="text-muted-foreground mb-4">
                    This client hasn't had any consultations yet.
                  </p>
                  <Button onClick={handleBookConsultation}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Book First Consultation
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {clientConsultations.map((consultation) => (
                    <Card key={consultation.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row justify-between gap-4">
                          <div className="flex flex-col gap-2">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">
                                {consultation.consultation_date
                                  ? format(
                                      new Date(consultation.consultation_date),
                                      'MMMM d, yyyy'
                                    )
                                  : 'Unknown date'}
                              </span>
                              <span className="text-muted-foreground">at</span>
                              <span className="font-medium">
                                {consultation.consultation_date
                                  ? format(
                                      new Date(consultation.consultation_date),
                                      'h:mm a'
                                    )
                                  : 'Unknown time'}
                              </span>
                            </div>

                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span>
                                {consultation.duration_minutes} minutes
                              </span>
                              <Badge
                                variant={
                                  consultation.status === 'completed'
                                    ? 'outline'
                                    : 'default'
                                }
                                className={
                                  consultation.status === 'completed'
                                    ? 'bg-green-50 text-green-700 border-green-200'
                                    : consultation.status === 'cancelled'
                                      ? 'bg-red-50 text-red-700 border-red-200'
                                      : ''
                                }
                              >
                                {consultation.status.charAt(0).toUpperCase() +
                                  consultation.status.slice(1)}
                              </Badge>
                            </div>

                            {consultation.document_id && (
                              <div className="flex items-center gap-2 mt-2">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <span>Document attached</span>
                              </div>
                            )}

                            {consultation.notes && (
                              <div className="mt-2 text-sm text-muted-foreground">
                                <p className="line-clamp-2">
                                  {consultation.notes}
                                </p>
                              </div>
                            )}
                          </div>

                          <div className="flex flex-col gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                router.push(
                                  `/${username}/lawyer/consultations/${consultation.id}`
                                )
                              }
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Client Notes</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedClient && <ClientNotes clientId={selectedClient.id} />}
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Client list view
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="relative w-full md:w-96">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search clients..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Button variant="outline" className="gap-2">
          <Filter className="h-4 w-4" />
          <span>Filter</span>
        </Button>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Clients</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-[100px] w-full rounded-md" />
              <Skeleton className="h-[100px] w-full rounded-md" />
              <Skeleton className="h-[100px] w-full rounded-md" />
            </div>
          ) : filteredClients.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Clients Found</h3>
                  <p className="text-muted-foreground mb-4 max-w-md">
                    {searchQuery
                      ? `No clients matching "${searchQuery}"`
                      : "You don't have any clients yet. When you have consultations with clients, they will appear here."}
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredClients.map((client) => (
                <Card
                  key={client.id}
                  className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => handleClientSelect(client)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <UserAvatar
                          fallbackText={client.full_name}
                          avatarUrl={client.avatar_url}
                          size="md"
                          className="h-10 w-10"
                        />
                        <div>
                          <h3 className="font-medium">{client.full_name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {client.email}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-6">
                        <div className="flex flex-col items-center">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">
                              {client.consultation_count}
                            </span>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            Consultations
                          </span>
                        </div>

                        <div className="flex flex-col items-center">
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">
                              {client.last_consultation
                                ? format(
                                    new Date(client.last_consultation),
                                    'MMM d'
                                  )
                                : 'None'}
                            </span>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            Last Session
                          </span>
                        </div>

                        <Badge
                          variant="outline"
                          className="bg-green-50 text-green-700 border-green-200"
                        >
                          Active
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

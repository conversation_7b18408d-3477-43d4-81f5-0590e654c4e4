'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useLawyerAvailability } from '@/lib/hooks';
import { cn } from '@/lib/utils';
import { addDays, format, isSameDay, startOfDay } from 'date-fns';
import { Calendar, ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ConsultationTimeSlotsProps {
  lawyerId?: string;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  selectedTimeSlot: string | null;
  onTimeSlotChange: (timeSlot: string | null) => void;
  durationMinutes: number;
}

export function ConsultationTimeSlots({
  lawyerId,
  selectedDate,
  onDateChange,
  selectedTimeSlot,
  onTimeSlotChange,
  durationMinutes = 60,
}: ConsultationTimeSlotsProps) {
  const { getAvailableTimeSlots } = useLawyerAvailability(lawyerId);
  const [timeSlots, setTimeSlots] = useState<{ start: string; end: string }[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [weekStartDate, setWeekStartDate] = useState<Date>(
    startOfDay(selectedDate)
  );

  // Generate an array of 7 days starting from weekStartDate
  const weekDates = Array.from({ length: 7 }, (_, i) =>
    addDays(weekStartDate, i)
  );

  // Fetch available time slots when the selected date changes
  useEffect(() => {
    const fetchTimeSlots = async () => {
      setLoading(true);
      try {
        const slots = await getAvailableTimeSlots(
          selectedDate,
          durationMinutes,
          lawyerId
        );
        setTimeSlots(slots);
        setError(null);
      } catch (err) {
        console.error('Error fetching time slots:', err);
        setError(
          err instanceof Error ? err : new Error('Failed to fetch time slots')
        );
        setTimeSlots([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTimeSlots();
  }, [selectedDate, durationMinutes, getAvailableTimeSlots, lawyerId]);

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    onDateChange(date);
    onTimeSlotChange(null); // Reset time slot when date changes
  };

  // Handle time slot selection
  const handleTimeSlotSelect = (timeSlot: string) => {
    onTimeSlotChange(timeSlot === selectedTimeSlot ? null : timeSlot);
  };

  // Navigate to previous week
  const handlePreviousWeek = () => {
    setWeekStartDate(addDays(weekStartDate, -7));
  };

  // Navigate to next week
  const handleNextWeek = () => {
    setWeekStartDate(addDays(weekStartDate, 7));
  };

  // Format time for display
  const formatTime = (isoString: string) => {
    return format(new Date(isoString), 'h:mm a');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" size="sm" onClick={handlePreviousWeek}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous Week
        </Button>
        <div className="text-sm font-medium">
          {format(weekStartDate, 'MMMM d')} -{' '}
          {format(addDays(weekStartDate, 6), 'MMMM d, yyyy')}
        </div>
        <Button variant="outline" size="sm" onClick={handleNextWeek}>
          Next Week
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>

      <div className="grid grid-cols-7 gap-2">
        {weekDates.map((date) => (
          <Button
            key={date.toISOString()}
            variant={isSameDay(date, selectedDate) ? 'default' : 'outline'}
            className="flex flex-col h-auto py-2"
            onClick={() => handleDateSelect(date)}
          >
            <span className="text-xs">{format(date, 'EEE')}</span>
            <span className="text-lg font-bold">{format(date, 'd')}</span>
          </Button>
        ))}
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-medium mb-4">
          Available Time Slots for {format(selectedDate, 'EEEE, MMMM d, yyyy')}
        </h3>

        {loading ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {Array.from({ length: 8 }).map((_, index) => (
              <Skeleton key={index} className="h-12 w-full rounded-md" />
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-destructive">
                Error loading time slots. Please try again.
              </p>
            </CardContent>
          </Card>
        ) : timeSlots.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">
                No Available Time Slots
              </h3>
              <p className="text-muted-foreground mb-4">
                There are no available time slots for this date. Please select
                another date.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {timeSlots.map((slot) => (
              <Button
                key={slot.start}
                variant={
                  selectedTimeSlot === slot.start ? 'default' : 'outline'
                }
                className={cn(
                  'flex items-center justify-center h-12',
                  selectedTimeSlot === slot.start ? 'border-primary' : ''
                )}
                onClick={() => handleTimeSlotSelect(slot.start)}
              >
                <Clock className="h-4 w-4 mr-2" />
                <span>{formatTime(slot.start)}</span>
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

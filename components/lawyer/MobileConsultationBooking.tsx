'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { UserAvatar } from '@/components/ui/user-avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ConsultationTimeSlots } from '@/components/lawyer/ConsultationTimeSlots';
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { Lawyer } from '@/lib/types/database-modules';
import { format } from 'date-fns';
import {
  Calendar,
  FileText,
  Video,
  CheckCircle,
  Loader2,
  Star,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { toast } from 'sonner';

interface MobileConsultationBookingProps {
  lawyerId?: string;
  documentId?: string;
  clientId?: string;
  onSuccess?: (result: any) => void;
}

export function MobileConsultationBooking({
  lawyerId,
  documentId,
  clientId,
  onSuccess,
}: MobileConsultationBookingProps) {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username || '';

  // Lawyers hook
  const {
    lawyers,
    loading: loadingLawyers,
    fetchAllLawyers,
    scheduleLawyerConsultation,
  } = useLawyers();

  // State
  const [selectedLawyer, setSelectedLawyer] = useState<Lawyer | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [consultationType, setConsultationType] = useState<
    'video' | 'document'
  >('video');
  const [notes, setNotes] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [step, setStep] = useState<number>(1);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);

  // Fetch lawyers on component mount
  useEffect(() => {
    const loadLawyers = async () => {
      try {
        await fetchAllLawyers();

        if (lawyerId) {
          // If lawyerId is provided, find that specific lawyer
          const lawyer = lawyers.find((l) => l.id === lawyerId);
          if (lawyer) {
            setSelectedLawyer(lawyer);
            setStep(2); // Skip to date selection
          }
        }
      } catch (error) {
        console.error('Error fetching lawyers:', error);
        toast.error('Failed to load lawyers');
      }
    };

    // Use toast.promise for better user feedback
    toast.promise(loadLawyers(), {
      loading: 'Loading lawyers...',
      success: 'Lawyers loaded successfully',
      error: 'Failed to load lawyers',
    });
  }, [lawyerId, fetchAllLawyers, lawyers]);

  // Set document consultation type if documentId is provided
  useEffect(() => {
    if (documentId) {
      setConsultationType('document');
    }
  }, [documentId]);

  // Handle lawyer selection
  const handleSelectLawyer = (lawyer: Lawyer) => {
    setSelectedLawyer(lawyer);
    setStep(2);
  };

  // Move to next step when time slot is selected
  useEffect(() => {
    if (selectedTimeSlot) {
      setStep(3);
    }
  }, [selectedTimeSlot]);

  // Handle consultation type change
  const handleConsultationTypeChange = (type: 'video' | 'document') => {
    setConsultationType(type);
  };

  // Handle notes change
  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNotes(e.target.value);
  };

  // Handle back button
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  // Handle book consultation
  const handleBookConsultation = async () => {
    if (!selectedLawyer || !selectedDate) {
      toast.error('Please select a lawyer and date');
      return;
    }

    setIsSubmitting(true);

    // Create a promise for booking the consultation
    const bookingPromise = async () => {
      try {
        // Format the date as ISO string
        const scheduledAt = selectedDate.toISOString();

        // Use scheduleLawyerConsultation from the useLawyers hook
        const consultation = await scheduleLawyerConsultation(
          selectedLawyer.id,
          consultationType === 'document' ? documentId || null : null,
          scheduledAt,
          60, // Default to 1 hour
          notes || undefined,
          'video'
        );

        // Log the client ID for debugging
        console.log('Client ID used for booking:', clientId || profile?.id);

        setStep(4);

        if (onSuccess) {
          onSuccess(consultation);
        }

        return consultation;
      } catch (error) {
        console.error('Error booking consultation:', error);
        throw error;
      } finally {
        setIsSubmitting(false);
      }
    };

    // Use toast.promise for better user feedback
    toast.promise(bookingPromise(), {
      loading: 'Booking consultation...',
      success: 'Consultation booked successfully',
      error: 'Failed to book consultation',
    });
  };

  // Render lawyer card
  const renderLawyerCard = (lawyer: Lawyer) => (
    <Card
      key={lawyer.id}
      className={`mb-4 cursor-pointer hover:shadow-md transition-shadow ${
        selectedLawyer?.id === lawyer.id ? 'border-primary' : ''
      }`}
      onClick={() => handleSelectLawyer(lawyer)}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <UserAvatar
            avatarUrl={lawyer.avatar_url}
            fallbackText={lawyer.full_name}
            size="lg"
            className="h-12 w-12"
          />
          <div className="flex-1">
            <h3 className="font-medium">{lawyer.full_name}</h3>
            <p className="text-sm text-muted-foreground">
              {lawyer.specialization}
            </p>
            <div className="flex items-center mt-1">
              <div className="flex">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < (lawyer.average_rating || 0)
                        ? 'text-yellow-400 fill-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs ml-1">
                ({(lawyer as any).review_count || 0} reviews)
              </span>
            </div>
          </div>
          <ChevronRight className="h-5 w-5 text-muted-foreground" />
        </div>
      </CardContent>
    </Card>
  );

  // Render step 1: Select lawyer
  const renderStep1 = () => (
    <div>
      <h2 className="text-lg font-medium mb-4">Select a Lawyer</h2>

      {loadingLawyers ? (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} className="mb-4">
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-32 mb-1" />
                    <Skeleton className="h-4 w-24 mb-1" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div>
          {lawyers.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">
              No lawyers available
            </p>
          ) : (
            lawyers.map((lawyer) => renderLawyerCard(lawyer))
          )}
        </div>
      )}
    </div>
  );

  // Render step 2: Select date and time
  const renderStep2 = () => (
    <div>
      <div className="flex items-center mb-4">
        <Button variant="ghost" size="sm" onClick={handleBack}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h2 className="text-lg font-medium flex-1 text-center">
          Select Date & Time
        </h2>
      </div>

      {selectedLawyer && (
        <div className="mb-4">
          <Card className="mb-4">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <UserAvatar
                  avatarUrl={selectedLawyer.avatar_url}
                  fallbackText={selectedLawyer.full_name}
                  size="md"
                  className="h-10 w-10"
                />
                <div>
                  <h3 className="font-medium">{selectedLawyer.full_name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedLawyer.specialization}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <ConsultationTimeSlots
            lawyerId={selectedLawyer.id}
            selectedDate={selectedDate || new Date()}
            onDateChange={setSelectedDate}
            selectedTimeSlot={selectedTimeSlot}
            onTimeSlotChange={setSelectedTimeSlot}
            durationMinutes={60}
          />
        </div>
      )}
    </div>
  );

  // Render step 3: Consultation details
  const renderStep3 = () => (
    <div>
      <div className="flex items-center mb-4">
        <Button variant="ghost" size="sm" onClick={handleBack}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h2 className="text-lg font-medium flex-1 text-center">
          Consultation Details
        </h2>
      </div>

      <div className="space-y-4">
        <div>
          <Label className="text-base">Consultation Type</Label>
          <RadioGroup
            value={consultationType}
            onValueChange={(value) =>
              handleConsultationTypeChange(value as 'video' | 'document')
            }
            className="mt-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="video" id="video" />
              <Label htmlFor="video" className="flex items-center">
                <Video className="h-4 w-4 mr-2 text-blue-500" />
                Video Consultation
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="document"
                id="document"
                disabled={!documentId}
              />
              <Label
                htmlFor="document"
                className={`flex items-center ${
                  !documentId ? 'text-muted-foreground' : ''
                }`}
              >
                <FileText className="h-4 w-4 mr-2 text-indigo-500" />
                Document Review
                {!documentId && (
                  <span className="text-xs ml-2">(No document selected)</span>
                )}
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div>
          <Label htmlFor="notes">Notes (Optional)</Label>
          <Textarea
            id="notes"
            placeholder="Add any additional information or questions for the lawyer"
            value={notes}
            onChange={handleNotesChange}
            className="mt-2"
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Booking Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Lawyer:</span>
              <span className="font-medium">{selectedLawyer?.full_name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Date:</span>
              <span className="font-medium">
                {selectedDate && format(selectedDate, 'MMMM d, yyyy')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Time:</span>
              <span className="font-medium">
                {selectedDate && format(selectedDate, 'h:mm a')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Type:</span>
              <span className="font-medium">
                {consultationType === 'video'
                  ? 'Video Consultation'
                  : 'Document Review'}
              </span>
            </div>
          </CardContent>
        </Card>

        <Button
          className="w-full"
          onClick={handleBookConsultation}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Booking...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Confirm Booking
            </>
          )}
        </Button>
      </div>
    </div>
  );

  // Render step 4: Success
  const renderStep4 = () => (
    <div className="text-center py-8">
      <div className="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
        <CheckCircle className="h-8 w-8 text-green-600" />
      </div>
      <h2 className="text-xl font-bold mb-2">Booking Confirmed!</h2>
      <p className="text-muted-foreground mb-6">
        Your consultation has been successfully booked.
      </p>

      <Card className="mb-6">
        <CardContent className="p-4 space-y-3">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Lawyer:</span>
            <span className="font-medium">{selectedLawyer?.full_name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Date:</span>
            <span className="font-medium">
              {selectedDate && format(selectedDate, 'MMMM d, yyyy')}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Time:</span>
            <span className="font-medium">
              {selectedDate && format(selectedDate, 'h:mm a')}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Type:</span>
            <span className="font-medium">
              {consultationType === 'video'
                ? 'Video Consultation'
                : 'Document Review'}
            </span>
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-col gap-2">
        <Button onClick={() => router.push(`/${username}/calendar`)}>
          <Calendar className="h-4 w-4 mr-2" />
          View in Calendar
        </Button>
        <Button variant="outline" onClick={() => router.push(`/${username}`)}>
          Return to Dashboard
        </Button>
      </div>
    </div>
  );

  // Render current step
  const renderCurrentStep = () => {
    switch (step) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      case 4:
        return renderStep4();
      default:
        return renderStep1();
    }
  };

  return <div className="p-4">{renderCurrentStep()}</div>;
}

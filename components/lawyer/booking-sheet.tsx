'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Sheet } from '@/components/ui/sheet';
import { Textarea } from '@/components/ui/textarea';
import { useLawyers } from '@/lib/hooks';
import { Lawyer } from '@/lib/types/database-modules';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon, Clock } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

const bookingFormSchema = z.object({
  lawyerId: z.string({
    required_error: 'Please select a lawyer',
  }),
  documentId: z.string().optional(),
  consultationDate: z.date({
    required_error: 'Please select a date for the consultation',
  }),
  consultationTime: z.string({
    required_error: 'Please select a time for the consultation',
  }),
  durationMinutes: z.coerce
    .number()
    .min(30, 'Consultation must be at least 30 minutes')
    .max(180, 'Consultation cannot exceed 3 hours'),
  notes: z.string().optional(),
});

type BookingFormValues = z.infer<typeof bookingFormSchema>;

interface BookingSheetProps {
  isOpen: boolean;
  onClose: () => void;
  selectedLawyer?: Lawyer;
  documentId?: string;
}

export function BookingSheet({
  isOpen,
  onClose,
  selectedLawyer,
  documentId,
}: BookingSheetProps) {
  const { lawyers, fetchAllLawyers, scheduleLawyerConsultation } = useLawyers();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [datePickerOpen, setDatePickerOpen] = useState(false);

  useEffect(() => {
    if (isOpen && lawyers.length === 0) {
      fetchAllLawyers();
    }
  }, [isOpen, lawyers.length, fetchAllLawyers]);

  const form = useForm<BookingFormValues>({
    resolver: zodResolver(bookingFormSchema),
    defaultValues: {
      lawyerId: selectedLawyer?.id || '',
      documentId: documentId || '',
      consultationDate: new Date(
        new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000
      ), // Tomorrow
      consultationTime: '10:00', // Default to 10 AM
      durationMinutes: 60,
      notes: '',
    },
  });

  useEffect(() => {
    if (selectedLawyer) {
      form.setValue('lawyerId', selectedLawyer.id);
    }
    if (documentId) {
      form.setValue('documentId', documentId);
    }
  }, [selectedLawyer, documentId, form]);

  const timeSlots = [
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
  ];

  async function onSubmit(data: BookingFormValues) {
    setIsSubmitting(true);
    try {
      // Combine date and time
      const dateTime = new Date(data.consultationDate);
      const [hours, minutes] = data.consultationTime.split(':').map(Number);
      dateTime.setHours(hours, minutes);

      // Format the date as ISO string
      const isoDateTime = dateTime.toISOString();

      console.log('Booking consultation with date:', {
        originalDate: data.consultationDate,
        time: data.consultationTime,
        combinedDateTime: dateTime,
        isoDateTime: isoDateTime,
      });

      // Get the selected lawyer's name for the success message
      const selectedLawyerName =
        lawyers.find((l) => l.id === data.lawyerId)?.full_name || 'the lawyer';

      console.log('Selected lawyer:', {
        lawyerId: data.lawyerId,
        lawyerName: selectedLawyerName,
      });

      const result = await scheduleLawyerConsultation(
        data.lawyerId,
        data.documentId || null,
        isoDateTime,
        data.durationMinutes,
        data.notes
      );

      console.log('Consultation scheduling result:', result);

      // Consider the booking successful even if result is null
      // This is because the scheduleLawyerConsultation function might return null
      // but still successfully create the booking in the database
      toast.success('Consultation scheduled successfully', {
        description: `Your consultation with ${selectedLawyerName} has been scheduled for ${format(dateTime, 'PPP')} at ${format(dateTime, 'p')}.`,
      });
      onClose();
      form.reset();
    } catch (error) {
      console.error('Error scheduling consultation:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      } else {
        console.error('Unknown error type:', typeof error);
      }
      toast.error('An error occurred while scheduling the consultation', {
        description:
          error instanceof Error ? error.message : 'Please try again later.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Reset form when sheet is closed
  useEffect(() => {
    if (!isOpen) {
      form.reset({
        lawyerId: selectedLawyer?.id || '',
        documentId: documentId || '',
        consultationDate: new Date(
          new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000
        ),
        consultationTime: '10:00',
        durationMinutes: 60,
        notes: '',
      });
    }
  }, [isOpen, form, selectedLawyer, documentId]);

  return (
    <Sheet
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
        }
      }}
    >
      <div className="p-6">
        <Sheet.Title className="text-xl font-medium text-zinc-900 mb-2">
          Book a Consultation
        </Sheet.Title>
        <p className="text-sm text-gray-500 mb-4">
          Schedule a consultation with a legal expert to discuss your document
          or legal matter.
        </p>

        <div className="mt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="lawyerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lawyer</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a lawyer" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {lawyers.map((lawyer) => (
                          <SelectItem key={lawyer.id} value={lawyer.id}>
                            {lawyer.full_name} -{' '}
                            {lawyer.specialization.join(', ')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the lawyer you want to consult with
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="consultationDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Date</FormLabel>
                      <Popover
                        open={datePickerOpen}
                        onOpenChange={setDatePickerOpen}
                      >
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className="w-full pl-3 text-left font-normal"
                              type="button"
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={(date) => {
                              if (date) {
                                field.onChange(date);
                                // Close the popover when a date is selected
                                setDatePickerOpen(false);
                              }
                            }}
                            disabled={(date) =>
                              date < new Date() ||
                              date >
                                new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                            }
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>
                        Select a date for your consultation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="consultationTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Time</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a time" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {timeSlots.map((time) => (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose a time for your consultation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="durationMinutes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (minutes)</FormLabel>
                    <div className="flex items-center space-x-2">
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          min={30}
                          max={180}
                          step={15}
                        />
                      </FormControl>
                      <Clock className="h-4 w-4 opacity-50" />
                    </div>
                    <FormDescription>
                      Consultation duration in minutes (30-180)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Briefly describe what you'd like to discuss"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide any additional information about your legal matter
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Scheduling...' : 'Schedule Consultation'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </Sheet>
  );
}

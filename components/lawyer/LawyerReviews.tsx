'use client';

import { Card, CardContent } from '@/components/ui/card';
import { LawyerReview } from '@/lib/types/database-modules';
import { UserAvatar } from '@/components/ui/user-avatar';
import { format } from 'date-fns';
import { Star } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { useLawyers } from '@/lib/hooks';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Extended type to include profiles from join
interface LawyerReviewWithProfiles extends LawyerReview {
  profiles?: {
    full_name?: string;
    avatar_url?: string;
  };
}

interface LawyerReviewsProps {
  reviews?: LawyerReviewWithProfiles[];
  loading?: boolean;
  lawyerId?: string;
}

export function LawyerReviews({
  reviews: initialReviews,
  loading: initialLoading = false,
  lawyerId,
}: LawyerReviewsProps) {
  const { fetchLawyerReviews } = useLawyers();
  const [reviews, setReviews] = useState<LawyerReviewWithProfiles[]>(
    initialReviews || []
  );
  const [loading, setLoading] = useState(initialLoading || !initialReviews);
  const [error, setError] = useState<Error | null>(null);

  // Fetch reviews if not provided and lawyerId is available
  useEffect(() => {
    if (!initialReviews && lawyerId) {
      const fetchReviews = async () => {
        setLoading(true);
        try {
          const fetchedReviews = await fetchLawyerReviews(lawyerId);
          // Cast the fetched reviews to the correct type
          setReviews((fetchedReviews || []) as LawyerReviewWithProfiles[]);
          setError(null);
        } catch (err) {
          console.error('Error fetching lawyer reviews:', err);
          setError(
            err instanceof Error ? err : new Error('Failed to fetch reviews')
          );
          toast.error('Failed to load reviews', {
            description:
              err instanceof Error
                ? err.message
                : 'An unexpected error occurred',
          });
        } finally {
          setLoading(false);
        }
      };

      // Use toast.promise for better user feedback
      toast.promise(fetchReviews(), {
        loading: 'Loading lawyer reviews...',
        success: 'Reviews loaded successfully',
        error: 'Failed to load reviews',
      });
    } else if (initialReviews) {
      setReviews(initialReviews);
    }
  }, [initialReviews, lawyerId, fetchLawyerReviews]);

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-[120px] w-full rounded-md" />
        <Skeleton className="h-[120px] w-full rounded-md" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-destructive py-8">
            Error loading reviews: {error.message}
          </p>
        </CardContent>
      </Card>
    );
  }

  if (reviews.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground py-8">No reviews yet</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {reviews.map((review) => {
        // Get user name from profiles if available, otherwise use Anonymous
        const userName = review.profiles?.full_name || 'Anonymous User';
        const userAvatar = review.profiles?.avatar_url;
        const isAnonymous = review.is_anonymous || false;

        return (
          <Card key={review.id} className="hover:shadow-sm transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <UserAvatar
                  fallbackText={isAnonymous ? 'Anonymous' : userName || 'User'}
                  avatarUrl={isAnonymous ? undefined : userAvatar}
                  size="sm"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">
                        {isAnonymous ? 'Anonymous User' : userName}
                      </h4>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < review.rating
                                  ? 'text-yellow-500 fill-yellow-500'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {format(new Date(review.created_at), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>
                  </div>
                  {review.review_text && (
                    <p className="mt-2 text-sm text-neutral-700">
                      {review.review_text}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useLawyers } from '@/lib/hooks';
import { Lawyer, LawyerConsultation } from '@/lib/types/database-modules';
import {
  Calendar,
  Clock,
  DollarSign,
  FileText,
  Star,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface LawyerStatsProps {
  lawyerId?: string; // Optional: if not provided, will use the current user's lawyer profile
}

export function LawyerStats({ lawyerId }: LawyerStatsProps) {
  const {
    loading: hookLoading,
    getLawyerById,
    fetchLawyerProfile,
    fetchLawyerConsultations,
  } = useLawyers();
  const [consultations, setConsultations] = useState<LawyerConsultation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lawyerProfile, setLawyerProfile] = useState<Lawyer | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        let profile: Lawyer | null = null;
        if (lawyerId) {
          toast.loading('Loading lawyer profile...');
          try {
            const result = await getLawyerById(lawyerId);
            if (result) {
              profile = result;
              toast.success('Lawyer profile loaded');
            } else {
              toast.error('Lawyer profile not found');
            }
          } catch (error) {
            toast.error('Failed to load lawyer profile');
            throw error;
          }
        } else {
          toast.loading('Loading lawyer profile...');
          try {
            profile = await fetchLawyerProfile();
            toast.success('Lawyer profile loaded');
          } catch (error) {
            toast.error('Failed to load lawyer profile');
            throw error;
          }
        }

        if (profile) {
          setLawyerProfile(profile);
          toast.loading('Loading consultations...');
          try {
            const data = await fetchLawyerConsultations(profile.id);
            toast.success('Consultations loaded');
            setConsultations(data || []);
          } catch (error) {
            toast.error('Failed to load consultations');
            throw error;
          }
        }
      } catch (error) {
        console.error('Error fetching lawyer data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [lawyerId, getLawyerById, fetchLawyerProfile, fetchLawyerConsultations]);

  // If loading, show skeleton
  if (isLoading || hookLoading || !lawyerProfile) {
    return <LawyerStatsLoading />;
  }

  // Calculate stats
  const upcomingConsultations = consultations.filter(
    (c: LawyerConsultation) => c.status === 'scheduled'
  ).length;

  const completedConsultations = consultations.filter(
    (c: LawyerConsultation) => c.status === 'completed'
  ).length;

  const totalEarnings = consultations
    .filter((c: LawyerConsultation) => c.payment_status === 'paid')
    .reduce(
      (sum: number, c: LawyerConsultation) => sum + (c.payment_amount || 0),
      0
    );

  const averageRating = lawyerProfile.average_rating || 0;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Upcoming Consultations
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{upcomingConsultations}</div>
          <p className="text-xs text-muted-foreground">
            {upcomingConsultations === 1 ? 'Consultation' : 'Consultations'}{' '}
            scheduled
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Completed Consultations
          </CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{completedConsultations}</div>
          <p className="text-xs text-muted-foreground">
            Total completed sessions
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${totalEarnings.toFixed(2)}</div>
          <p className="text-xs text-muted-foreground">
            From {completedConsultations} consultations
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
          <Star className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
          <p className="text-xs text-muted-foreground">
            Based on {lawyerProfile.consultation_count || 0} reviews
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

// Loading skeleton component
function LawyerStatsLoading() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {[1, 2, 3, 4].map((i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-4 rounded-full" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-4 w-24" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

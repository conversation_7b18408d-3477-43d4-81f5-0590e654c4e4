'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { useClientNotes } from '@/lib/hooks';
import { ClientNote } from '@/lib/types/database-modules';
import { toast } from 'sonner';
import { format } from 'date-fns';
import {
  Edit,
  Loader2,
  MoreVertical,
  Pin,
  PinOff,
  Plus,
  Save,
  Trash2,
  X,
} from 'lucide-react';
import { useRef, useState } from 'react';

interface ClientNotesProps {
  clientId: string;
}

export function ClientNotes({ clientId }: ClientNotesProps) {
  const { notes, loading, error, addNote, updateNote, deleteNote, togglePin } =
    useClientNotes(clientId);

  const [isAddingNote, setIsAddingNote] = useState(false);
  const [newNoteContent, setNewNoteContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState('');
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Handle adding a new note
  const handleAddNote = async () => {
    if (!newNoteContent.trim()) return;

    setIsSubmitting(true);
    try {
      // Create a promise for the note addition
      const addNotePromise = addNote({
        client_id: clientId,
        content: newNoteContent.trim(),
        lawyer_id: '00000000-0000-0000-0000-000000000000', // This will be replaced with the actual lawyer ID on the server
        is_pinned: false,
      });

      // Use toast.promise with the note promise
      toast.promise(addNotePromise, {
        loading: 'Adding note...',
        success: 'Note added successfully',
        error: 'Failed to add note',
      });

      // Await the result
      await addNotePromise;

      // Reset form
      setNewNoteContent('');
      setIsAddingNote(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle canceling adding a new note
  const handleCancelAdd = () => {
    setNewNoteContent('');
    setIsAddingNote(false);
  };

  // Handle editing a note
  const handleEditNote = (note: ClientNote) => {
    setEditingNoteId(note.id);
    setEditedContent(note.content);

    // Focus the textarea after a short delay to allow rendering
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }, 100);
  };

  // Handle saving an edited note
  const handleSaveEdit = async () => {
    if (!editingNoteId || !editedContent.trim()) return;

    setIsSubmitting(true);
    try {
      // Create a promise for the note update
      const updateNotePromise = updateNote({
        id: editingNoteId,
        content: editedContent.trim(),
      });

      // Use toast.promise with the update promise
      toast.promise(updateNotePromise, {
        loading: 'Updating note...',
        success: 'Note updated successfully',
        error: 'Failed to update note',
      });

      // Await the result
      await updateNotePromise;

      // Reset editing state
      setEditingNoteId(null);
      setEditedContent('');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle canceling editing a note
  const handleCancelEdit = () => {
    setEditingNoteId(null);
    setEditedContent('');
  };

  // Handle toggling pin status
  const handleTogglePin = async (
    noteId: string,
    currentPinned: boolean | null
  ) => {
    // Create a promise for toggling the pin
    const togglePinPromise = togglePin(noteId, !(currentPinned || false));

    // Use toast.promise with the toggle promise
    toast.promise(togglePinPromise, {
      loading: 'Updating note...',
      success: currentPinned ? 'Note unpinned' : 'Note pinned',
      error: 'Failed to update note',
    });

    // Await the result
    await togglePinPromise;
  };

  // Handle confirming note deletion
  const handleConfirmDelete = async () => {
    if (!noteToDelete) return;

    // Create a promise for deleting the note
    const deleteNotePromise = deleteNote(noteToDelete);

    // Use toast.promise with the delete promise
    toast.promise(deleteNotePromise, {
      loading: 'Deleting note...',
      success: 'Note deleted successfully',
      error: 'Failed to delete note',
    });

    // Await the result
    await deleteNotePromise;

    // Close the dialog
    setNoteToDelete(null);
  };

  // Render a single note
  const renderNote = (note: ClientNote) => {
    const isEditing = editingNoteId === note.id;

    return (
      <Card key={note.id} className={note.is_pinned ? 'border-primary/50' : ''}>
        <CardContent className="p-4">
          {isEditing ? (
            <div className="space-y-4">
              <Textarea
                ref={textareaRef}
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="min-h-[100px] w-full"
                placeholder="Enter note content..."
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelEdit}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveEdit}
                  disabled={!editedContent.trim() || isSubmitting}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-1" />
                  )}
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex justify-between items-start mb-2">
                <div className="text-xs text-muted-foreground">
                  {note.created_at
                    ? format(new Date(note.created_at), 'MMM d, yyyy h:mm a')
                    : 'Unknown date'}
                  {note.created_at &&
                    note.updated_at &&
                    note.created_at !== note.updated_at &&
                    ' (edited)'}
                </div>
                <div className="flex items-center">
                  {note.is_pinned && (
                    <Pin className="h-3.5 w-3.5 text-primary mr-2" />
                  )}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditNote(note)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleTogglePin(note.id, note.is_pinned)}
                      >
                        {note.is_pinned ? (
                          <>
                            <PinOff className="h-4 w-4 mr-2" />
                            Unpin
                          </>
                        ) : (
                          <>
                            <Pin className="h-4 w-4 mr-2" />
                            Pin
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive"
                        onClick={() => setNoteToDelete(note.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <div className="whitespace-pre-wrap">{note.content}</div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Client Notes</h3>
        {!isAddingNote && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAddingNote(true)}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Note
          </Button>
        )}
      </div>

      {isAddingNote && (
        <Card>
          <CardContent className="p-4 space-y-4">
            <Textarea
              value={newNoteContent}
              onChange={(e) => setNewNoteContent(e.target.value)}
              className="min-h-[100px] w-full"
              placeholder="Enter note content..."
              autoFocus
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancelAdd}
                disabled={isSubmitting}
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleAddNote}
                disabled={!newNoteContent.trim() || isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-1" />
                )}
                Save
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-[120px] w-full rounded-md" />
          <Skeleton className="h-[120px] w-full rounded-md" />
        </div>
      ) : notes.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              No notes yet. Add a note to keep track of important information
              about this client.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">{notes.map(renderNote)}</div>
      )}

      {/* Delete confirmation dialog */}
      <AlertDialog
        open={!!noteToDelete}
        onOpenChange={() => setNoteToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Note</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this note? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useLawyers } from '@/lib/hooks';
import { Lawyer } from '@/lib/types/database-modules';
import {
  BookOpen,
  BriefcaseBusiness,
  Calendar,
  CheckCircle2,
  Clock,
  GraduationCap,
  Languages,
  Mail,
  Phone,
  Star,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface LawyerProfilePreviewProps {
  lawyerProfile?: Lawyer;
  lawyerId?: string;
  onEditClick?: () => void;
}

export function LawyerProfilePreview({
  lawyerProfile: initialLawyerProfile,
  lawyerId,
  onEditClick,
}: LawyerProfilePreviewProps) {
  const { getLawyerById } = useLawyers();
  const [lawyerProfile, setLawyerProfile] = useState<Lawyer | null>(
    initialLawyerProfile || null
  );
  const [loading, setLoading] = useState(!initialLawyerProfile);
  const [error, setError] = useState<Error | null>(null);

  // Fetch lawyer profile if not provided
  useEffect(() => {
    if (!initialLawyerProfile && lawyerId) {
      const fetchLawyerProfile = async () => {
        setLoading(true);
        try {
          const fetchedProfile = await getLawyerById(lawyerId);
          setLawyerProfile(fetchedProfile);
          setError(null);
        } catch (err) {
          console.error('Error fetching lawyer profile:', err);
          setError(
            err instanceof Error
              ? err
              : new Error('Failed to fetch lawyer profile')
          );
          toast.error('Failed to load lawyer profile', {
            description:
              err instanceof Error
                ? err.message
                : 'An unexpected error occurred',
          });
        } finally {
          setLoading(false);
        }
      };

      // Use toast.promise for better user feedback
      toast.promise(fetchLawyerProfile(), {
        loading: 'Loading lawyer profile...',
        success: 'Profile loaded successfully',
        error: 'Failed to load profile',
      });
    } else if (initialLawyerProfile) {
      setLawyerProfile(initialLawyerProfile);
    }
  }, [initialLawyerProfile, lawyerId, getLawyerById]);

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row gap-6">
          <div className="md:w-1/3">
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col items-center">
                  <Skeleton className="h-32 w-32 rounded-full" />
                  <Skeleton className="h-6 w-48 mt-4" />
                  <Skeleton className="h-4 w-32 mt-2" />
                  <Skeleton className="h-4 w-40 mt-1" />
                  <Separator className="my-6" />
                  <div className="w-full space-y-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="flex items-start gap-3">
                        <Skeleton className="h-5 w-5 rounded-full" />
                        <div className="flex-1">
                          <Skeleton className="h-4 w-20 mb-1" />
                          <Skeleton className="h-4 w-40" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="md:w-2/3">
            <Card className="mb-6">
              <CardContent className="p-6">
                <Skeleton className="h-6 w-32 mb-3" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
            <Card className="mb-6">
              <CardContent className="p-6">
                <Skeleton className="h-6 w-32 mb-4" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex items-start gap-3">
                      <Skeleton className="h-5 w-5 rounded-full" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-32 mb-1" />
                        <Skeleton className="h-4 w-full" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !lawyerProfile) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-destructive py-8">
            {error ? `Error: ${error.message}` : 'Lawyer profile not found'}
          </p>
        </CardContent>
      </Card>
    );
  }
  return (
    <div className="space-y-6">
      {onEditClick && (
        <div className="flex justify-end">
          <Button onClick={onEditClick}>Edit Profile</Button>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-6">
        {/* Left column - Profile info */}
        <div className="md:w-1/3">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center">
                <div className="relative">
                  <UserAvatar
                    fallbackText={lawyerProfile.full_name}
                    avatarUrl={
                      lawyerProfile.avatar_url ||
                      lawyerProfile.profile_image_url
                    }
                    size="xl"
                    className="h-32 w-32"
                  />
                  {lawyerProfile.is_verified && (
                    <div className="absolute bottom-0 right-0 rounded-full bg-white p-0.5">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                    </div>
                  )}
                </div>

                <h2 className="text-xl font-semibold mt-4">
                  {lawyerProfile.full_name}
                </h2>

                <div className="flex items-center gap-1 mt-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                  <span className="font-medium">
                    {lawyerProfile.average_rating || '0.0'}
                  </span>
                  <span className="text-sm text-neutral-500">
                    ({lawyerProfile.consultation_count || 0} consultations)
                  </span>
                </div>

                <div className="flex items-center gap-1 text-sm text-neutral-500 mt-1">
                  <BriefcaseBusiness className="h-3.5 w-3.5" />
                  <span>
                    {lawyerProfile.specialization?.join(', ') ||
                      'General Practice'}
                  </span>
                </div>

                <Separator className="my-6" />

                <div className="w-full space-y-4">
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Email</h3>
                      <p className="text-sm">
                        {(lawyerProfile as any).email || 'Not provided'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Phone</h3>
                      <p className="text-sm">
                        {(lawyerProfile as any).phone || 'Not provided'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Languages className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Languages</h3>
                      <p className="text-sm">
                        {lawyerProfile.languages?.join(', ') || 'Not specified'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Experience</h3>
                      <p className="text-sm">
                        {lawyerProfile.years_experience
                          ? `${lawyerProfile.years_experience} years`
                          : 'Not specified'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column - Bio, expertise, rates */}
        <div className="md:w-2/3">
          <Card className="mb-6">
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-3">About</h3>
              <p className="text-neutral-700 whitespace-pre-line">
                {lawyerProfile.bio || 'No bio available for this lawyer.'}
              </p>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Expertise</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start gap-3">
                  <BriefcaseBusiness className="h-5 w-5 text-neutral-500 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium">Specializations</h4>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {lawyerProfile.specialization?.map((spec: string) => (
                        <Badge key={spec} variant="outline">
                          {spec}
                        </Badge>
                      )) || (
                        <span className="text-sm text-neutral-500">
                          Not specified
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <GraduationCap className="h-5 w-5 text-neutral-500 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium">Education</h4>
                    <p className="text-sm">
                      {lawyerProfile.education || 'Not specified'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <BookOpen className="h-5 w-5 text-neutral-500 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium">Practice Areas</h4>
                    <p className="text-sm">
                      {lawyerProfile.specialization?.join(', ') ||
                        'Not specified'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-neutral-500 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium">Years of Experience</h4>
                    <p className="text-sm">
                      {lawyerProfile.years_experience
                        ? `${lawyerProfile.years_experience} years`
                        : 'Not specified'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Consultation Options</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">Video Consultation</h4>
                      <p className="text-sm text-neutral-500">30-60 minutes</p>
                    </div>
                    <Badge>Recommended</Badge>
                  </div>
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-baseline gap-1">
                      <span className="text-lg font-semibold">
                        ${lawyerProfile.consultation_fee || 0}
                      </span>
                      <span className="text-sm text-neutral-500">flat fee</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">Hourly Services</h4>
                      <p className="text-sm text-neutral-500">
                        Ongoing legal work
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-baseline gap-1">
                      <span className="text-lg font-semibold">
                        ${lawyerProfile.hourly_rate || 0}
                      </span>
                      <span className="text-sm text-neutral-500">/hour</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

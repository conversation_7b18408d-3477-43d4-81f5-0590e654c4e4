'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useDocuments, useLawyerMessages, useLawyers } from '@/lib/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileText, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Drawer } from 'vaul';
import * as z from 'zod';

const formSchema = z.object({
  lawyerId: z.string({
    required_error: 'Please select a lawyer',
  }),
  documentId: z.string({
    required_error: 'Please select a document',
  }),
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface DocumentReviewSubmissionProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  defaultLawyerId?: string;
  defaultDocumentId?: string;
}

export function DocumentReviewSubmission({
  isOpen,
  onClose,
  onSuccess,
  defaultLawyerId,
  defaultDocumentId,
}: DocumentReviewSubmissionProps) {
  const { lawyers, loading: lawyersLoading } = useLawyers();
  const {
    documents,
    loading: documentsLoading,
    getAll: fetchDocuments,
  } = useDocuments();
  const { submitDocumentForReview } = useLawyerMessages('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      lawyerId: defaultLawyerId || '',
      documentId: defaultDocumentId || '',
      notes: '',
    },
  });

  // Get the fetchAllLawyers function from useLawyers
  const { fetchAllLawyers } = useLawyers();

  useEffect(() => {
    if (isOpen) {
      // Fetch lawyers and documents when the dialog opens
      fetchAllLawyers();
      fetchDocuments();

      // Set default values if provided
      if (defaultLawyerId) {
        form.setValue('lawyerId', defaultLawyerId);
      }
      if (defaultDocumentId) {
        form.setValue('documentId', defaultDocumentId);
      }
    }
  }, [
    isOpen,
    fetchAllLawyers,
    fetchDocuments,
    form,
    defaultLawyerId,
    defaultDocumentId,
  ]);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      // Create a promise for submitting the document
      const submitPromise = async () => {
        return await submitDocumentForReview(
          data.lawyerId,
          data.documentId,
          data.notes
        );
      };

      // Use toast.promise for better user feedback
      toast.promise(submitPromise(), {
        loading: 'Submitting document for review...',
        success: 'Document submitted for review',
        error: (err) =>
          `Failed to submit document for review: ${err.message || 'Unknown error'}`,
      });

      // Execute the promise and get the result
      const result = await submitPromise();

      if (result) {
        // Reset form
        form.reset();

        // Close dialog
        onClose();

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      console.error('Error submitting document for review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Drawer.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Drawer.Content className="sm:max-w-[500px] overflow-y-auto">
        <Drawer.Title className="sr-only">
          Submit Document for Review
        </Drawer.Title>
        <div className="p-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold">
              Submit Document for Review
            </h2>
            <p className="text-sm text-muted-foreground">
              Send your document to a lawyer for professional review and
              feedback.
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="lawyerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lawyer</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={lawyersLoading || isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a lawyer" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {lawyersLoading ? (
                          <div className="flex items-center justify-center py-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading lawyers...</span>
                          </div>
                        ) : lawyers.length === 0 ? (
                          <div className="p-2 text-center text-sm text-muted-foreground">
                            No lawyers available
                          </div>
                        ) : (
                          lawyers.map((lawyer) => (
                            <SelectItem key={lawyer.id} value={lawyer.id}>
                              {lawyer.full_name || lawyer.email}
                              {lawyer.specialization &&
                                ` (${lawyer.specialization})`}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="documentId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={documentsLoading || isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a document" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {documentsLoading ? (
                          <div className="flex items-center justify-center py-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading documents...</span>
                          </div>
                        ) : documents.length === 0 ? (
                          <div className="p-2 text-center text-sm text-muted-foreground">
                            No documents available
                          </div>
                        ) : (
                          documents.map((document) => (
                            <SelectItem key={document.id} value={document.id}>
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4" />
                                <span>{document.title}</span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the document you want the lawyer to review
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any specific questions or concerns you have about the document..."
                        className="resize-none"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide context or specific areas you'd like the lawyer to
                      focus on
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Submit for Review
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </Drawer.Content>
    </Drawer.Root>
  );
}

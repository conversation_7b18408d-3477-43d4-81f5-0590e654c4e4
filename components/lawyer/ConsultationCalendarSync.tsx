'use client';

import { Button } from '@/components/ui/button';
import { useCalendarIntegration } from '@/lib/hooks';
import { Calendar, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface ConsultationCalendarSyncProps {
  consultationId: string;
  isNewConsultation?: boolean;
}

export function ConsultationCalendarSync({
  consultationId,
  isNewConsultation = false,
}: ConsultationCalendarSyncProps) {
  const { loading, isProviderConnected, syncConsultation } =
    useCalendarIntegration();

  const [isSyncing, setIsSyncing] = useState(false);

  // Handle sync to Google Calendar
  const handleSync = async () => {
    if (!isProviderConnected('google')) {
      toast.error('Please connect to Google Calendar in settings first');
      return;
    }

    setIsSyncing(true);
    try {
      const action = isNewConsultation ? 'create' : 'update';
      await syncConsultation(consultationId, action);
    } finally {
      setIsSyncing(false);
    }
  };

  if (loading) {
    return (
      <Button variant="outline" disabled>
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        Loading...
      </Button>
    );
  }

  if (!isProviderConnected('google')) {
    return (
      <Button
        variant="outline"
        onClick={() =>
          toast.error('Please connect to Google Calendar in settings first')
        }
      >
        <Calendar className="h-4 w-4 mr-2" />
        Add to Calendar
      </Button>
    );
  }

  return (
    <Button variant="outline" onClick={handleSync} disabled={isSyncing}>
      {isSyncing ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Syncing...
        </>
      ) : (
        <>
          <Calendar className="h-4 w-4 mr-2" />
          {isNewConsultation ? 'Add to Calendar' : 'Update in Calendar'}
        </>
      )}
    </Button>
  );
}

'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useRecurringConsultations } from '@/lib/hooks';
import { RecurringConsultationWithJoins } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { format, parseISO } from 'date-fns';
import {
  AlertCircle,
  Calendar,
  ChevronDown,
  ChevronUp,
  Clock,
  Edit,
  FileText,
  Loader2,
  Plus,
  Repeat,
  Trash2,
  Video,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { RecurringConsultationForm } from './RecurringConsultationForm';

interface RecurringConsultationsListProps {
  lawyerId?: string;
  clientId?: string;
  showCreateButton?: boolean;
  onConsultationCreated?: (
    consultation: RecurringConsultationWithJoins
  ) => void;
}

export function RecurringConsultationsList({
  lawyerId,
  clientId,
  showCreateButton = true,
  onConsultationCreated,
}: RecurringConsultationsListProps) {
  const {
    recurringConsultations,
    loading,
    error,
    toggleActiveStatus,
    deleteRecurringConsultation,
    getGeneratedConsultations,
  } = useRecurringConsultations(lawyerId, clientId);

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedConsultation, setSelectedConsultation] =
    useState<RecurringConsultationWithJoins | null>(null);
  const [expandedConsultation, setExpandedConsultation] = useState<
    string | null
  >(null);
  const [generatedConsultations, setGeneratedConsultations] = useState<any[]>(
    []
  );
  const [loadingGenerated, setLoadingGenerated] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle create success
  const handleCreateSuccess = (
    consultation: RecurringConsultationWithJoins
  ) => {
    setIsCreateDialogOpen(false);
    onConsultationCreated?.(consultation);
  };

  // Handle edit success
  const handleEditSuccess = () => {
    setIsEditDialogOpen(false);
    setSelectedConsultation(null);
  };

  // Handle toggle active status
  const handleToggleActive = async (id: string, isActive: boolean) => {
    setIsUpdatingStatus(id);

    // Create a promise for the toggle operation
    const togglePromise = toggleActiveStatus(id, isActive);

    // Use toast.promise for better user feedback
    toast.promise(togglePromise, {
      loading: `${isActive ? 'Activating' : 'Deactivating'} recurring consultation...`,
      success: `Consultation ${isActive ? 'activated' : 'deactivated'} successfully`,
      error: `Failed to ${isActive ? 'activate' : 'deactivate'} consultation`,
    });

    await togglePromise;
    setIsUpdatingStatus(null);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!selectedConsultation) return;

    setIsDeleting(true);

    // Create a promise for the delete operation
    const deletePromise = deleteRecurringConsultation(
      selectedConsultation.id,
      true
    );

    // Use toast.promise for better user feedback
    toast.promise(deletePromise, {
      loading: 'Deleting recurring consultation...',
      success: 'Consultation deleted successfully',
      error: 'Failed to delete consultation',
    });

    const success = await deletePromise;
    setIsDeleting(false);

    if (success) {
      setIsDeleteDialogOpen(false);
      setSelectedConsultation(null);
    }
  };

  // Handle expand/collapse
  const handleToggleExpand = async (id: string) => {
    if (expandedConsultation === id) {
      setExpandedConsultation(null);
      return;
    }

    setExpandedConsultation(id);
    setLoadingGenerated(true);

    // Create a promise for fetching generated consultations
    const fetchPromise = async () => {
      try {
        const consultations = await getGeneratedConsultations(id);
        setGeneratedConsultations(consultations);
        return consultations;
      } catch (error) {
        console.error('Error fetching generated consultations:', error);
        throw error;
      } finally {
        setLoadingGenerated(false);
      }
    };

    // Use toast.promise for better user feedback
    toast.promise(fetchPromise(), {
      loading: 'Loading scheduled consultations...',
      success: 'Consultations loaded successfully',
      error: 'Failed to load consultations',
    });
  };

  // Format day of week
  const formatDayOfWeek = (day: number) => {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    return days[day];
  };

  // Format frequency
  const formatFrequency = (frequency: string) => {
    switch (frequency) {
      case 'weekly':
        return 'Weekly';
      case 'biweekly':
        return 'Bi-weekly';
      case 'monthly':
        return 'Monthly';
      default:
        return frequency;
    }
  };

  // Format time
  const formatTime = (time: string) => {
    try {
      const [hours, minutes] = time.split(':');
      return format(
        new Date().setHours(parseInt(hours), parseInt(minutes)),
        'h:mm a'
      );
    } catch (error) {
      return time;
    }
  };

  // Format consultation status
  const formatStatus = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'confirmed':
        return 'Confirmed';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'completed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-6 w-20" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">
            Error Loading Recurring Consultations
          </h3>
          <p className="text-muted-foreground mb-4">
            There was an error loading your recurring consultations. Please try
            again.
          </p>
          <Button onClick={() => window.location.reload()}>Refresh</Button>
        </CardContent>
      </Card>
    );
  }

  if (recurringConsultations.length === 0) {
    return (
      <div className="space-y-4">
        <Card>
          <CardContent className="p-6 text-center">
            <Repeat className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">
              No Recurring Consultations
            </h3>
            <p className="text-muted-foreground mb-4">
              You don't have any recurring consultations set up yet.
            </p>
            {showCreateButton && (
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Recurring Consultation
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Create Dialog */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <RecurringConsultationForm
              lawyerId={lawyerId}
              clientId={clientId}
              onSuccess={handleCreateSuccess}
              onCancel={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {showCreateButton && (
        <div className="flex justify-end mb-4">
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Recurring Consultation
          </Button>
        </div>
      )}

      {recurringConsultations.map((consultation) => (
        <Card
          key={consultation.id}
          className={cn(
            'overflow-hidden transition-all',
            !consultation.is_active && 'opacity-70'
          )}
        >
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Repeat className="h-5 w-5 text-primary" />
                <h3 className="font-medium text-lg">{consultation.title}</h3>
                {!consultation.is_active && (
                  <Badge
                    variant="outline"
                    className="bg-gray-100 text-gray-800"
                  >
                    Inactive
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Switch
                  checked={consultation.is_active}
                  onCheckedChange={(checked) =>
                    handleToggleActive(consultation.id, checked)
                  }
                  disabled={isUpdatingStatus === consultation.id}
                  aria-label={consultation.is_active ? 'Active' : 'Inactive'}
                />

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    setSelectedConsultation(
                      consultation as any as RecurringConsultationWithJoins
                    );
                    setIsEditDialogOpen(true);
                  }}
                >
                  <Edit className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  size="icon"
                  className="text-destructive"
                  onClick={() => {
                    setSelectedConsultation(
                      consultation as any as RecurringConsultationWithJoins
                    );
                    setIsDeleteDialogOpen(true);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDayOfWeek(consultation.day_of_week)},{' '}
                    {formatFrequency(consultation.frequency)}
                  </span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatTime(consultation.start_time)} (
                    {consultation.duration_minutes} minutes)
                  </span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  {consultation.consultation_type === 'video' ? (
                    <Video className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span>
                    {consultation.consultation_type === 'video'
                      ? 'Video Consultation'
                      : 'Document Review'}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <span className="font-medium">Start Date:</span>
                  <span>
                    {format(new Date(consultation.start_date), 'MMMM d, yyyy')}
                  </span>
                </div>

                {consultation.end_date && (
                  <div className="flex items-center gap-2 text-sm">
                    <span className="font-medium">End Date:</span>
                    <span>
                      {format(new Date(consultation.end_date), 'MMMM d, yyyy')}
                    </span>
                  </div>
                )}

                {/* Client and lawyer information */}
                {((consultation as any).client ||
                  (consultation as any).lawyer) && (
                  <div className="flex items-center gap-2">
                    {(consultation as any).client && (
                      <div className="flex items-center gap-2">
                        <UserAvatar
                          size="sm"
                          avatarUrl={(consultation as any).client.avatar_url}
                          fallbackText={(consultation as any).client.full_name}
                          className="h-6 w-6"
                        />
                        <span className="text-sm">
                          {(consultation as any).client.full_name}
                        </span>
                      </div>
                    )}

                    {(consultation as any).lawyer && !lawyerId && (
                      <div className="flex items-center gap-2 ml-4">
                        <UserAvatar
                          size="sm"
                          avatarUrl={(consultation as any).lawyer.avatar_url}
                          fallbackText={(consultation as any).lawyer.full_name}
                          className="h-6 w-6"
                        />
                        <span className="text-sm">
                          {(consultation as any).lawyer.full_name}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {consultation.description && (
              <div className="mb-4">
                <p className="text-sm text-muted-foreground">
                  {consultation.description}
                </p>
              </div>
            )}

            <div className="mt-4 pt-4 border-t">
              <Button
                variant="ghost"
                size="sm"
                className="w-full flex items-center justify-center"
                onClick={() => handleToggleExpand(consultation.id)}
              >
                {expandedConsultation === consultation.id ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-2" />
                    Hide Scheduled Consultations
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-2" />
                    Show Scheduled Consultations
                  </>
                )}
              </Button>

              {expandedConsultation === consultation.id && (
                <div className="mt-4 space-y-2">
                  {loadingGenerated ? (
                    <div className="flex justify-center py-4">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    </div>
                  ) : generatedConsultations.length === 0 ? (
                    <p className="text-sm text-center text-muted-foreground py-2">
                      No upcoming consultations scheduled
                    </p>
                  ) : (
                    <div className="space-y-2 max-h-[300px] overflow-y-auto">
                      {generatedConsultations.map((consultation) => (
                        <div
                          key={consultation.id}
                          className="flex items-center justify-between p-3 bg-muted rounded-md"
                        >
                          <div className="flex items-center gap-3">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm font-medium">
                              {format(
                                parseISO(consultation.consultation_date),
                                'EEEE, MMMM d, yyyy'
                              )}
                            </span>
                            <span className="text-sm">
                              {format(
                                parseISO(consultation.consultation_date),
                                'h:mm a'
                              )}
                            </span>
                          </div>
                          <Badge
                            variant="outline"
                            className={getStatusColor(consultation.status)}
                          >
                            {formatStatus(consultation.status)}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <RecurringConsultationForm
            lawyerId={lawyerId}
            clientId={clientId}
            onSuccess={handleCreateSuccess}
            onCancel={() => setIsCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          {selectedConsultation && (
            <RecurringConsultationForm
              recurringConsultationId={selectedConsultation.id}
              onSuccess={handleEditSuccess}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedConsultation(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Recurring Consultation</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-2">
              Are you sure you want to delete this recurring consultation?
            </p>
            <p className="text-sm text-muted-foreground">
              This will cancel all future consultations. This action cannot be
              undone.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setSelectedConsultation(null);
              }}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

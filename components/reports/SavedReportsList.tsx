'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { SavedReport } from '@/lib/types/database-modules';
import { format, parseISO } from 'date-fns';
import {
  AlertCircle,
  BookmarkIcon,
  Calendar,
  Clock,
  Download,
  FileText,
  Loader2,
  Trash2,
  Users,
} from 'lucide-react';

interface SavedReportsListProps {
  reports: SavedReport[];
  loading?: boolean;
  onDelete: (reportId: string) => Promise<boolean>;
  onExport: (report: SavedReport) => Promise<boolean>;
}

export function SavedReportsList({
  reports,
  loading = false,
  onDelete,
  onExport,
}: SavedReportsListProps) {
  const [selectedReport, setSelectedReport] = useState<SavedReport | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Get report icon based on type
  const getReportIcon = (type: string) => {
    switch (type) {
      case 'performance':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'client':
        return <Users className="h-4 w-4 text-green-500" />;
      case 'time_slot':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'monthly':
        return <Calendar className="h-4 w-4 text-purple-500" />;
      default:
        return <BookmarkIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format report type
  const formatReportType = (type: string) => {
    switch (type) {
      case 'performance':
        return 'Performance Report';
      case 'client':
        return 'Client Report';
      case 'time_slot':
        return 'Time Slot Report';
      case 'monthly':
        return 'Monthly Report';
      default:
        return type;
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!selectedReport) return;

    setIsDeleting(true);
    const success = await onDelete(selectedReport.id);
    setIsDeleting(false);

    if (success) {
      setIsDeleteDialogOpen(false);
      setSelectedReport(null);
    }
  };

  // Handle export
  const handleExport = async (report: SavedReport) => {
    setIsExporting(true);
    await onExport(report);
    setIsExporting(false);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between py-3 border-b"
            >
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-8 rounded" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (reports.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Saved Reports</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No saved reports found</p>
          <p className="text-sm text-muted-foreground mt-2">
            Generate a report and click "Save Report" to save it for future
            reference.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Saved Reports</CardTitle>
        </CardHeader>
        <CardContent className="space-y-1">
          {reports.map((report) => (
            <div
              key={report.id}
              className="flex items-center justify-between py-3 border-b last:border-0"
            >
              <div className="flex items-center gap-3">
                <div className="bg-muted p-2 rounded">
                  {getReportIcon(report.report_type)}
                </div>
                <div>
                  <h3 className="font-medium">{report.report_name}</h3>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{formatReportType(report.report_type)}</span>
                    <span>•</span>
                    <span>
                      {report.created_at
                        ? format(parseISO(report.created_at), 'MMM d, yyyy')
                        : 'Unknown date'}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleExport(report)}
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    setSelectedReport(report);
                    setIsDeleteDialogOpen(true);
                  }}
                >
                  <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Report</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this report? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="shadow_red"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { MonthlyTrend, ChartData } from '@/lib/types/database-modules';
import { AlertCircle } from 'lucide-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface MonthlyTrendChartProps {
  data: MonthlyTrend[] | null;
  loading?: boolean;
}

export function MonthlyTrendChart({
  data,
  loading = false,
}: MonthlyTrendChartProps) {
  const [chartData, setChartData] = useState<ChartData | null>(null);

  // Prepare chart data when data changes
  useEffect(() => {
    if (!data || data.length === 0) {
      setChartData(null);
      return;
    }

    // Format month labels
    const labels = data.map((item) => {
      const monthNames = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return `${monthNames[item.month - 1]} ${item.year}`;
    });

    // Prepare datasets
    const chartData: ChartData = {
      labels,
      datasets: [
        {
          label: 'Total',
          data: data.map((item) => item.total_consultations),
          backgroundColor: 'rgba(59, 130, 246, 0.5)', // blue
          borderColor: 'rgb(59, 130, 246)',
          borderWidth: 1,
        },
        {
          label: 'Completed',
          data: data.map((item) => item.completed_consultations),
          backgroundColor: 'rgba(16, 185, 129, 0.5)', // green
          borderColor: 'rgb(16, 185, 129)',
          borderWidth: 1,
        },
        {
          label: 'Cancelled',
          data: data.map((item) => item.cancelled_consultations),
          backgroundColor: 'rgba(239, 68, 68, 0.5)', // red
          borderColor: 'rgb(239, 68, 68)',
          borderWidth: 1,
        },
      ],
    };

    setChartData(chartData);
  }, [data]);

  // Chart options
  const options: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0,
        },
      },
    },
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0 || !chartData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Monthly Trend</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No monthly data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Trend</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <Bar options={options} data={chartData} />
        </div>
      </CardContent>
    </Card>
  );
}

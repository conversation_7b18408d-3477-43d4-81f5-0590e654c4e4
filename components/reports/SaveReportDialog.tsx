'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ReportType } from '@/lib/types/database-modules';
import { BookmarkPlus, Loader2 } from 'lucide-react';

interface SaveReportDialogProps {
  reportType: ReportType;
  onSave: (reportName: string) => Promise<boolean>;
}

export function SaveReportDialog({
  reportType,
  onSave,
}: SaveReportDialogProps) {
  const [open, setOpen] = useState(false);
  const [reportName, setReportName] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Get default report name based on type
  const getDefaultReportName = () => {
    const date = new Date();
    const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

    switch (reportType) {
      case 'performance':
        return `Performance Report - ${formattedDate}`;
      case 'client':
        return `Client Report - ${formattedDate}`;
      case 'time_slot':
        return `Time Slot Report - ${formattedDate}`;
      case 'monthly':
        return `Monthly Report - ${formattedDate}`;
      default:
        return `Report - ${formattedDate}`;
    }
  };

  // Handle dialog open
  const handleOpen = (open: boolean) => {
    setOpen(open);
    if (open) {
      setReportName(getDefaultReportName());
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!reportName.trim()) return;

    setIsSaving(true);
    const success = await onSave(reportName.trim());
    setIsSaving(false);

    if (success) {
      setOpen(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <BookmarkPlus className="h-4 w-4 mr-2" />
          Save Report
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Save Report</DialogTitle>
          <DialogDescription>
            Save this report for future reference. You can access saved reports
            from the Reports page.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="report-name">Report Name</Label>
            <Input
              id="report-name"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
              placeholder="Enter report name"
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!reportName.trim() || isSaving}
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Report'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Organization, SubscriptionLevel } from '@/types';
import { Building2, ChevronRight, Settings, Users } from 'lucide-react';
import Link from 'next/link';

interface OrganizationCardProps {
  organization: Organization;
  showActions?: boolean;
}

export function OrganizationCard({
  organization,
  showActions = true,
}: OrganizationCardProps) {
  const getSubscriptionBadge = (level: SubscriptionLevel) => {
    const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';
    switch (level) {
      case 'basic':
        return (
          <span className={`${baseClasses} bg-blue-100 text-blue-800`}>
            Basic
          </span>
        );
      case 'professional':
        return (
          <span className={`${baseClasses} bg-purple-100 text-purple-800`}>
            Professional
          </span>
        );
      case 'enterprise':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            Enterprise
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Building2 className="mr-2 h-5 w-5 text-primary" />
            {organization.name}
          </CardTitle>
          {getSubscriptionBadge(organization.subscriptionLevel)}
        </div>
        <CardDescription>
          {organization.teams.length} teams ·{' '}
          {organization.teams.reduce(
            (sum, team) => sum + team.members.length,
            0
          )}{' '}
          members
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-4">
          {organization.logoUrl && (
            <div className="flex justify-center mb-4">
              <img
                src={organization.logoUrl}
                alt={`${organization.name} logo`}
                className="h-16 w-16 rounded object-contain"
              />
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col space-y-1">
              <span className="text-xs text-muted-foreground">Teams</span>
              <div className="flex items-center">
                <Users className="mr-1.5 h-3.5 w-3.5 text-muted-foreground" />
                <span className="font-medium">{organization.teams.length}</span>
              </div>
            </div>
            <div className="flex flex-col space-y-1">
              <span className="text-xs text-muted-foreground">Features</span>
              <span className="font-medium">
                {organization.features.filter((f) => f.enabled).length} enabled
              </span>
            </div>
          </div>

          {organization.contactEmail && (
            <div className="text-sm">
              <span className="text-muted-foreground">Contact: </span>
              <a
                href={`mailto:${organization.contactEmail}`}
                className="text-primary hover:underline"
              >
                {organization.contactEmail}
              </a>
            </div>
          )}
        </div>
      </CardContent>
      {showActions && (
        <CardFooter className="flex flex-col space-y-2 pt-2">
          <div className="grid w-full grid-cols-2 gap-2">
            <Link href={`/organizations/${organization.id}`} passHref>
              <Button
                variant="outline"
                className="w-full justify-between"
                size="sm"
              >
                <span>View Details</span>
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
            <Link href={`/organizations/${organization.id}/settings`} passHref>
              <Button
                variant="outline"
                className="w-full justify-between"
                size="sm"
              >
                <span>Settings</span>
                <Settings className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </div>
          <Link href={`/organizations/${organization.id}/teams`} passHref>
            <Button className="w-full" size="sm">
              Manage Teams
            </Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  );
}

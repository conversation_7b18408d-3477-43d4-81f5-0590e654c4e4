'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import { Check, Loader2, Sparkles } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

// Define the plan types to match the database enum
type PlanType = 'Free' | 'Standard' | 'Enterprise';

interface Plan {
  id: PlanType;
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  features: string[];
  popular?: boolean;
}

interface PlanSelectionStepProps {
  onComplete: (plan: PlanType) => void;
  onPlanChange?: (plan: PlanType) => Promise<void>;
}

export function PlanSelectionStep({
  onComplete,
  onPlanChange,
}: PlanSelectionStepProps) {
  const { profile } = userStore();
  const [selectedPlan, setSelectedPlan] = useState<PlanType>(
    (profile?.plan as PlanType) || 'free'
  );
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>(
    'monthly'
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define the plans to match the database enum
  const plans: Plan[] = [
    {
      id: 'Free',
      name: 'Free',
      description: 'Basic document management for individuals',
      monthlyPrice: 0,
      yearlyPrice: 0,
      features: [
        'Create up to 5 documents',
        'Access to 3 basic templates',
        'Basic document editing',
        'Document export as PDF',
        'Document organization with tags',
      ],
    },
    {
      id: 'Standard',
      name: 'Standard',
      description: 'Enhanced features for professionals',
      monthlyPrice: 19.99,
      yearlyPrice: 191.9, // 20% discount for yearly
      features: [
        'Unlimited document creation',
        'Access to 15 professional templates',
        'Advanced document editing',
        'Document version history (10 versions)',
        'Document sharing with basic permissions',
        'Document storage up to 2GB',
        'View lawyer profiles',
        'Book up to 2 consultations per month (pay-per-use)',
      ],
      popular: true,
    },
    {
      id: 'Enterprise',
      name: 'Enterprise',
      description: 'Complete solution for large organizations',
      monthlyPrice: 99.99,
      yearlyPrice: 959.9, // 20% discount for yearly
      features: [
        'Everything in Standard, plus:',
        'Unlimited access to all templates',
        'Enterprise-grade document security',
        'Document storage up to 100GB',
        'Custom workflow creation',
        'Dedicated lawyer access',
        'Unlimited consultations',
        'Unlimited collaboration projects',
        '24/7 priority support',
        'Ability to register as a lawyer',
        'Custom template creation',
      ],
    },
  ];

  const handleSelectPlan = async (plan: PlanType) => {
    setSelectedPlan(plan);

    // If onPlanChange is provided, save the plan to the database
    if (onPlanChange) {
      try {
        await onPlanChange(plan);
      } catch (error) {
        console.error('Error saving plan:', error);
      }
    }
  };

  const handleContinue = async () => {
    setIsSubmitting(true);
    try {
      // Simulate a delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 500));
      onComplete(selectedPlan);
    } catch (error) {
      console.error('Error selecting plan:', error);
      toast.error('Failed to select plan');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">Choose Your Plan</h1>
        <p className="text-neutral-500">
          Select a subscription plan that fits your needs.
        </p>
      </div>

      <Tabs
        defaultValue="monthly"
        value={billingCycle}
        onValueChange={(value) =>
          setBillingCycle(value as 'monthly' | 'yearly')
        }
        className="w-full"
      >
        <div className="flex justify-center mb-6">
          <TabsList className="grid w-[400px] grid-cols-2">
            <TabsTrigger value="monthly">Monthly Billing</TabsTrigger>
            <TabsTrigger value="yearly">
              Yearly Billing{' '}
              <Badge className="ml-2 bg-green-100 text-green-800">
                Save 20%
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="monthly" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={cn(
                  'relative cursor-pointer transition-all hover:border-primary',
                  selectedPlan === plan.id
                    ? 'border-2 border-primary'
                    : 'border'
                )}
                onClick={() => handleSelectPlan(plan.id)}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-white flex items-center gap-1">
                      <Sparkles className="h-3 w-3" />
                      Popular
                    </Badge>
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <span className="text-3xl font-bold">
                      $
                      {billingCycle === 'monthly'
                        ? plan.monthlyPrice
                        : plan.yearlyPrice}
                    </span>
                    <span className="text-muted-foreground">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  </div>
                  <ul className="space-y-2 text-sm">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-500 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    variant={selectedPlan === plan.id ? 'default' : 'outline'}
                    className="w-full"
                    onClick={() => handleSelectPlan(plan.id)}
                  >
                    {selectedPlan === plan.id ? 'Selected' : 'Select Plan'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="yearly" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={cn(
                  'relative cursor-pointer transition-all hover:border-primary',
                  selectedPlan === plan.id
                    ? 'border-2 border-primary'
                    : 'border'
                )}
                onClick={() => handleSelectPlan(plan.id)}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-white flex items-center gap-1">
                      <Sparkles className="h-3 w-3" />
                      Popular
                    </Badge>
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <span className="text-3xl font-bold">
                      $
                      {billingCycle === 'monthly'
                        ? plan.monthlyPrice
                        : plan.yearlyPrice}
                    </span>
                    <span className="text-muted-foreground">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  </div>
                  <ul className="space-y-2 text-sm">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Check className="h-4 w-4 text-green-500 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    variant={selectedPlan === plan.id ? 'default' : 'outline'}
                    className="w-full"
                    onClick={() => handleSelectPlan(plan.id)}
                  >
                    {selectedPlan === plan.id ? 'Selected' : 'Select Plan'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={handleContinue} disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>Continue with {plans.find((p) => p.id === selectedPlan)?.name}</>
          )}
        </Button>
      </div>
    </div>
  );
}

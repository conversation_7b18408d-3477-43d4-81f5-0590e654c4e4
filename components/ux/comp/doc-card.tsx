'use client';
import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'motion/react';

const decorativeElementVariants = {
  initial: {
    width: 30,
    height: 30,
  },
  hover: {
    width: 50,
    height: 50,
  },
};

const cornerElementVariants = {
  initial: {
    width: 28,
    height: 28,
  },
  hover: {
    width: 42,
    height: 42,
    boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.05)',
  },
};

export function DocCard({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className="mx-auto w-full">
      <motion.div
        initial="initial"
        whileHover="hover"
        className={cn(
          'relative z-10 mt-0 block h-full w-full overflow-hidden',
          'rounded-lg bg-white shadow-[inset_0_0_0_1px] shadow-gray-200',
          className
        )}
      >
        <motion.div
          variants={cornerElementVariants}
          transition={{ duration: 0.18 }}
          className="absolute top-0 right-0 -translate-y-2 translate-x-2 rounded-bl-lg border bg-gray-50"
        />
        <motion.div
          variants={decorativeElementVariants}
          transition={{ duration: 0.18 }}
          className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 rotate-45 bg-gray-50 shadow-[0_1px_0_0_] shadow-gray-200"
        />
        <div>{children}</div>
      </motion.div>
    </div>
  );
}

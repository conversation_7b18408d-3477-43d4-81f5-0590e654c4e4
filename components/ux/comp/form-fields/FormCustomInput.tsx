// Icons
import { Trash2 } from 'lucide-react';
import BaseButton from '../BaseButton';
import FormInput from './FormInput';

type FormCustomInputProps = {
  index: number;
  location: string;
  removeField: (index: number) => void;
};

const FormCustomInput = ({
  index,
  location,
  removeField,
}: FormCustomInputProps) => {
  const nameKey = `${location}[${index}].key`;
  const nameValue = `${location}[${index}].value`;
  return (
    <>
      <div className="flex flex-wrap items-end gap-3">
        <FormInput
          vertical
          name={nameKey}
          label={'Input Name'}
          placeholder="Name"
        />
        <FormInput
          vertical
          name={nameValue}
          label={'Input Value'}
          placeholder="Name"
        />
        <div className="mb-1 flex h-full justify-center">
          <BaseButton
            size="icon"
            variant="shadow_red"
            onClick={() => removeField(index)}
          >
            <Trash2 />
          </BaseButton>
        </div>
      </div>
    </>
  );
};

export default FormCustomInput;

'use client';

import { useFormContext } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
// Types
import { CurrencyType, NameType } from '@/types';
import { useCurrencies } from '@/lib/hooks';

type CurrencySelectorProps = {
  name: NameType;
  label?: string;
  placeholder?: string;
};

const CurrencySelector = ({
  name,
  label,
  placeholder,
}: CurrencySelectorProps) => {
  const { control } = useFormContext();

  const { currencies, currenciesLoading } = useCurrencies();

  return (
    <div>
      <FormField
        control={control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <div className="flex flex-col items-center justify-between gap-1 text-sm md:items-start">
              <FormLabel className="ml-2 text-sm text-neutral-400">
                {label}
              </FormLabel>
              <div>
                <Select
                  {...field}
                  defaultValue={field.value}
                  onValueChange={field.onChange}
                >
                  <FormControl>
                    <SelectTrigger className="w-fit max-w-[13rem]">
                      <SelectValue placeholder={placeholder} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent
                    style={{
                      overflowY: 'hidden',
                      height: '200px',
                    }}
                  >
                    <SelectGroup>
                      <SelectLabel>Currencies</SelectLabel>
                      {!currenciesLoading &&
                        currencies.map(
                          (currency: CurrencyType, idx: number) => (
                            <SelectItem key={idx} value={currency.code}>
                              {currency.name} {`(${currency.code})`}
                            </SelectItem>
                          )
                        )}
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <FormMessage />
              </div>
            </div>
          </FormItem>
        )}
      />
    </div>
  );
};

export default CurrencySelector;

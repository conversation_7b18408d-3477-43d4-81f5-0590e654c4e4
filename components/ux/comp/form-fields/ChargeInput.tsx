'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Percent, RefreshCw } from 'lucide-react';
import React from 'react';
import { useFormContext } from 'react-hook-form';

// Types
import { NameType } from '@/types';
import BaseButton from '../BaseButton';

type ChargeInputProps = {
  label: string;
  name: NameType;
  switchAmountType: (
    type: string,
    setType: React.Dispatch<React.SetStateAction<string>>
  ) => void;
  type: string;
  setType: React.Dispatch<React.SetStateAction<string>>;
  currency: string;
};

const ChargeInput = ({
  label,
  name,
  switchAmountType,
  type,
  setType,
  currency,
}: ChargeInputProps) => {
  const { control } = useFormContext();

  return (
    <>
      <div className="flex items-center justify-between">
        <div>{label}</div>

        <div className="flex items-center gap-1">
          <BaseButton
            variant="ghost"
            size="icon"
            onClick={() => switchAmountType(type, setType)}
          >
            <RefreshCw />
          </BaseButton>

          <FormField
            control={control}
            name={name}
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between text-sm">
                  <FormControl>
                    <Input
                      {...field}
                      className="w-[7rem]"
                      placeholder={label}
                      type="number"
                      min="0"
                      max="1000000"
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          {type == 'percentage' ? <Percent /> : <div>{currency}</div>}
        </div>
      </div>
    </>
  );
};

export default ChargeInput;

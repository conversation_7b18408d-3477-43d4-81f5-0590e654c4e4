import { cn } from '@/lib/utils';

export function GitHubEnhanced({ className }: { className?: string }) {
  return (
    <svg
      className={cn('h-full w-full', className)}
      viewBox="0 0 222 222"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="111" cy="111" r="111" fill="#0D1117" />
      <path
        d="M111.001 44C72.8976 44 42 74.7556 42 112.696C42 143.048 61.7708 168.798 89.1868 177.882C92.6352 178.518 93.9013 176.392 93.9013 174.577C93.9013 172.939 93.8373 167.528 93.8076 161.787C74.6115 165.943 70.5609 153.682 70.5609 153.682C67.4222 145.742 62.8997 143.63 62.8997 143.63C56.6393 139.367 63.3716 139.454 63.3716 139.454C70.3004 139.939 73.9488 146.534 73.9488 146.534C80.103 157.036 90.0906 154 94.0281 152.244C94.6474 147.804 96.4356 144.774 98.4089 143.058C83.0829 141.321 66.972 135.431 66.972 109.108C66.972 101.608 69.6674 95.4801 74.0814 90.6693C73.3649 88.939 71.0031 81.952 74.7498 72.4896C74.7498 72.4896 80.544 70.6433 93.7299 79.5312C99.2339 78.0091 105.137 77.2458 111.001 77.2196C116.864 77.2458 122.772 78.0091 128.286 79.5312C141.456 70.6433 147.242 72.4896 147.242 72.4896C150.998 81.952 148.635 88.939 147.919 90.6693C152.343 95.4801 155.02 101.608 155.02 109.108C155.02 135.493 138.878 141.303 123.513 143.004C125.988 145.136 128.194 149.316 128.194 155.725C128.194 164.917 128.114 172.315 128.114 174.577C128.114 176.405 129.356 178.547 132.853 177.873C160.254 168.779 180 143.038 180 112.696C180 74.7556 149.107 44 111.001 44ZM67.843 141.859C67.691 142.201 67.1517 142.303 66.6603 142.069C66.1599 141.844 65.8788 141.379 66.041 141.037C66.1896 140.685 66.73 140.587 67.2294 140.823C67.731 141.047 68.0166 141.517 67.843 141.859ZM71.237 144.874C70.9079 145.178 70.2646 145.037 69.8282 144.557C69.3768 144.078 69.2923 143.438 69.6259 143.129C69.9653 142.826 70.5892 142.968 71.0416 143.447C71.493 143.931 71.5809 144.567 71.237 144.874ZM73.5655 148.732C73.1427 149.024 72.4514 148.75 72.0241 148.139C71.6013 147.529 71.6013 146.796 72.0332 146.502C72.4617 146.209 73.1427 146.473 73.5758 147.079C73.9974 147.7 73.9974 148.433 73.5655 148.732ZM77.5034 153.2C77.1252 153.615 76.3196 153.504 75.73 152.937C75.1267 152.383 74.9588 151.597 75.3381 151.182C75.7209 150.766 76.531 150.883 77.1252 151.445C77.7239 151.998 77.9067 152.789 77.5034 153.2ZM82.5928 154.708C82.4259 155.246 81.6501 155.491 80.8685 155.262C80.0881 155.027 79.5774 154.397 79.7351 153.853C79.8973 153.311 80.6766 153.056 81.4639 153.301C82.2431 153.535 82.755 154.161 82.5928 154.708ZM88.3847 155.348C88.4041 155.915 87.7414 156.384 86.921 156.395C86.096 156.413 85.4287 155.954 85.4196 155.397C85.4196 154.825 86.0674 154.359 86.8924 154.346C87.7128 154.33 88.3847 154.785 88.3847 155.348ZM94.0746 155.131C94.1729 155.684 93.6027 156.251 92.788 156.403C91.987 156.548 91.2455 156.207 91.1438 155.659C91.0444 155.092 91.6248 154.525 92.4247 154.378C93.2405 154.237 93.9706 154.569 94.0746 155.131Z"
        fill="url(#paint0_radial_39_32)"
      />
      <defs>
        <radialGradient
          id="paint0_radial_39_32"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(42 44) rotate(44.1575) scale(192.354 192.312)"
        >
          <stop stopColor="white" stopOpacity="0.77" />
          <stop offset="1" stopColor="white" stopOpacity="0.1" />
        </radialGradient>
      </defs>
    </svg>
  );
}

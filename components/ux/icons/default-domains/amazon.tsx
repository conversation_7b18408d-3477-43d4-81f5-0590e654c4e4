import { cn } from '@/lib/utils';

export function Amazon({ className }: { className?: string }) {
  return (
    <svg
      className={cn('h-full w-full', className)}
      viewBox="0 0 222 222"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="111" cy="111" r="111" fill="url(#paint0_linear_39_35)" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M167.627 151.288C165.868 148.976 155.967 150.187 151.538 150.74C150.176 150.91 149.967 149.698 151.198 148.825C159.073 143.144 172.023 144.772 173.515 146.687C175.032 148.602 173.137 161.895 165.718 168.236C164.584 169.22 163.503 168.692 164.012 167.402C165.66 163.141 169.406 153.595 167.627 151.288ZM161.889 158.017C148.087 168.43 128.096 174 110.873 174C86.7384 174 65.0031 164.852 48.5549 149.615C47.2658 148.423 48.424 146.794 49.9749 147.72C67.7121 158.308 89.6704 164.668 112.331 164.668C127.607 164.668 144.438 161.43 159.888 154.686C162.233 153.683 164.186 156.257 161.894 158.002M120.018 102.131C120.018 108.758 120.187 114.27 116.989 120.141C114.415 124.911 110.325 127.849 105.765 127.849C99.5374 127.849 95.9221 122.889 95.9221 115.559C95.9221 101.093 108.304 98.4611 120.022 98.4611L120.022 102.136M136.379 143.493C135.317 144.496 133.766 144.574 132.555 143.91C127.18 139.231 126.211 137.055 123.24 132.585C114.343 142.092 108.057 144.932 96.5085 144.932C82.8614 144.932 72.2288 136.129 72.2288 118.497C72.2288 104.729 79.3673 95.3391 89.5105 90.753C98.2967 86.7002 110.601 85.9828 120.008 84.863L120.008 82.6669C120.008 78.6335 120.313 73.8632 118.06 70.3776C116.073 67.2508 112.288 65.9661 108.954 65.9661C102.765 65.9661 97.2548 69.2819 95.9124 76.1562C95.6265 77.6881 94.57 79.1861 93.0919 79.2637L77.361 77.5039C76.0379 77.1839 74.5792 76.0641 74.9379 73.9456C78.5725 54.0258 95.8009 48 111.207 48C119.102 48 129.419 50.1961 135.632 56.4449C143.527 64.153 142.771 74.4352 142.771 85.6289L142.771 112.064C142.771 120.019 145.911 123.5 148.887 127.8C149.929 129.318 150.156 131.169 148.848 132.309C145.533 135.227 139.65 140.564 136.412 143.575L136.354 143.517"
        fill="url(#paint1_radial_39_35)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_39_35"
          x1="0"
          y1="0"
          x2="222"
          y2="222"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF9900" />
          <stop offset="0.5" stopColor="#FF9900" />
          <stop offset="1" stopColor="#EA580C" />
        </linearGradient>
        <radialGradient
          id="paint1_radial_39_35"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(48 48) rotate(45) scale(178.191 178.229)"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="white" stopOpacity="0.5" />
        </radialGradient>
      </defs>
    </svg>
  );
}

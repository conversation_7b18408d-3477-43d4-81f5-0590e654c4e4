import { cn } from '@/lib/utils';

export function ChatGPT({ className }: { className?: string }) {
  return (
    <svg
      className={cn('h-full w-full', className)}
      viewBox="0 0 222 222"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="111" cy="111" r="111" fill="black" />
      <path
        d="M173.607 97.7454C176.886 87.8002 175.751 76.9142 170.492 67.8719C162.584 54.0098 146.691 46.8792 131.17 50.2298C122.423 40.4311 109.151 36.0738 96.3504 38.7981C83.5497 41.5224 73.1632 50.9148 69.1005 63.4398C58.9049 65.5455 50.1051 71.9745 44.9529 81.0819C36.9593 94.9215 38.7736 112.379 49.4395 124.252C46.1479 134.192 47.2727 145.08 52.5258 154.125C60.4432 167.992 76.3466 175.122 91.8764 171.767C98.784 179.601 108.708 184.058 119.11 183.999C135.02 184.014 149.114 173.67 153.974 158.414C164.168 156.304 172.967 149.876 178.122 140.771C186.019 126.956 184.197 109.593 173.607 97.7454ZM119.11 174.444C112.76 174.454 106.609 172.213 101.735 168.112L102.593 167.623L131.456 150.844C132.917 149.981 133.818 148.406 133.827 146.7V105.717L146.03 112.826C146.152 112.889 146.237 113.006 146.258 113.143V147.103C146.227 162.19 134.091 174.412 119.11 174.444ZM60.756 149.348C57.5714 143.81 56.428 137.318 57.5268 131.015L58.3841 131.533L87.2755 148.312C88.7311 149.172 90.535 149.172 91.9907 148.312L127.283 127.82V142.009C127.277 142.158 127.203 142.296 127.083 142.383L97.849 159.363C84.8581 166.9 68.2607 162.419 60.756 149.348ZM53.1545 86.032C56.3612 80.4583 61.4227 76.207 67.443 74.0308V108.567C67.4209 110.266 68.3188 111.843 69.7863 112.682L104.907 133.087L92.7051 140.196C92.5711 140.267 92.4105 140.267 92.2764 140.196L63.0993 123.244C50.134 115.676 45.6875 98.9734 53.1545 85.8881V86.032ZM153.403 109.488L118.167 88.8812L130.341 81.8013C130.475 81.7297 130.636 81.7297 130.77 81.8013L159.947 98.7815C169.053 104.074 174.307 114.185 173.432 124.737C172.557 135.289 165.712 144.383 155.86 148.082V113.546C155.809 111.851 154.875 110.309 153.403 109.488ZM165.548 91.0973L164.691 90.5792L135.856 73.6566C134.392 72.7912 132.577 72.7912 131.113 73.6566L95.8486 94.1479V79.9594C95.8333 79.8125 95.8991 79.6689 96.02 79.5853L125.197 62.6339C134.326 57.3376 145.672 57.8319 154.312 63.9022C162.953 69.9726 167.331 80.5253 165.548 90.9821V91.0973ZM89.1901 116.251L76.9877 109.171C76.8644 109.096 76.7807 108.969 76.7591 108.826V74.9517C76.773 64.3439 82.8721 54.6994 92.4112 50.201C101.95 45.7026 113.211 47.1605 121.311 53.9424L120.453 54.4316L91.5906 71.2103C90.1293 72.0736 89.2279 73.6485 89.2187 75.3546L89.1901 116.251ZM95.82 101.861L111.537 92.7377L127.283 101.861V120.107L111.595 129.231L95.8486 120.107L95.82 101.861Z"
        fill="url(#paint0_radial_39_26)"
      />
      <defs>
        <radialGradient
          id="paint0_radial_39_26"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(40 38) rotate(45.5947) scale(204.365 204.365)"
        >
          <stop stopColor="white" stopOpacity="0.85" />
          <stop offset="1" stopColor="white" stopOpacity="0.1" />
        </radialGradient>
      </defs>
    </svg>
  );
}

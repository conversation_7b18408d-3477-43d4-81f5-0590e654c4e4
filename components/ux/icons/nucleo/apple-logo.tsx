import { SVGProps } from "react";

export function AppleLogo(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      height="24"
      width="24"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g
        fill="currentColor"
        strokeLinecap="square"
        strokeLinejoin="miter"
        strokeMiterlimit="10"
      >
        <path
          d="M20.1407 16.7842C19.676 17.8035 19.4543 18.2585 18.8551 19.1592C18.0214 20.417 16.8442 21.9835 15.3865 21.997C14.091 22.0092 13.758 21.1627 12 21.172C10.242 21.1813 9.87505 22.012 8.57954 21.9999C7.12182 21.987 6.00764 20.572 5.17254 19.3135C2.83863 15.7957 2.59497 11.6678 4.03427 9.47356C5.05708 7.91357 6.67134 7.00144 8.18855 7.00144C9.73339 7.00144 10.7045 7.84 11.9823 7.84C13.2218 7.84 13.9762 7.00001 15.7633 7.00001C17.1133 7.00001 18.5441 7.72786 19.5634 8.9857C16.2237 10.7993 16.7663 15.5214 20.1407 16.7842Z"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        />
        <path
          d="M15.3652 3.793C16.0815 2.87425 16.5702 1.63176 16.373 0.305766C15.203 0.386016 13.89 1.07576 13.0912 2.0455C12.3667 2.92525 11.7675 4.231 12 5.5C13.2765 5.53975 14.598 4.77775 15.3652 3.793Z"
          fill="currentColor"
          stroke="none"
        />
      </g>
    </svg>
  );
}

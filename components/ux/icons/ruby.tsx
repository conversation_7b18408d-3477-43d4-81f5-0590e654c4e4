import { SVGProps } from "react";

export function Ruby(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 199 198"
      fill="none"
      {...props}
    >
      <g fillRule="evenodd" clipPath="url(#a)" clipRule="evenodd">
        <path
          fill="#871101"
          fillOpacity={0.8}
          d="M153.5 130.41 40.38 197.58l146.469-9.939L198.13 39.95l-44.63 90.46Z"
        />
        <path
          fill="#871101"
          d="M187.089 187.54 174.5 100.65l-34.291 45.28 46.88 41.61Z"
        />
        <path
          fill="url(#b)"
          d="M187.259 187.54 95.03 180.3l-54.16 17.091 146.389-9.851Z"
        />
        <path
          fill="url(#c)"
          d="m41 197.41 23.04-75.48-50.7 10.841L41 197.41Z"
        />
        <path
          fill="url(#d)"
          d="M140.2 146.18 119 63.14l-60.67 56.87 81.87 26.17Z"
        />
        <path
          fill="url(#e)"
          d="m193.32 64.31-57.35-46.84L120 69.1l73.32-4.79Z"
        />
        <path fill="url(#f)" d="m166.5.77-33.73 18.64L111.49.52l55.01.25Z" />
        <path fill="url(#g)" d="m0 158.09 14.13-25.77-11.43-30.7L0 158.09Z" />
        <path
          fill="#fff"
          d="m1.94 100.65 11.5 32.62 49.97-11.211 57.05-53.02 16.1-51.139L111.209 0l-43.1 16.13c-13.58 12.63-39.93 37.62-40.88 38.09-.94.48-17.4 31.59-25.29 46.43Z"
        />
        <path
          fill="url(#h)"
          d="M42.32 42.05c29.43-29.18 67.37-46.42 81.93-31.73 14.551 14.69-.88 50.39-30.31 79.56s-66.9 47.36-81.45 32.67c-14.56-14.68.4-51.33 29.83-80.5Z"
        />
        <path
          fill="url(#i)"
          d="m41 197.38 22.86-75.72 75.92 24.39c-27.45 25.74-57.98 47.5-98.78 51.33Z"
        />
        <path
          fill="url(#j)"
          d="m120.56 68.89 19.49 77.2c22.93-24.11 43.51-50.03 53.589-82.09l-73.079 4.89Z"
        />
        <path
          fill="url(#k)"
          d="M193.44 64.39c7.8-23.54 9.6-57.31-27.181-63.58l-30.18 16.67 57.361 46.91Z"
        />
        <path
          fill="#9E1209"
          d="M0 157.75c1.08 38.851 29.11 39.43 41.05 39.771L13.47 133.11 0 157.75Z"
        />
        <path
          fill="url(#l)"
          d="M120.669 69.01c17.62 10.83 53.131 32.58 53.851 32.98 1.119.63 15.31-23.93 18.53-37.81l-72.381 4.83Z"
        />
        <path
          fill="url(#m)"
          d="m63.83 121.66 30.56 58.96c18.07-9.8 32.22-21.74 45.18-34.53l-75.74-24.43Z"
        />
        <path
          fill="url(#n)"
          d="m13.35 133.19-4.33 51.56c8.17 11.16 19.41 12.13 31.2 11.26-8.53-21.23-25.57-63.68-26.87-62.82Z"
        />
        <path
          fill="url(#o)"
          d="m135.9 17.61 60.71 8.52C193.37 12.4 183.42 3.54 166.46.77L135.9 17.61Z"
        />
      </g>
      <defs>
        <linearGradient
          id="b"
          x1={151.795}
          x2={97.93}
          y1={217.785}
          y2={181.638}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#871101" />
          <stop offset={0.99} stopColor="#911209" />
          <stop offset={1} stopColor="#911209" />
        </linearGradient>
        <linearGradient
          id="c"
          x1={38.696}
          x2={47.047}
          y1={127.391}
          y2={181.661}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#fff" />
          <stop offset={0.23} stopColor="#E57252" />
          <stop offset={0.46} stopColor="#DE3B20" />
          <stop offset={0.99} stopColor="#A60003" />
          <stop offset={1} stopColor="#A60003" />
        </linearGradient>
        <linearGradient
          id="d"
          x1={96.133}
          x2={99.21}
          y1={76.715}
          y2={132.102}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#fff" />
          <stop offset={0.23} stopColor="#E4714E" />
          <stop offset={0.56} stopColor="#BE1A0D" />
          <stop offset={0.99} stopColor="#A80D00" />
          <stop offset={1} stopColor="#A80D00" />
        </linearGradient>
        <linearGradient
          id="e"
          x1={147.103}
          x2={156.314}
          y1={25.521}
          y2={65.216}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#fff" />
          <stop offset={0.18} stopColor="#E46342" />
          <stop offset={0.4} stopColor="#C82410" />
          <stop offset={0.99} stopColor="#A80D00" />
          <stop offset={1} stopColor="#A80D00" />
        </linearGradient>
        <linearGradient
          id="f"
          x1={118.976}
          x2={158.669}
          y1={11.541}
          y2={-8.305}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#fff" />
          <stop offset={0.54} stopColor="#C81F11" />
          <stop offset={0.99} stopColor="#BF0905" />
          <stop offset={1} stopColor="#BF0905" />
        </linearGradient>
        <linearGradient
          id="g"
          x1={3.903}
          x2={7.17}
          y1={113.555}
          y2={146.263}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#fff" />
          <stop offset={0.31} stopColor="#DE4024" />
          <stop offset={0.99} stopColor="#BF190B" />
          <stop offset={1} stopColor="#BF190B" />
        </linearGradient>
        <linearGradient
          id="h"
          x1={-18.556}
          x2={135.015}
          y1={155.104}
          y2={-2.809}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BD0012" />
          <stop offset={0.07} stopColor="#fff" />
          <stop offset={0.17} stopColor="#fff" />
          <stop offset={0.27} stopColor="#C82F1C" />
          <stop offset={0.33} stopColor="#820C01" />
          <stop offset={0.46} stopColor="#A31601" />
          <stop offset={0.72} stopColor="#B31301" />
          <stop offset={0.99} stopColor="#E82609" />
          <stop offset={1} stopColor="#E82609" />
        </linearGradient>
        <linearGradient
          id="i"
          x1={99.075}
          x2={52.818}
          y1={171.033}
          y2={159.617}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#8C0C01" />
          <stop offset={0.54} stopColor="#990C00" />
          <stop offset={0.99} stopColor="#A80D0E" />
          <stop offset={1} stopColor="#A80D0E" />
        </linearGradient>
        <linearGradient
          id="j"
          x1={178.526}
          x2={137.433}
          y1={115.515}
          y2={78.684}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#7E110B" />
          <stop offset={0.99} stopColor="#9E0C00" />
          <stop offset={1} stopColor="#9E0C00" />
        </linearGradient>
        <linearGradient
          id="k"
          x1={193.623}
          x2={173.154}
          y1={47.937}
          y2={26.054}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#79130D" />
          <stop offset={0.99} stopColor="#9E120B" />
          <stop offset={1} stopColor="#9E120B" />
        </linearGradient>
        <linearGradient
          id="n"
          x1={26.67}
          x2={9.989}
          y1={197.336}
          y2={140.742}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#8B2114" />
          <stop offset={0.43} stopColor="#9E100A" />
          <stop offset={0.99} stopColor="#B3100C" />
          <stop offset={1} stopColor="#B3100C" />
        </linearGradient>
        <linearGradient
          id="o"
          x1={154.641}
          x2={192.039}
          y1={9.798}
          y2={26.306}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#B31000" />
          <stop offset={0.44} stopColor="#910F08" />
          <stop offset={0.99} stopColor="#791C12" />
          <stop offset={1} stopColor="#791C12" />
        </linearGradient>
        <radialGradient
          id="l"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(143.831 79.388) scale(50.3576)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#A80D00" />
          <stop offset={0.99} stopColor="#7E0E08" />
          <stop offset={1} stopColor="#7E0E08" />
        </radialGradient>
        <radialGradient
          id="m"
          cx={0}
          cy={0}
          r={1}
          gradientTransform="translate(74.092 145.751) scale(66.9437)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#A30C00" />
          <stop offset={0.99} stopColor="#800E08" />
          <stop offset={1} stopColor="#800E08" />
        </radialGradient>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h198.13v197.58H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

import { SVGProps } from "react";

export function CashApp(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill="#00D64B"
        fillRule="evenodd"
        d="M13.696.273C12.83 0 12.028 0 10.421 0H5.578c-1.606 0-2.41 0-3.274.273a3.4 3.4 0 0 0-2.03 2.031C0 3.17 0 3.972 0 5.578v4.844c0 1.606 0 2.409.273 3.273a3.4 3.4 0 0 0 2.031 2.032C3.17 16 3.972 16 5.578 16h4.844c1.606 0 2.409 0 3.273-.273a3.4 3.4 0 0 0 2.032-2.032c.273-.864.273-1.667.273-3.274V5.579c0-1.606 0-2.41-.273-3.274a3.4 3.4 0 0 0-2.032-2.03M10.327 6.15a3.1 3.1 0 0 0-2-.744c-.605 0-1.21.217-1.21.765 0 .519.553.718 1.228.962l.17.061c1.313.448 2.394.995 2.394 2.293 0 1.41-1.085 2.374-2.858 2.484l-.16.766a.3.3 0 0 1-.298.24l-1.116-.008a.302.302 0 0 1-.293-.365l.17-.797a4.1 4.1 0 0 1-1.792-.999.304.304 0 0 1-.002-.434l.619-.61a.3.3 0 0 1 .423 0c.598.59 1.37.833 2.106.833.807 0 1.355-.331 1.355-.894 0-.495-.448-.66-1.302-.976l-.284-.105c-1.096-.39-2.134-.952-2.134-2.264 0-1.516 1.255-2.255 2.736-2.329l.162-.765a.3.3 0 0 1 .296-.24h1.114c.191 0 .334.177.295.365l-.18.86c.638.21 1.158.537 1.544.87a.3.3 0 0 1 .014.438l-.576.584a.304.304 0 0 1-.42.009"
        clipRule="evenodd"
      ></path>
    </svg>
  );
}

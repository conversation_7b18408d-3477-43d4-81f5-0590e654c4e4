import { SVGProps } from "react";

export function Typescript(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 128 128"
      fill="none"
      {...props}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={128}
        height={128}
        fill="none"
        {...props}
      >
        <path
          fill="currentColor"
          fillRule="evenodd"
          d="M6 0h116a6 6 0 0 1 6 6v116a6 6 0 0 1-6 6H6a6 6 0 0 1-6-6V6a6 6 0 0 1 6-6Zm68.262 113.494V99.468c2.535 2.133 5.288 3.733 8.26 4.799 2.97 1.067 5.971 1.6 9.001 1.6 1.778 0 3.329-.161 4.654-.482 1.326-.321 2.433-.767 3.322-1.337.888-.57 1.551-1.242 1.988-2.016a5.04 5.04 0 0 0 .655-2.52c0-1.228-.349-2.323-1.049-3.287-.699-.965-1.653-1.856-2.862-2.674-1.209-.818-2.644-1.607-4.304-2.367-1.66-.76-3.452-1.534-5.375-2.323-4.895-2.045-8.543-4.544-10.947-7.495C75.202 78.415 74 74.85 74 70.672c0-3.273.656-6.085 1.966-8.438a16.732 16.732 0 0 1 5.354-5.807c2.257-1.52 4.872-2.637 7.844-3.353C92.135 52.358 95.28 52 98.603 52c3.263 0 6.154.197 8.674.592 2.52.394 4.843 1 6.97 1.819v13.105a21.071 21.071 0 0 0-3.43-1.929 27.158 27.158 0 0 0-3.824-1.38 29.09 29.09 0 0 0-3.911-.811 27.607 27.607 0 0 0-3.693-.263c-1.602 0-3.059.153-4.37.46-1.31.307-2.418.738-3.32 1.293-.904.555-1.603 1.22-2.098 1.994-.496.775-.743 1.644-.743 2.608 0 1.052.276 1.995.83 2.827.553.833 1.34 1.622 2.36 2.367 1.02.745 2.257 1.476 3.714 2.192a84.756 84.756 0 0 0 4.938 2.213c2.506 1.052 4.756 2.17 6.752 3.353 1.995 1.183 3.707 2.52 5.134 4.01a15.632 15.632 0 0 1 3.278 5.107c.757 1.914 1.136 4.142 1.136 6.684 0 3.506-.663 6.45-1.988 8.831a16.179 16.179 0 0 1-5.397 5.786c-2.273 1.476-4.916 2.535-7.932 3.178-3.015.643-6.198.964-9.548.964-3.438 0-6.708-.292-9.81-.877-3.103-.584-5.79-1.461-8.063-2.629ZM69 64.554H50.703V116H36.208V64.554H18V53h51v11.554Z"
          clipRule="evenodd"
        />
      </svg>
    </svg>
  );
}

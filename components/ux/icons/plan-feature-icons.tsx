import {
  Bolt,
  Calendar6,
  ChartLine,
  ConnectedDots4,
  CreditCard,
  CursorRays,
  Flask,
  Folder,
  Gear3,
  Gift,
  Globe,
  Hyperlink,
  InvoiceDollar,
  License,
  MarketingTarget,
  PaperPlane,
  PercentageArrowDown,
  ShieldKeyhole,
  Sparkle3,
  SquareLayoutGrid5,
  UserCrown,
  Users2,
  UsersSettings,
  Versions2,
  Webhook,
} from "./nucleo";
import { Slack } from "./slack";

export const PLAN_FEATURE_ICONS = {
  clicks: CursorRays,
  links: Hyperlink,
  retention: Calendar6,
  sales: InvoiceDollar,
  domains: Globe,
  users: Users2,
  analytics: ChartLine,
  ai: Sparkle3,
  advanced: Gear3,
  folders: Folder,
  dotlink: Gift,
  deeplinks: MarketingTarget,
  events: Bolt,
  webhooks: Webhook,
  partners: ConnectedDots4,
  payouts: CreditCard,
  roles: UsersSettings,
  slack: Slack,
  tests: Flask,
  whitelabel: SquareLayoutGrid5,
  email: PaperPlane,
  volume: PercentageArrowDown,
  sso: ShieldKeyhole,
  sla: License,
  logs: Versions2,
  success: User<PERSON>rown,
};

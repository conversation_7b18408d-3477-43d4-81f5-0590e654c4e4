import { SVGProps } from "react";

export function Php(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="130"
      height="62"
      viewBox="0 0 130 62"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path
          d="M26.0001 20.7441C27.4318 20.5641 28.8849 20.6696 30.2755 21.0545C31.6661 21.4394 32.9666 22.096 34.102 22.9865C34.8345 24.1149 35.3016 25.3947 35.468 26.7296C35.6345 28.0646 35.496 29.4199 35.063 30.6936C34.6832 33.8292 33.1667 36.7166 30.8009 38.8092C27.9478 40.647 24.5751 41.5089 21.1901 41.2653H14.6901L18.6829 20.7859L26.0001 20.7441ZM0 62H10.6786L13.209 49.0002H22.3415C25.706 49.0858 29.0636 48.6521 32.2959 47.7141C34.9505 46.8377 37.3769 45.3819 39.3995 43.452C42.8974 40.3066 45.255 36.0912 46.1038 31.4643C46.7738 29.1614 46.8918 26.733 46.4481 24.376C46.0044 22.019 45.0117 19.7997 43.5502 17.898C41.7883 16.152 39.6664 14.8116 37.3329 13.9704C34.9993 13.1292 32.5102 12.8075 30.0394 13.0277H9.5504L0 62Z"
          fill="#484c88"
        />
        <path
          d="M26.0001 20.7441C27.4318 20.5641 28.8849 20.6696 30.2755 21.0545C31.6661 21.4394 32.9666 22.096 34.102 22.9865C34.8345 24.1149 35.3016 25.3947 35.468 26.7296C35.6345 28.0646 35.496 29.4199 35.063 30.6936C34.6832 33.8292 33.1667 36.7166 30.8009 38.8092C27.9478 40.647 24.5751 41.5089 21.1901 41.2653H14.6901L18.6829 20.7859L26.0001 20.7441ZM0 62H10.6786L13.209 49.0002H22.3415C25.706 49.0858 29.0636 48.6521 32.2959 47.7141C34.9505 46.8377 37.3769 45.3819 39.3995 43.452C42.8974 40.3066 45.255 36.0912 46.1038 31.4643C46.7738 29.1614 46.8918 26.733 46.4481 24.376C46.0044 22.019 45.0117 19.7997 43.5502 17.898C41.7883 16.152 39.6664 14.8116 37.3329 13.9704C34.9993 13.1292 32.5102 12.8075 30.0394 13.0277H9.5504L0 62Z"
          fill="#484c88"
        />
        <path
          d="M53.9367 0H64.5364L61.9689 12.9998H71.3986C75.7309 12.5462 80.0834 13.6471 83.679 16.1059C84.8687 17.4583 85.682 19.0997 86.0373 20.8655C86.3926 22.6313 86.2774 24.4595 85.7033 26.1668L81.274 48.9583H70.5489L74.7647 27.2904C75.058 26.4637 75.1627 25.5818 75.0711 24.7094C74.9795 23.837 74.694 22.9961 74.2354 22.2483C72.6985 21.1456 70.7989 20.6707 68.9239 20.9205H60.46L55.0046 48.9816H44.3817L53.9367 0Z"
          fill="url(#paint2_linear_6002_28760)"
        />
        <path
          d="M53.9367 0H64.5364L61.9689 12.9998H71.3986C75.7309 12.5462 80.0834 13.6471 83.679 16.1059C84.8687 17.4583 85.682 19.0997 86.0373 20.8655C86.3926 22.6313 86.2774 24.4595 85.7033 26.1668L81.274 48.9583H70.5489L74.7647 27.2904C75.058 26.4637 75.1627 25.5818 75.0711 24.7094C74.9795 23.837 74.694 22.9961 74.2354 22.2483C72.6985 21.1456 70.7989 20.6707 68.9239 20.9205H60.46L55.0046 48.9816H44.3817L53.9367 0Z"
          fill="#484c88"
        />
        <path
          d="M109.289 20.744C110.72 20.564 112.173 20.6695 113.564 21.0544C114.955 21.4393 116.255 22.096 117.39 22.9865C118.123 24.1148 118.59 25.3946 118.757 26.7296C118.923 28.0645 118.784 29.4198 118.351 30.6935C117.972 33.8291 116.455 36.7166 114.089 38.8092C111.231 40.6509 107.851 41.513 104.46 41.2652H97.9646L101.948 20.7626L109.289 20.744ZM83.2885 62H93.9671L96.4975 49.0001H105.635C109.005 49.0877 112.369 48.6539 115.608 47.7141C118.262 46.8377 120.689 45.3818 122.711 43.452C126.201 40.3032 128.55 36.0881 129.392 31.4642C130.062 29.1613 130.18 26.733 129.737 24.376C129.293 22.019 128.3 19.7997 126.839 17.898C125.076 16.1551 122.954 14.8176 120.622 13.9788C118.289 13.1401 115.802 12.8202 113.333 13.0416H92.811L83.2885 62Z"
          fill="url(#paint4_linear_6002_28760)"
        />
        <path
          d="M109.289 20.744C110.72 20.564 112.173 20.6695 113.564 21.0544C114.955 21.4393 116.255 22.096 117.39 22.9865C118.123 24.1148 118.59 25.3946 118.757 26.7296C118.923 28.0645 118.784 29.4198 118.351 30.6935C117.972 33.8291 116.455 36.7166 114.089 38.8092C111.231 40.6509 107.851 41.513 104.46 41.2652H97.9646L101.948 20.7626L109.289 20.744ZM83.2885 62H93.9671L96.4975 49.0001H105.635C109.005 49.0877 112.369 48.6539 115.608 47.7141C118.262 46.8377 120.689 45.3818 122.711 43.452C126.201 40.3032 128.55 36.0881 129.392 31.4642C130.062 29.1613 130.18 26.733 129.737 24.376C129.293 22.019 128.3 19.7997 126.839 17.898C125.076 16.1551 122.954 14.8176 120.622 13.9788C118.289 13.1401 115.802 12.8202 113.333 13.0416H92.811L83.2885 62Z"
          fill="#484c88"
        />
      </g>
    </svg>
  );
}

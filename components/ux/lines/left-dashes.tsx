export function LeftDashes() {
  return (
    <div
      className='absolute inset-y-0 left-0  w-px'
      style={{
        maskImage:
          'linear-gradient(transparent, white 5rem, white calc(100% - 5rem), transparent)',
      }}
    >
      <svg className='h-full w-full' preserveAspectRatio='none'>
        <line
          x1='0'
          y1='0'
          x2='0'
          y2='100%'
          className='stroke-gray-300'
          strokeWidth='2'
          strokeDasharray='3 3'
        />
      </svg>
    </div>
  );
}

export function LeftDashesWithPercentages({
  percentage = 25,
}: {
  percentage?: number;
}) {
  return (
    <div
      className='absolute inset-y-0 -z-10 my-[-5rem] hidden w-px sm:block'
      style={
        {
          '--left-offset': `${percentage}%`,
          left: 'var(--left-offset)',
          maskImage:
            'linear-gradient(transparent, white 5rem, white calc(100% - 5rem), transparent)',
        } as React.CSSProperties
      }
    >
      <svg className='h-full w-full' preserveAspectRatio='none'>
        <line
          x1='0'
          y1='0'
          x2='0'
          y2='100%'
          className='stroke-gray-300'
          strokeWidth='2'
          strokeDasharray='3 3'
        />
      </svg>
    </div>
  );
}

export function LeftDashesWithOffset({ offset = '4rem' }: { offset?: string }) {
  return (
    <div
      className='absolute inset-y-0 -z-10 w-px block'
      style={
        {
          '--left-offset': offset,
          left: 'var(--left-offset)',
          maskImage:
            'linear-gradient(transparent, white 5rem, white calc(100% - 5rem), transparent)',
        } as React.CSSProperties
      }
    >
      <svg className='h-full w-full' preserveAspectRatio='none'>
        <line
          x1='0'
          y1='0'
          x2='0'
          y2='100%'
          className='stroke-gray-300'
          strokeWidth='2'
          strokeDasharray='3 3'
        />
      </svg>
    </div>
  );
}

export function LeftDashedBg() {
  return (
    <svg
      className='w-2 absolute h-full inset-y-0 left-0 border-y border-dashed border-gray-300 stroke-gray-300'
      // style={{
      //   maskImage:
      //     'linear-gradient(transparent, white 10rem, white calc(100% - 10rem), transparent)',
      // }}
    >
      <defs>
        <pattern
          id='diagonal-footer-pattern'
          patternUnits='userSpaceOnUse'
          width='64'
          height='64'
        >
          {Array.from({ length: 17 }, (_, i) => {
            const offset = i * 8;
            return (
              <path
                key={i}
                d={`M${-106 + offset} 110L${22 + offset} -18`}
                stroke=''
                strokeWidth='1'
              />
            );
          })}
        </pattern>
      </defs>
      <rect
        stroke='none'
        width='100%'
        height='100%'
        fill='url(#diagonal-footer-pattern)'
      />
    </svg>
  );
}

'use client';
import { motion } from 'motion/react';

export const DistortedGlass = () => {
  return (
    <>
      <div className='relative h-[240px] w-full overflow-hidden'>
        <motion.div
          drag
          initial={{ x: 100, y: 100 }}
          className='h-24 w-24 rounded-[4px]'
          style={{
            background: 'linear-gradient(190deg, #BAA7FF 10%, #7D66D9 90%)',
          }}
        />
        <div className='pointer-events-none absolute bottom-0 left-0 z-10 h-1/2 w-1/2 overflow-hidden rounded-[4px] border border-[#f5f5f51a]'>
          <div className='glass-effect h-full w-full' />
        </div>
        <svg>
          <defs>
            <filter id='fractal-noise-glass'>
              <feTurbulence
                type='fractalNoise'
                baseFrequency='0.12 0.12'
                numOctaves='1'
                result='warp'
              ></feTurbulence>
              <feDisplacementMap
                xChannelSelector='R'
                yChannelSelector='G'
                scale='30'
                in='SourceGraphic'
                in2='warp'
              />
            </filter>
          </defs>
        </svg>
      </div>

      <style jsx>{`
        .glass-effect {
          background: rgba(0, 0, 0, 0.2);
          background: repeating-radial-gradient(
            circle at 50%50%,
            rgb(255 255 255 / 0),
            rgba(255, 255, 255, 0.2) 10px,
            rgb(255 255 255) 31px
          );
          filter: url(#fractal-noise-glass);
          background-size: 6px 6px;
          backdrop-filter: blur(0px);
        }
      `}</style>
    </>
  );
};

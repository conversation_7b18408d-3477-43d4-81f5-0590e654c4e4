import { Button } from '@/components/ui/button';
import { FileText as FileTextIcon, Plus as PlusIcon } from 'lucide-react';

interface DocumentEmptyStateProps {
  onCreateDocument: () => void;
}

export function DocumentEmptyState({
  onCreateDocument,
}: DocumentEmptyStateProps) {
  return (
    <div className="text-center py-12 border rounded-lg bg-muted/20">
      <FileTextIcon className="mx-auto h-12 w-12 text-muted-foreground" />
      <h3 className="mt-4 text-lg font-medium">No documents yet</h3>
      <p className="mt-2 text-muted-foreground">
        Get started by creating your first document
      </p>
      <Button className="mt-4" onClick={onCreateDocument}>
        <PlusIcon className="mr-2 h-4 w-4" />
        Create Document
      </Button>
    </div>
  );
}

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON>Header } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DocCard } from '@/components/ux/comp/doc-card';
import React from 'react';

interface DocumentLoadingStateProps {
  viewMode: 'card' | 'table';
  columnVisibility?: {
    type: boolean;
    status: boolean;
    updated: boolean;
  };
}

// Memoize the card and table skeletons for better performance
const CardSkeleton = React.memo(() => (
  <DocCard className="mb-4">
    <div className="h-full">
      <CardHeader className="pb-2 pt-6 px-6">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-neutral-50 rounded-full">
              <Skeleton className="h-5 w-5" />
            </div>
            <Skeleton className="h-6 w-40" />
          </div>
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
        <Skeleton className="h-4 w-1/2 mt-2" />
      </CardHeader>
      <CardContent className="px-6 py-4">
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-2/3 mb-2" />
        <div className="flex gap-2 mt-3">
          <Skeleton className="h-6 w-20 rounded-full" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-4 pb-6 px-6 border-t">
        <Skeleton className="h-4 w-20" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </CardFooter>
    </div>
  </DocCard>
));

// Add display name
CardSkeleton.displayName = 'CardSkeleton';

export function DocumentLoadingState({
  viewMode,
  columnVisibility = { type: true, status: true, updated: true },
}: DocumentLoadingStateProps) {
  if (viewMode === 'card') {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <CardSkeleton key={i} />
        ))}
      </div>
    );
  }

  // Memoize the table row skeleton for better performance
  const TableRowSkeleton = React.memo(({ index }: { index: number }) => (
    <TableRow key={index}>
      <TableCell>
        <Skeleton className="h-6 w-full" />
      </TableCell>
      {columnVisibility.type && (
        <TableCell>
          <Skeleton className="h-4 w-20" />
        </TableCell>
      )}
      {columnVisibility.status && (
        <TableCell>
          <div className="flex items-center">
            <Skeleton className="h-6 w-24" />
          </div>
        </TableCell>
      )}
      {columnVisibility.updated && (
        <TableCell>
          <Skeleton className="h-4 w-24" />
        </TableCell>
      )}
      <TableCell className="text-right">
        <div className="flex justify-end space-x-2">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </TableCell>
    </TableRow>
  ));

  // Add display name
  TableRowSkeleton.displayName = 'TableRowSkeleton';

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Title</TableHead>
            {columnVisibility.type && <TableHead>Type</TableHead>}
            {columnVisibility.status && <TableHead>Status</TableHead>}
            {columnVisibility.updated && <TableHead>Last Updated</TableHead>}
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 3 }).map((_, i) => (
            <TableRowSkeleton key={i} index={i} />
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { TrashFill } from '@/components/ux/icons/nucleo/trash-fill';
import { DocumentSummary } from '@/lib/types/database-modules';
import { JSX, useEffect, useState } from 'react';
import { DocumentCard } from './DocumentCard';

interface DocumentCardGridProps {
  documents: DocumentSummary[];
  username: string;
  onDelete: (id: string) => void;
  onShare?: (id: string) => void;
  onBatchDelete?: (ids: string[]) => void;
  getDocumentTypeIcon: (type: string | null) => JSX.Element;
  getStatusColor: (status: string) => string;
}

export function DocumentCardGrid({
  documents,
  username,
  onDelete,
  onShare,
  onBatchDelete,
  getDocumentTypeIcon,
  getStatusColor,
}: DocumentCardGridProps) {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectAllState, setSelectAllState] = useState<
    boolean | 'indeterminate'
  >(false);

  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedDocuments([]);
    setSelectAllState(false);
  };

  const toggleSelectDocument = (
    id: string,
    checked?: boolean | 'indeterminate'
  ) => {
    if (checked === 'indeterminate') return;

    setSelectedDocuments((prev) => {
      let newSelected;
      if (checked === false) {
        newSelected = prev.filter((docId) => docId !== id);
      } else if (checked === true) {
        newSelected = [...prev, id];
      } else {
        // Toggle behavior for backward compatibility
        if (prev.includes(id)) {
          newSelected = prev.filter((docId) => docId !== id);
        } else {
          newSelected = [...prev, id];
        }
      }

      // Update selectAllState based on the new selection
      updateSelectAllState(newSelected);
      return newSelected;
    });
  };

  // Helper function to update the selectAllState based on selected documents
  const updateSelectAllState = (selected: string[] = selectedDocuments) => {
    if (selected.length === 0) {
      setSelectAllState(false);
    } else if (selected.length === documents.length) {
      setSelectAllState(true);
    } else {
      setSelectAllState('indeterminate');
    }
  };

  // Effect to update selectAllState when documents change
  useEffect(() => {
    updateSelectAllState();
  }, [documents.length]);

  const toggleSelectAll = (checked: boolean | 'indeterminate') => {
    if (checked === 'indeterminate') {
      // If currently indeterminate, select all
      setSelectedDocuments(
        documents.map((doc) => doc.id).filter((id): id is string => id !== null)
      );
      setSelectAllState(true);
    } else if (checked) {
      // If checked, select all
      setSelectedDocuments(
        documents.map((doc) => doc.id).filter((id): id is string => id !== null)
      );
      setSelectAllState(true);
    } else {
      // If unchecked, deselect all
      setSelectedDocuments([]);
      setSelectAllState(false);
    }
  };

  const handleBatchDelete = () => {
    if (selectedDocuments.length > 0 && onBatchDelete) {
      onBatchDelete(selectedDocuments);
      setIsSelectionMode(false);
      setSelectedDocuments([]);
      setSelectAllState(false);
    }
  };

  return (
    <>
      {isSelectionMode && (
        <div className="flex justify-between items-center mb-4 p-2 border border-neutral-200/60 rounded-2xl">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={selectAllState}
              onCheckedChange={toggleSelectAll}
              className="mr-2"
            />
            <span className="text-sm text-muted-foreground uppercase font-medium">
              {selectedDocuments.length}{' '}
              {selectedDocuments.length === 1 ? 'document' : 'documents'}{' '}
              selected
            </span>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="shadow_red"
              size="sm"
              onClick={handleBatchDelete}
              disabled={selectedDocuments.length === 0}
              className="uppercase text-sm rounded-lg"
            >
              <TrashFill className="mr-2 h-4 w-4" />
              Delete Selected
            </Button>
            <Button
              variant="shadow"
              size="sm"
              onClick={toggleSelectionMode}
              className="uppercase text-sm rounded-lg"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {!isSelectionMode && (
        <div className="flex justify-end mb-4">
          <Button
            variant="shadow"
            size="sm"
            onClick={toggleSelectionMode}
            className="uppercase text-sm rounded-lg"
          >
            Select Documents
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4">
        {documents.map((doc) => (
          <DocumentCard
            key={doc.id ?? ''}
            document={doc}
            username={username}
            onDelete={onDelete}
            onShare={onShare}
            getDocumentTypeIcon={getDocumentTypeIcon}
            getStatusColor={getStatusColor}
            isSelectionMode={isSelectionMode}
            isSelected={doc.id !== null && selectedDocuments.includes(doc.id)}
            onToggleSelect={(checked) =>
              doc.id !== null && toggleSelectDocument(doc.id, checked)
            }
          />
        ))}
      </div>
    </>
  );
}

'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DocumentAttachment } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { PaperclipIcon, UploadIcon } from 'lucide-react';
import { useState } from 'react';
import { FileList } from './FileList';
import { FileUpload } from './FileUpload';

interface DocumentAttachmentsProps {
  documentId: string;
  className?: string;
  defaultTab?: 'view' | 'upload';
}

export function DocumentAttachments({
  documentId,
  className,
  defaultTab = 'view',
}: DocumentAttachmentsProps) {
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [refreshKey, setRefreshKey] = useState<number>(0);

  const handleUploadComplete = (attachment: DocumentAttachment) => {
    // Switch to view tab after successful upload
    setActiveTab('view');
    // Force refresh the file list
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle>Document Attachments</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="view" className="flex items-center gap-2">
              <PaperclipIcon className="h-4 w-4" />
              View Files
            </TabsTrigger>
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <UploadIcon className="h-4 w-4" />
              Upload Files
            </TabsTrigger>
          </TabsList>
          <TabsContent value="view" className="mt-4">
            <FileList
              key={refreshKey}
              documentId={documentId}
              onDelete={() => setRefreshKey((prev) => prev + 1)}
            />
          </TabsContent>
          <TabsContent value="upload" className="mt-4">
            <FileUpload
              documentId={documentId}
              onUploadComplete={handleUploadComplete}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

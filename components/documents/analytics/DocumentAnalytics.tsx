'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDocuments } from '@/lib/hooks';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  Activity,
  ArrowDownRight,
  ArrowUpRight,
  BarChart3,
  Calendar,
  Clock,
  FileText,
  Loader2,
  PieC<PERSON>,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface DocumentAnalyticsProps {
  userId?: string;
  timeRange?: 'week' | 'month' | 'year';
}

interface AnalyticsData {
  totalDocuments: number;
  totalDocumentsChange: number;
  activeDocuments: number;
  activeDocumentsChange: number;
  sharedDocuments: number;
  sharedDocumentsChange: number;
  averageEditTime: number;
  averageEditTimeChange: number;
  documentsByType: {
    type: string;
    count: number;
    percentage: number;
  }[];
  documentsByStatus: {
    status: string;
    count: number;
    percentage: number;
  }[];
  activityOverTime: {
    date: string;
    created: number;
    edited: number;
    viewed: number;
  }[];
  topCollaborators: {
    id: string;
    name: string;
    email: string;
    documentCount: number;
  }[];
}

export function DocumentAnalytics({
  userId,
  timeRange = 'week',
}: DocumentAnalyticsProps) {
  const { getDocumentAnalytics } = useDocuments();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState<
    'week' | 'month' | 'year'
  >(timeRange);

  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      try {
        // Fetch real analytics data from the database
        const data = await getDocumentAnalytics(selectedTimeRange, userId);

        if (data) {
          setAnalyticsData(data as AnalyticsData);
        } else {
          // If no data is returned, show an empty state
          setAnalyticsData({
            totalDocuments: 0,
            totalDocumentsChange: 0,
            activeDocuments: 0,
            activeDocumentsChange: 0,
            sharedDocuments: 0,
            sharedDocumentsChange: 0,
            averageEditTime: 0,
            averageEditTimeChange: 0,
            documentsByType: [],
            documentsByStatus: [],
            activityOverTime: [],
            topCollaborators: [],
          });
        }
      } catch (error) {
        console.error('Error fetching document analytics:', error);
        toast.error('Failed to load analytics', {
          description: 'Could not retrieve document analytics data',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [selectedTimeRange, userId, getDocumentAnalytics]);

  const renderChangeIndicator = (value: number) => {
    if (value === 0) return null;

    return (
      <div
        className={cn(
          'flex items-center text-xs font-medium ml-2',
          value > 0 ? 'text-green-600' : 'text-red-600'
        )}
      >
        {value > 0 ? (
          <ArrowUpRight className="h-3 w-3 mr-1" />
        ) : (
          <ArrowDownRight className="h-3 w-3 mr-1" />
        )}
        {Math.abs(value)}%
      </div>
    );
  };

  if (loading || !analyticsData) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Document Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[400px] flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-12 w-12 text-muted-foreground mx-auto mb-4 animate-spin" />
                <p className="text-muted-foreground">
                  Loading analytics data...
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Document Analytics</h2>

        <Tabs
          value={selectedTimeRange}
          onValueChange={(value) => setSelectedTimeRange(value as any)}
        >
          <TabsList>
            <TabsTrigger value="week">Week</TabsTrigger>
            <TabsTrigger value="month">Month</TabsTrigger>
            <TabsTrigger value="year">Year</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-blue-100 rounded-lg p-3">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex items-center">
                {renderChangeIndicator(analyticsData.totalDocumentsChange)}
              </div>
            </div>
            <div className="mt-3">
              <p className="text-sm font-medium text-muted-foreground">
                Total Documents
              </p>
              <h3 className="text-2xl font-bold">
                {analyticsData.totalDocuments}
              </h3>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-green-100 rounded-lg p-3">
                <Activity className="h-5 w-5 text-green-600" />
              </div>
              <div className="flex items-center">
                {renderChangeIndicator(analyticsData.activeDocumentsChange)}
              </div>
            </div>
            <div className="mt-3">
              <p className="text-sm font-medium text-muted-foreground">
                Active Documents
              </p>
              <h3 className="text-2xl font-bold">
                {analyticsData.activeDocuments}
              </h3>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-purple-100 rounded-lg p-3">
                <Users className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex items-center">
                {renderChangeIndicator(analyticsData.sharedDocumentsChange)}
              </div>
            </div>
            <div className="mt-3">
              <p className="text-sm font-medium text-muted-foreground">
                Shared Documents
              </p>
              <h3 className="text-2xl font-bold">
                {analyticsData.sharedDocuments}
              </h3>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="bg-amber-100 rounded-lg p-3">
                <Clock className="h-5 w-5 text-amber-600" />
              </div>
              <div className="flex items-center">
                {renderChangeIndicator(analyticsData.averageEditTimeChange)}
              </div>
            </div>
            <div className="mt-3">
              <p className="text-sm font-medium text-muted-foreground">
                Avg. Edit Time
              </p>
              <h3 className="text-2xl font-bold">
                {analyticsData.averageEditTime} min
              </h3>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-muted-foreground" />
              Document Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              {/* In a real implementation, this would be a chart component */}
              <div className="h-full flex flex-col">
                <div className="flex-1 grid grid-cols-1 gap-2">
                  {analyticsData.activityOverTime.slice(-7).map((day) => (
                    <div key={day.date} className="flex items-center">
                      <div className="w-20 text-xs text-muted-foreground">
                        {format(new Date(day.date), 'MMM d')}
                      </div>
                      <div className="flex-1 flex items-center gap-1">
                        <div
                          className="bg-blue-500 h-4 rounded"
                          style={{ width: `${day.created * 5}%` }}
                        />
                        <div
                          className="bg-green-500 h-4 rounded"
                          style={{ width: `${day.edited * 3}%` }}
                        />
                        <div
                          className="bg-purple-500 h-4 rounded"
                          style={{ width: `${day.viewed * 2}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex items-center justify-center mt-4 gap-4">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded mr-2" />
                    <span className="text-xs">Created</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded mr-2" />
                    <span className="text-xs">Edited</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-purple-500 rounded mr-2" />
                    <span className="text-xs">Viewed</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2 text-muted-foreground" />
              Documents by Type
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              {/* In a real implementation, this would be a chart component */}
              <div className="w-full max-w-md">
                {analyticsData.documentsByType.map((type, index) => (
                  <div key={type.type} className="mb-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm">{type.type}</span>
                      <span className="text-sm font-medium">
                        {type.count} ({type.percentage}%)
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2.5">
                      <div
                        className={cn(
                          'h-2.5 rounded-full',
                          index === 0
                            ? 'bg-blue-600'
                            : index === 1
                              ? 'bg-green-600'
                              : index === 2
                                ? 'bg-yellow-600'
                                : index === 3
                                  ? 'bg-purple-600'
                                  : 'bg-gray-600'
                        )}
                        style={{ width: `${type.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-muted-foreground" />
              Document Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.documentsByStatus.map((status) => (
                <div key={status.status}>
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center">
                      <Badge
                        className={cn(
                          status.status === 'Draft'
                            ? 'bg-yellow-100 text-yellow-800'
                            : status.status === 'Published'
                              ? 'bg-green-100 text-green-800'
                              : status.status === 'Archived'
                                ? 'bg-gray-100 text-gray-800'
                                : 'bg-blue-100 text-blue-800'
                        )}
                      >
                        {status.status}
                      </Badge>
                    </div>
                    <span className="text-sm font-medium">
                      {status.count} documents
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2.5">
                    <div
                      className={cn(
                        'h-2.5 rounded-full',
                        status.status === 'Draft'
                          ? 'bg-yellow-600'
                          : status.status === 'Published'
                            ? 'bg-green-600'
                            : status.status === 'Archived'
                              ? 'bg-gray-600'
                              : 'bg-blue-600'
                      )}
                      style={{ width: `${status.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-muted-foreground" />
              Top Collaborators
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.topCollaborators.map((user, index) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={cn(
                        'flex items-center justify-center w-8 h-8 rounded-full text-white font-medium',
                        index === 0
                          ? 'bg-blue-600'
                          : index === 1
                            ? 'bg-green-600'
                            : index === 2
                              ? 'bg-purple-600'
                              : 'bg-gray-600'
                      )}
                    >
                      {user.name.charAt(0)}
                    </div>
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Badge variant="outline">{user.documentCount} docs</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

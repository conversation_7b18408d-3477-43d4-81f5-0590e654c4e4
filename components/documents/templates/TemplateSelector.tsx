'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useDocuments } from '@/lib/hooks';
import { DocumentSummary, DocumentType } from '@/lib/types/database-modules';
import {
  ArrowRight,
  Eye,
  FileText,
  Filter,
  Search,
  BookTemplate as TemplateIcon,
} from 'lucide-react';
import { useEffect, useState } from 'react';
// Template preview is now a separate page
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useParams, useRouter } from 'next/navigation';

interface TemplateSelectorProps {
  onSelectTemplate?: (templateId: string) => void;
  showCreateButton?: boolean;
  className?: string;
}

export function TemplateSelector({
  onSelectTemplate,
  showCreateButton = true,
  className,
}: TemplateSelectorProps) {
  const documentsContext = useDocuments();
  const router = useRouter();
  const params = useParams();
  const username = params.username as string;

  const [templates, setTemplates] = useState<DocumentSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  // No longer need previewTemplateId state

  useEffect(() => {
    const fetchTemplates = async () => {
      setLoading(true);
      try {
        // Filter documents to only include templates if needed
        const templateDocs = documentsContext.documents
          .filter((doc) => doc.is_template)
          .map((doc) => ({
            id: doc.id ?? null,
            title: doc.title ?? '',
            description: doc.description ?? null,
            document_type: doc.document_type ?? null,
            is_template: doc.is_template ?? null,
            owner_id: doc.owner_id ?? null,
            owner_full_name: null,
            owner_username: null,
            status: doc.status ?? null,
            tags: [],
            created_at: doc.created_at ?? null,
            updated_at: doc.updated_at ?? null,
          }));
        setTemplates(templateDocs);
      } catch (error) {
        console.error('Error fetching templates:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, [documentsContext.documents]);

  // Filter templates based on search query and type filter
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      searchQuery === '' ||
      template.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (template.description &&
        template.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesType =
      typeFilter === 'all' || template.document_type === typeFilter;

    return matchesSearch && matchesType;
  });

  const handleSelectTemplate = (templateId: string) => {
    if (onSelectTemplate) {
      onSelectTemplate(templateId);
    }
  };

  const getDocumentTypeIcon = (type: DocumentType) => {
    switch (type) {
      case 'contract':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'form':
        return <FileText className="h-4 w-4 text-green-500" />;
      case 'letter':
        return <FileText className="h-4 w-4 text-purple-500" />;
      case 'agreement':
        return <FileText className="h-4 w-4 text-orange-500" />;
      case 'report':
        return <FileText className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className={className}>
      <div className="mb-6">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="relative flex-1">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
              size={16}
            />
            <Input
              placeholder="Search templates..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2 w-full md:w-auto">
            <Filter size={16} className="text-muted-foreground" />
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Document Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="contract">Contract</SelectItem>
                <SelectItem value="form">Form</SelectItem>
                <SelectItem value="letter">Letter</SelectItem>
                <SelectItem value="agreement">Agreement</SelectItem>
                <SelectItem value="report">Report</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Skeleton className="h-8 w-8 rounded" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                  <Skeleton className="h-3 w-full mb-2" />
                  <Skeleton className="h-3 w-3/4" />
                </div>
                <div className="border-t p-3 bg-muted/20">
                  <Skeleton className="h-8 w-20 rounded-md" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredTemplates.length === 0 ? (
        <div className="text-center py-12 border rounded-lg bg-muted/20">
          <TemplateIcon className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No templates found</h3>
          <p className="mt-2 text-muted-foreground">
            {searchQuery || typeFilter !== 'all'
              ? 'No templates match your search criteria. Try adjusting your filters.'
              : 'There are no templates available yet.'}
          </p>
          {showCreateButton && (
            <Button
              className="mt-4"
              onClick={() =>
                router.push(`/${username}/documents/new?template=true`)
              }
            >
              Create Template
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTemplates.map((template) => (
            <Card
              key={template.id}
              className="overflow-hidden hover:shadow-md transition-shadow"
            >
              <CardContent className="p-0">
                <div className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    {getDocumentTypeIcon(
                      (template.document_type ?? 'other') as DocumentType
                    )}
                    <h3 className="font-medium">{template.title}</h3>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {template.description || 'No description provided'}
                  </p>
                </div>
                <div className="border-t p-3 bg-muted/20 flex justify-between items-center">
                  <span className="text-xs text-muted-foreground">
                    {template.document_type}
                  </span>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        router.push(
                          `/${username}/documents/templates/${template.id ?? ''}`
                        )
                      }
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleSelectTemplate(template.id ?? '')}
                    >
                      Use
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Template preview is now a separate page */}
    </div>
  );
}

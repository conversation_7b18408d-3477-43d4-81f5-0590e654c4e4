'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  DocumentExportService,
  ExportFormat,
  ExportOptions,
} from '@/lib/services/documentExportService';
import { Document } from '@/lib/types/database-modules';
import { Download } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface ExportDialogProps {
  document: Document;
  isOpen: boolean;
  onClose: () => void;
}

export function ExportDialog({ document, isOpen, onClose }: ExportDialogProps) {
  const [exportFormat, setExportFormat] = useState<ExportFormat>('pdf');
  const [includeWatermark, setIncludeWatermark] = useState(false);
  const [watermarkText, setWatermarkText] = useState('CONFIDENTIAL');
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  // Using react-pdf for all PDF exports

  const handleExport = async () => {
    if (!document) return;

    setIsExporting(true);

    try {
      const exportService = new DocumentExportService();

      const options: ExportOptions = {
        format: exportFormat,
        includeWatermark,
        watermarkText: includeWatermark ? watermarkText : undefined,
        includeMetadata,
      };

      const result = await exportService.exportDocument(document, options);

      // Create a download link
      if (result instanceof Blob) {
        // For binary formats like PDF
        const url = URL.createObjectURL(result);
        const a = window.document.createElement('a');
        a.href = url;
        a.download = `${document.title}.${exportFormat}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        // For text formats like HTML or plain text
        const blob = new Blob([result], {
          type: exportFormat === 'html' ? 'text/html' : 'text/plain',
        });
        const url = URL.createObjectURL(blob);
        const a = window.document.createElement('a');
        a.href = url;
        a.download = `${document.title}.${exportFormat === 'html' ? 'html' : 'txt'}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }

      toast.success('Document exported', {
        description: `Successfully exported document as ${exportFormat.toUpperCase()}`,
      });

      onClose();
    } catch (error) {
      console.error('Error exporting document:', error);
      toast.error('Export failed', {
        description:
          error instanceof Error ? error.message : 'Failed to export document',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Export Document</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div>
            <h3 className="text-sm font-medium mb-2">
              Export &quot;{document.title}&quot;
            </h3>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="export-format">Export Format</Label>
              <Select
                value={exportFormat}
                onValueChange={(value) =>
                  setExportFormat(value as ExportFormat)
                }
              >
                <SelectTrigger id="export-format">
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF Document</SelectItem>
                  <SelectItem value="html">HTML Document</SelectItem>
                  <SelectItem value="text">Plain Text</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="include-metadata">Include Metadata</Label>
                <Switch
                  id="include-metadata"
                  checked={includeMetadata}
                  onCheckedChange={setIncludeMetadata}
                />
              </div>
              <p className="text-sm text-muted-foreground">
                Include document type, status, and creation date
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="include-watermark">Add Watermark</Label>
                <Switch
                  id="include-watermark"
                  checked={includeWatermark}
                  onCheckedChange={setIncludeWatermark}
                />
              </div>

              {includeWatermark && (
                <div className="pt-2">
                  <Label htmlFor="watermark-text">Watermark Text</Label>
                  <Input
                    id="watermark-text"
                    value={watermarkText}
                    onChange={(e) => setWatermarkText(e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}
            </div>

            {exportFormat === 'pdf' && (
              <div className="space-y-2 mt-4">
                <p className="text-sm text-muted-foreground">
                  Using React PDF for high-quality document rendering
                </p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="button" onClick={handleExport} disabled={isExporting}>
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

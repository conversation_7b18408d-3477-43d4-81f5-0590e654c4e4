'use client';

import { ShareLinkStats } from '@/components/documents/sharing/ShareLinkStats';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDocuments } from '@/lib/hooks';
import { DocumentShareLink } from '@/lib/types/database-modules';
import { du } from '@/lib/utils';
import { Clock, Copy, Link, Mail, Trash } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

interface DocumentShareSheetProps {
  documentId: string;
  documentTitle: string;
  isOpen: boolean;
  onClose: () => void;
}

export function DocumentShareSheet({
  documentId,
  documentTitle,
  isOpen,
  onClose,
}: DocumentShareSheetProps) {
  const {
    loading,
    shareDocumentWithUser,
    createShareLink,
    getDocumentCollaborators,
    getDocumentShareLinks,
    removeCollaborator,
    deactivateShareLink,
    updateCollaboratorPermission,
  } = useDocuments();

  const [activeTab, setActiveTab] = useState<string>('people');
  const [email, setEmail] = useState<string>('');
  const [permission, setPermission] = useState<'view' | 'edit' | 'comment'>(
    'view'
  );
  const [expirationDays, setExpirationDays] = useState<number | null>(7);
  const [shareLink, setShareLink] = useState<string>('');
  const [collaborators, setCollaborators] = useState<any[]>([]);
  const [shareLinks, setShareLinks] = useState<DocumentShareLink[]>([]);
  const [isLoadingCollaborators, setIsLoadingCollaborators] =
    useState<boolean>(false);
  const [isLoadingLinks, setIsLoadingLinks] = useState<boolean>(false);

  // New state for access protection
  const [accessType, setAccessType] = useState<
    'public' | 'pin_protected' | 'password_protected'
  >('public');
  const [accessPin, setAccessPin] = useState<string>('');
  const [editPassword, setEditPassword] = useState<string>('');

  // Load collaborators and share links when the sheet opens
  useEffect(() => {
    if (isOpen && documentId) {
      loadCollaborators();
      loadShareLinks();
    }
  }, [isOpen, documentId]);

  const loadCollaborators = async () => {
    setIsLoadingCollaborators(true);
    try {
      const result = await getDocumentCollaborators(documentId);
      setCollaborators(result);
    } catch (error) {
      console.error('Error loading collaborators:', error);
      toast.error('Failed to load collaborators');
    } finally {
      setIsLoadingCollaborators(false);
    }
  };

  const loadShareLinks = async () => {
    setIsLoadingLinks(true);
    try {
      const result = await getDocumentShareLinks(documentId);
      setShareLinks(result);
    } catch (error) {
      console.error('Error loading share links:', error);
      toast.error('Failed to load share links');
    } finally {
      setIsLoadingLinks(false);
    }
  };

  const handleShareWithUser = async () => {
    if (!email.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    toast.promise(shareDocumentWithUser(documentId, email, permission), {
      loading: 'Sharing document...',
      success: () => {
        setEmail('');
        loadCollaborators();
        return 'Document shared successfully';
      },
      error: (err) => {
        // Check if the error is about a user not found
        if (err instanceof Error && err.message.includes('not found')) {
          return err.message;
        }
        return 'Failed to share document';
      },
    });
  };

  const handleCreateShareLink = async () => {
    // Validate PIN if access type is pin_protected
    if (
      accessType === 'pin_protected' &&
      (!accessPin || accessPin.length !== 4)
    ) {
      toast.error('Please enter a valid 4-digit PIN');
      return;
    }

    // Validate password if access type is password_protected and permission is edit
    if (
      accessType === 'password_protected' &&
      permission === 'edit' &&
      !editPassword
    ) {
      toast.error('Please enter a password for edit access');
      return;
    }

    toast.promise(
      createShareLink(
        documentId,
        permission,
        expirationDays,
        accessType,
        accessType === 'pin_protected' ? accessPin : undefined,
        accessType === 'password_protected' && permission === 'edit'
          ? editPassword
          : undefined
      ),
      {
        loading: 'Creating share link...',
        success: (link) => {
          if (link && 'token' in link) {
            const baseUrl = window.location.origin;
            const fullLink = `${baseUrl}/shared/${link.token}`;
            setShareLink(fullLink);
            loadShareLinks();

            // Reset protection settings after successful creation
            setAccessType('public');
            setAccessPin('');
            setEditPassword('');
          }
          return 'Share link created successfully';
        },
        error: (err) => {
          if (err instanceof Error) {
            return err.message;
          }
          return 'Failed to create share link';
        },
      }
    );
  };

  const handleCopyLink = (link: string) => {
    navigator.clipboard.writeText(link);
    toast.success('Link copied to clipboard');
  };

  const handleRemoveCollaborator = async (collaborationId: string) => {
    toast.promise(removeCollaborator(collaborationId), {
      loading: 'Removing collaborator...',
      success: () => {
        loadCollaborators();
        return 'Collaborator removed successfully';
      },
      error: 'Failed to remove collaborator',
    });
  };

  const handleDeactivateLink = async (linkId: string) => {
    toast.promise(deactivateShareLink(linkId), {
      loading: 'Deactivating link...',
      success: () => {
        loadShareLinks();
        return 'Link deactivated successfully';
      },
      error: 'Failed to deactivate link',
    });
  };

  const handleUpdatePermission = async (
    collaborationId: string,
    newPermission: 'view' | 'edit' | 'comment'
  ) => {
    toast.promise(
      updateCollaboratorPermission(collaborationId, newPermission),
      {
        loading: 'Updating permission...',
        success: () => {
          loadCollaborators();
          return 'Permission updated successfully';
        },
        error: 'Failed to update permission',
      }
    );
  };

  const getPermissionLabel = (permission: string) => {
    switch (permission) {
      case 'view':
        return 'Can view';
      case 'edit':
        return 'Can edit';
      case 'comment':
        return 'Can comment';
      default:
        return 'Unknown';
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <Drawer.Root open={isOpen} onOpenChange={onClose} direction="right">
      <Drawer.Content className="fixed bottom-2 right-2 top-2 z-10 flex w-[calc(100%-16px)] outline-none md:w-[540px]">
        <div className="scrollbar-hide flex size-full grow flex-col overflow-y-auto rounded-lg bg-white">
          <div className="flex flex-col p-6">
            <Drawer.Title className="text-xl font-medium text-zinc-900">
              Share "{documentTitle}"
            </Drawer.Title>
            <Drawer.Description className="text-sm text-muted-foreground">
              Share this document with others or create a shareable link.
            </Drawer.Description>

            <Tabs
              defaultValue="people"
              className="mt-6"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="people">People</TabsTrigger>
                <TabsTrigger value="links">Links</TabsTrigger>
              </TabsList>

              <TabsContent value="people" className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email address</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                    <Select
                      value={permission}
                      onValueChange={(value) =>
                        setPermission(value as 'view' | 'edit' | 'comment')
                      }
                    >
                      <SelectTrigger className="w-[110px]">
                        <SelectValue placeholder="Permission" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="view">View</SelectItem>
                        <SelectItem value="edit">Edit</SelectItem>
                        <SelectItem value="comment">Comment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button
                  type="button"
                  onClick={handleShareWithUser}
                  disabled={loading || !email.trim()}
                  className="w-full"
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Share
                </Button>

                <div className="space-y-2">
                  <Label>People with access</Label>
                  <div className="space-y-2 max-h-[300px] overflow-y-auto">
                    {isLoadingCollaborators ? (
                      Array.from({ length: 3 }).map((_, i) => (
                        <div
                          key={i}
                          className="flex items-center justify-between p-2 border rounded-md"
                        >
                          <div className="flex items-center space-x-2">
                            <Skeleton className="h-8 w-8 rounded-full" />
                            <div className="space-y-1">
                              <Skeleton className="h-4 w-24" />
                              <Skeleton className="h-3 w-16" />
                            </div>
                          </div>
                          <Skeleton className="h-8 w-20" />
                        </div>
                      ))
                    ) : collaborators.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        No collaborators yet
                      </div>
                    ) : (
                      collaborators.map((collaborator) => (
                        <div
                          key={collaborator.id}
                          className="flex items-center justify-between p-2 border rounded-md"
                        >
                          <div className="flex items-center space-x-2">
                            <Avatar>
                              <AvatarImage
                                src={collaborator.user?.avatar_url}
                                alt={
                                  collaborator.user?.full_name ||
                                  collaborator.user?.email
                                }
                              />
                              <AvatarFallback>
                                {getInitials(
                                  collaborator.user?.full_name ||
                                    collaborator.user?.email ||
                                    'User'
                                )}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">
                                {collaborator.user?.full_name ||
                                  collaborator.user?.email}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {collaborator.user?.email}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Select
                              value={collaborator.permission}
                              onValueChange={(value) =>
                                handleUpdatePermission(
                                  collaborator.id,
                                  value as 'view' | 'edit' | 'comment'
                                )
                              }
                            >
                              <SelectTrigger className="w-[110px]">
                                <SelectValue placeholder="Permission" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="view">View</SelectItem>
                                <SelectItem value="edit">Edit</SelectItem>
                                <SelectItem value="comment">Comment</SelectItem>
                              </SelectContent>
                            </Select>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() =>
                                handleRemoveCollaborator(collaborator.id)
                              }
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="links" className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label>Create a new link</Label>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="permission">Permission</Label>
                      <Select
                        value={permission}
                        onValueChange={(value) =>
                          setPermission(value as 'view' | 'edit' | 'comment')
                        }
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="Permission" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="view">View only</SelectItem>
                          <SelectItem value="edit">Can edit</SelectItem>
                          <SelectItem value="comment">Can comment</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="expiration">Expiration</Label>
                      <Select
                        value={expirationDays?.toString() || 'never'}
                        onValueChange={(value) =>
                          setExpirationDays(
                            value === 'never' ? null : parseInt(value)
                          )
                        }
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="Expiration" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 day</SelectItem>
                          <SelectItem value="7">7 days</SelectItem>
                          <SelectItem value="30">30 days</SelectItem>
                          <SelectItem value="never">Never expires</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="accessType">Access Protection</Label>
                      <Select
                        value={accessType}
                        onValueChange={(value) =>
                          setAccessType(
                            value as
                              | 'public'
                              | 'pin_protected'
                              | 'password_protected'
                          )
                        }
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="Access Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="public">Public Access</SelectItem>
                          <SelectItem value="pin_protected">
                            PIN Protected
                          </SelectItem>
                          <SelectItem value="password_protected">
                            Password Protected
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {accessType === 'pin_protected' && (
                      <div className="space-y-2">
                        <Label htmlFor="accessPin">4-Digit Access PIN</Label>
                        <Input
                          id="accessPin"
                          type="text"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          maxLength={4}
                          placeholder="Enter 4-digit PIN"
                          value={accessPin}
                          onChange={(e) => {
                            // Only allow digits
                            const value = e.target.value.replace(/[^0-9]/g, '');
                            setAccessPin(value);
                          }}
                          className="text-center text-lg tracking-widest"
                        />
                        <p className="text-xs text-muted-foreground">
                          Viewers will need to enter this PIN to access the
                          document
                        </p>
                      </div>
                    )}

                    {accessType === 'password_protected' &&
                      permission === 'edit' && (
                        <div className="space-y-2">
                          <Label htmlFor="editPassword">Edit Password</Label>
                          <Input
                            id="editPassword"
                            type="password"
                            placeholder="Enter password for edit access"
                            value={editPassword}
                            onChange={(e) => setEditPassword(e.target.value)}
                          />
                          <p className="text-xs text-muted-foreground">
                            Editors will need this password to make changes to
                            the document
                          </p>
                        </div>
                      )}

                    <Button
                      type="button"
                      onClick={handleCreateShareLink}
                      disabled={loading}
                      className="w-full"
                    >
                      <Link className="mr-2 h-4 w-4" />
                      Create link
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Active links</Label>
                  <div className="space-y-2 max-h-[300px] overflow-y-auto">
                    {isLoadingLinks ? (
                      Array.from({ length: 2 }).map((_, i) => (
                        <div
                          key={i}
                          className="flex flex-col p-2 border rounded-md space-y-2"
                        >
                          <div className="flex items-center justify-between">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-6 w-6 rounded-full" />
                          </div>
                          <Skeleton className="h-8 w-full" />
                          <div className="flex justify-between">
                            <Skeleton className="h-4 w-20" />
                            <Skeleton className="h-4 w-16" />
                          </div>
                        </div>
                      ))
                    ) : shareLinks.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        No active links
                      </div>
                    ) : (
                      shareLinks.map((link) => {
                        const baseUrl = window.location.origin;
                        const fullLink = `${baseUrl}/shared/${link.token}`;

                        return (
                          <div
                            key={link.id}
                            className="flex flex-col p-2 border rounded-md space-y-2"
                          >
                            <div className="flex items-center justify-between">
                              <Badge variant="outline">
                                {getPermissionLabel(link.permission)}
                              </Badge>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeactivateLink(link.id)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="flex items-center">
                              <Input
                                value={fullLink}
                                readOnly
                                className="flex-1"
                              />
                              <Button
                                type="button"
                                size="icon"
                                variant="ghost"
                                className="ml-2"
                                onClick={() => handleCopyLink(fullLink)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {link.expires_at
                                  ? `Expires ${du.getRelativeTime(link.expires_at)}`
                                  : 'Never expires'}
                              </div>
                              <div>
                                {link.access_count}{' '}
                                {link.access_count === 1 ? 'view' : 'views'}
                              </div>
                            </div>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>

                {/* Share Link Statistics */}
                <div className="mt-6">
                  <ShareLinkStats documentId={documentId} />
                </div>
              </TabsContent>
            </Tabs>

            <div className="mt-6 flex justify-end">
              <Button variant="outline" onClick={onClose}>
                Done
              </Button>
            </div>
          </div>
        </div>
      </Drawer.Content>
    </Drawer.Root>
  );
}

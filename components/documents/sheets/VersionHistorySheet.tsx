'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet } from '@/components/ui/sheet';
import { useDocuments } from '@/lib/hooks';
import { format, formatDistanceToNow } from 'date-fns';
import {
  ArrowLeft,
  Check,
  Clock,
  Eye,
  History,
  RotateCcw,
  User,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { DocumentVersion as DbDocumentVersion } from '@/lib/types/database-modules';
import { toast } from 'sonner';

// Extend the database DocumentVersion type with UI-specific fields
interface DocumentVersion extends Omit<DbDocumentVersion, 'change_summary'> {
  created_by_name: string;
  change_summary: string | null;
}

interface VersionHistorySheetProps {
  documentId: string;
  currentVersion: number;
  isOpen: boolean;
  onClose: () => void;
  onRestoreVersion?: (version: DocumentVersion) => void;
}

export function VersionHistorySheet({
  documentId,
  currentVersion,
  isOpen,
  onClose,
  onRestoreVersion,
}: VersionHistorySheetProps) {
  const { getDocumentVersions, restoreDocumentVersion } = useDocuments();

  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVersion, setSelectedVersion] =
    useState<DocumentVersion | null>(null);
  const [showPreviewMode, setShowPreviewMode] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  useEffect(() => {
    if (isOpen) {
      const fetchVersions = async () => {
        setLoading(true);
        try {
          // Fetch real document versions from the database
          const fetchedVersions = await getDocumentVersions(documentId);
          console.log('Fetched document versions:', fetchedVersions);

          if (fetchedVersions && fetchedVersions.length > 0) {
            setVersions(fetchedVersions);
          } else {
            // If no versions are found, create a default current version
            const defaultVersion = {
              id: 'current',
              version: currentVersion,
              document_id: documentId,
              content: null, // We don't have the content here
              created_at: new Date().toISOString(),
              created_by: '',
              created_by_name: 'Current Version',
              change_summary: 'Current working version',
            };
            setVersions([defaultVersion]);
          }
        } catch (error) {
          console.error('Error fetching document versions:', error);
          toast.error('Could not retrieve document version history');
        } finally {
          setLoading(false);
        }
      };

      fetchVersions();
    }
  }, [documentId, currentVersion, isOpen, getDocumentVersions]);

  const handlePreviewVersion = (version: DocumentVersion) => {
    setSelectedVersion(version);
    setShowPreviewMode(true);
  };

  const handleRestoreVersion = async (version: DocumentVersion) => {
    if (version.version === currentVersion) return;

    setIsRestoring(true);

    try {
      // Call the API to restore the version
      const success = await restoreDocumentVersion(documentId, version.id);

      if (success) {
        if (onRestoreVersion) {
          onRestoreVersion(version);
        }

        toast.success(`Document restored to version ${version.version}`);

        onClose();
      } else {
        throw new Error('Failed to restore version');
      }
    } catch (error) {
      console.error('Error restoring document version:', error);
      toast.error('Failed to restore document version');
    } finally {
      setIsRestoring(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'MMM d, yyyy h:mm a');
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <div className="p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <Sheet.Title className="flex items-center">
            <History className="mr-2 h-5 w-5" />
            {showPreviewMode && selectedVersion
              ? `Version ${selectedVersion.version} Preview`
              : 'Version History'}
          </Sheet.Title>
        </div>

        {showPreviewMode && selectedVersion ? (
          <div className="py-4">
            <Button
              variant="outline"
              className="mb-4"
              onClick={() => setShowPreviewMode(false)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Version List
            </Button>

            <ScrollArea className="h-[60vh]">
              <div className="space-y-4">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>{selectedVersion.created_by_name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>{formatDate(selectedVersion.created_at)}</span>
                  </div>
                </div>

                <div className="border-t pt-4">
                  {selectedVersion.content &&
                    typeof selectedVersion.content === 'object' &&
                    'sections' in selectedVersion.content &&
                    Array.isArray(selectedVersion.content.sections) &&
                    selectedVersion.content.sections.map(
                      (section: any, index: number) => (
                        <div key={index} className="mb-6">
                          {section.title && (
                            <h4 className="text-lg font-medium mb-2">
                              {section.title}
                            </h4>
                          )}
                          <div
                            className="prose max-w-none"
                            dangerouslySetInnerHTML={{
                              __html: section.content,
                            }}
                          />
                        </div>
                      )
                    )}
                </div>
              </div>
            </ScrollArea>

            <div className="pt-4 border-t mt-4 flex justify-end gap-2">
              {selectedVersion.version !== currentVersion && (
                <Button
                  onClick={() => handleRestoreVersion(selectedVersion)}
                  disabled={isRestoring}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  {isRestoring ? 'Restoring...' : 'Restore This Version'}
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="py-4">
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm text-muted-foreground">
                {versions.length} versions available
              </div>
            </div>

            {loading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="h-20 bg-muted animate-pulse rounded-md"
                  />
                ))}
              </div>
            ) : versions.length === 0 ? (
              <div className="text-center py-12 border rounded-lg bg-muted/20">
                <History className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
                <h4 className="text-lg font-medium mb-1">No version history</h4>
                <p className="text-muted-foreground">
                  This document has no previous versions.
                </p>
              </div>
            ) : (
              <ScrollArea className="h-[60vh]">
                <div className="space-y-3">
                  {versions.map((version) => (
                    <div
                      key={version.id}
                      className={`border rounded-md p-4 ${version.version === currentVersion ? 'border-accent-300' : ''}`}
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                        <div className="flex items-start gap-3">
                          <div className="bg-muted rounded-full h-9 w-9 flex items-center justify-center flex-shrink-0">
                            {version.version === currentVersion ? (
                              <Check className="h-5 w-5 text-accent-300" />
                            ) : (
                              <Clock className="h-5 w-5 text-muted-foreground" />
                            )}
                          </div>

                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                Version {version.version}
                              </span>
                              {version.version === currentVersion && (
                                <Badge className="bg-accent-300/10 text-accent-300 hover:bg-accent-300/20 border-accent-300/20">
                                  Current
                                </Badge>
                              )}
                            </div>

                            <div className="text-sm text-muted-foreground mt-1">
                              {version.change_summary}
                            </div>

                            <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                <span>{version.created_by_name}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span title={formatDate(version.created_at)}>
                                  {formatDistanceToNow(
                                    new Date(version.created_at),
                                    { addSuffix: true }
                                  )}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2 ml-auto">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8"
                            onClick={() => handlePreviewVersion(version)}
                          >
                            <Eye className="h-3.5 w-3.5 mr-1" />
                            Preview
                          </Button>

                          {version.version !== currentVersion && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8"
                              onClick={() => handleRestoreVersion(version)}
                              disabled={isRestoring}
                            >
                              <RotateCcw className="h-3.5 w-3.5 mr-1" />
                              {isRestoring ? 'Restoring...' : 'Restore'}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>
        )}
      </div>
    </Sheet>
  );
}

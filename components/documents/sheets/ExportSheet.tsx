'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Sheet } from '@/components/ui/sheet';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useDocuments } from '@/lib/hooks';
import {
  DocumentExportService,
  ExportFormat,
  ExportOptions,
} from '@/lib/services/documentExportService';
import { Document as DocumentType } from '@/lib/types/database-modules';
import { Download, FileText, Lock, Share2 } from 'lucide-react';
import { useState } from 'react';

interface ExportSheetProps {
  document: DocumentType;
  isOpen: boolean;
  onClose: () => void;
}

export default function ExportSheet({
  document,
  isOpen,
  onClose,
}: ExportSheetProps) {
  // We're using direct toast calls instead of the useToasts hook
  const { addDocumentActivity } = useDocuments();
  const [exportFormat, setExportFormat] = useState<ExportFormat>('pdf');
  const [includeWatermark, setIncludeWatermark] = useState(false);
  const [watermarkText, setWatermarkText] = useState('CONFIDENTIAL');
  const [watermarkOpacity, setWatermarkOpacity] = useState(30);
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [includeSignaturePage, setIncludeSignaturePage] = useState(false);
  const [protectionLevel, setProtectionLevel] = useState<
    'none' | 'password' | 'signature'
  >('none');
  const [password, setPassword] = useState('');
  // Using react-pdf for all PDF exports

  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!document) return;

    setIsExporting(true);

    try {
      // Import the safeDbOperation utility
      const { safeDbOperation } = await import('@/lib/utils/db-operation');
      const exportService = new DocumentExportService();

      // Validate watermark settings
      if (includeWatermark) {
        if (!watermarkText || watermarkText.trim() === '') {
          throw new Error(
            'Watermark text cannot be empty when watermark is enabled'
          );
        }

        // Ensure watermark opacity is within valid range (1-100)
        if (watermarkOpacity < 1 || watermarkOpacity > 100) {
          setWatermarkOpacity(30); // Reset to default if invalid
        }
      }

      const options: ExportOptions = {
        format: exportFormat,
        includeWatermark,
        watermarkText: includeWatermark ? watermarkText : undefined,
        watermarkOpacity: includeWatermark ? watermarkOpacity : undefined,
        includeMetadata,
        includeSignaturePage,
        styling: {
          fontFamily: 'Times New Roman, Times, serif',
          fontClassName: '',
        },
      };

      // Track document download activity using safeDbOperation
      if (document && document.id) {
        await safeDbOperation(
          () =>
            addDocumentActivity(document.id, 'download', {
              format: exportFormat,
              includeWatermark,
              includeMetadata,
              includeSignaturePage,
              protectionLevel,
            }),
          {
            showToast: false, // Don't show toast for this operation
            errorMessage: 'Failed to track document activity',
          }
        );
      }

      // Use safeDbOperation to handle the export process
      const result = await safeDbOperation(
        async () => await exportService.exportDocument(document, options),
        {
          loadingMessage: `Exporting document as ${exportFormat.toUpperCase()}...`,
          successMessage: `Successfully exported document as ${exportFormat.toUpperCase()}`,
          errorMessage: 'Export failed',
          retryCount: 2, // Try up to 3 times total (initial + 2 retries)
        }
      );

      // If result is null, the operation failed
      if (!result) {
        throw new Error('Export operation failed');
      }

      // Create a download link
      if (result instanceof Blob) {
        // For binary formats like PDF
        const url = URL.createObjectURL(result);
        const a = window.document.createElement('a');
        a.href = url;
        a.download = `${document.title}.${exportFormat}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        // For text formats like HTML or plain text
        const blob = new Blob([result as string], {
          type: exportFormat === 'html' ? 'text/html' : 'text/plain',
        });
        const url = URL.createObjectURL(blob);
        const a = window.document.createElement('a');
        a.href = url;
        a.download = `${document.title}.${exportFormat === 'html' ? 'html' : 'txt'}`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }

      onClose();
    } catch (error) {
      // Log the error for debugging
      console.error('Error exporting document:', error);
      // No need to show a toast here as toast.promise already handles errors
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <div className="p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <Sheet.Title>Export Document</Sheet.Title>
        </div>

        <div className="space-y-6 py-4">
          <div>
            <h3 className="text-sm font-medium mb-2">
              Export &quot;{document.title}&quot;
            </h3>
          </div>

          <Tabs defaultValue="format">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="format">
                <FileText className="h-4 w-4 mr-2" />
                Format
              </TabsTrigger>
              <TabsTrigger value="appearance">
                <Share2 className="h-4 w-4 mr-2" />
                Appearance
              </TabsTrigger>

              <TabsTrigger value="security">
                <Lock className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
            </TabsList>

            <TabsContent value="format" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="export-format">Export Format</Label>
                <Select
                  value={exportFormat}
                  onValueChange={(value) =>
                    setExportFormat(value as ExportFormat)
                  }
                >
                  <SelectTrigger id="export-format">
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF Document</SelectItem>
                    <SelectItem value="docx">Word Document (DOCX)</SelectItem>
                    <SelectItem value="html">HTML Document</SelectItem>
                    <SelectItem value="text">Plain Text</SelectItem>
                    <SelectItem value="markdown">Markdown</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {exportFormat === 'pdf' && (
                <div className="space-y-2 mt-4">
                  <div className="text-sm text-muted-foreground">
                    Using React PDF for high-quality document rendering
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="include-metadata">Include Metadata</Label>
                  <Switch
                    id="include-metadata"
                    checked={includeMetadata}
                    onCheckedChange={setIncludeMetadata}
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  Include document type, status, and creation date
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="include-signature-page">
                    Include Signature Page
                  </Label>
                  <Switch
                    id="include-signature-page"
                    checked={includeSignaturePage}
                    onCheckedChange={setIncludeSignaturePage}
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  Add a blank page for physical signatures
                </div>
              </div>
            </TabsContent>

            <TabsContent value="appearance" className="space-y-4 pt-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="include-watermark">Add Watermark</Label>
                  <Switch
                    id="include-watermark"
                    checked={includeWatermark}
                    onCheckedChange={(checked) => {
                      setIncludeWatermark(checked);
                      // Set default watermark text if enabling and current text is empty
                      if (
                        checked &&
                        (!watermarkText || watermarkText.trim() === '')
                      ) {
                        setWatermarkText('CONFIDENTIAL');
                      }
                    }}
                  />
                </div>

                {includeWatermark && (
                  <div className="space-y-4 pt-2">
                    <div>
                      <Label htmlFor="watermark-text">Watermark Text</Label>
                      <Input
                        id="watermark-text"
                        value={watermarkText}
                        onChange={(e) => setWatermarkText(e.target.value)}
                        className="mt-1"
                        placeholder="Enter watermark text"
                        required={includeWatermark}
                      />
                    </div>

                    <div>
                      <Label htmlFor="watermark-opacity">
                        Opacity: {watermarkOpacity}%
                      </Label>
                      <input
                        id="watermark-opacity"
                        type="range"
                        min="10"
                        max="90"
                        value={watermarkOpacity}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          // Ensure value is between 10 and 90
                          setWatermarkOpacity(
                            Math.max(10, Math.min(90, value))
                          );
                        }}
                        className="w-full mt-1"
                      />
                    </div>

                    <div className="p-4 border rounded-md bg-neutral-50 relative overflow-hidden">
                      <div className="text-center font-medium">
                        Watermark Preview
                      </div>
                      <div
                        className="absolute inset-0 flex items-center justify-center text-2xl font-bold rotate-[-30deg] pointer-events-none"
                        style={{ opacity: watermarkOpacity / 100 }}
                      >
                        {watermarkText}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="security" className="space-y-4 pt-4">
              <div className="space-y-2">
                <div className="text-sm">
                  <p>
                    Document security is handled through access controls in the
                    Share tab.
                  </p>
                  <p className="mt-2">
                    For additional security, you can add a watermark to your
                    document in the Appearance tab.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="pt-4 border-t flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="button" onClick={handleExport} disabled={isExporting}>
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </div>
      </div>
    </Sheet>
  );
}

'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Lock } from 'lucide-react';
import { useState } from 'react';

interface PinAccessFormProps {
  documentTitle: string;
  onSubmit: (pin: string) => void;
  error?: string;
}

export function PinAccessForm({
  documentTitle,
  onSubmit,
  error,
}: PinAccessFormProps) {
  const [pin, setPin] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!pin || pin.length !== 4) return;

    setIsSubmitting(true);
    try {
      await onSubmit(pin);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="bg-primary/10 p-3 rounded-full">
            <Lock className="h-6 w-6 text-primary" />
          </div>
        </div>
        <CardTitle className="text-xl">PIN Protected Document</CardTitle>
        <p className="text-sm text-muted-foreground mt-2">
          Enter the 4-digit PIN to access &quot;{documentTitle}&quot;
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pin">4-Digit PIN</Label>
              <Input
                id="pin"
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={4}
                placeholder="Enter 4-digit PIN"
                value={pin}
                onChange={(e) => {
                  // Only allow digits
                  const value = e.target.value.replace(/[^0-9]/g, '');
                  setPin(value);
                }}
                className="text-center text-lg tracking-widest"
                autoComplete="off"
              />
              {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={pin.length !== 4 || isSubmitting}
            >
              {isSubmitting ? 'Verifying...' : 'Access Document'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useLawyers } from '@/lib/hooks';
import { Lawyer } from '@/lib/types/database-modules';
import {
  Calendar,
  ChevronRight,
  Clock,
  ExternalLink,
  FileCheck,
  Gavel,
  Loader2,
  Mail,
  Phone,
  Star,
  Video,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface LawyerConsultationPanelProps {
  documentId: string;
  documentTitle: string;
}

export function LawyerConsultationPanel({
  documentId,
  documentTitle,
}: LawyerConsultationPanelProps) {
  const {
    lawyers,
    consultations,
    fetchAllLawyers,
    fetchConsultations,
    scheduleLawyerConsultation,
  } = useLawyers();

  const [selectedLawyer, setSelectedLawyer] = useState<Lawyer | null>(null);
  const [consultationType, setConsultationType] = useState<'review' | 'call'>(
    'review'
  );
  const [isBooking, setIsBooking] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>('Today');
  const [selectedTime, setSelectedTime] = useState<string>('9:00 AM');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('lawyers');

  // Fetch lawyers and consultations when component mounts
  useEffect(() => {
    setIsLoading(true);
    Promise.all([fetchAllLawyers(), fetchConsultations()])
      .catch((error) => {
        console.error('Error fetching data:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [fetchAllLawyers, fetchConsultations]);

  const handleSelectLawyer = (lawyer: Lawyer) => {
    setSelectedLawyer(lawyer);
  };

  const handleBookConsultation = async () => {
    if (!selectedLawyer) return;

    setIsBooking(true);

    try {
      // Create a date object for the consultation
      const now = new Date();
      let consultationDate = new Date();

      // Set the date based on selection
      if (selectedDate === 'Tomorrow') {
        consultationDate.setDate(now.getDate() + 1);
      } else if (selectedDate === 'Wed, Jun 15') {
        // This is a specific date, would need to be dynamic in a real app
        consultationDate = new Date('2023-06-15');
      }

      // Set the time based on selection
      const [hours, minutes] = selectedTime.split(':');
      const isPM = selectedTime.includes('PM');
      let hour = parseInt(hours);
      if (isPM && hour !== 12) hour += 12;
      if (!isPM && hour === 12) hour = 0;

      consultationDate.setHours(hour);
      consultationDate.setMinutes(parseInt(minutes));

      // Schedule the consultation
      const result = await toast.promise(
        scheduleLawyerConsultation(
          selectedLawyer.id,
          documentId,
          consultationDate.toISOString(),
          consultationType === 'review' ? 60 : 30,
          `Consultation for document: ${documentTitle}`,
          consultationType === 'call' ? 'video' : 'message'
        ),
        {
          loading: 'Scheduling consultation...',
          success: `Your ${consultationType === 'review' ? 'document review' : 'consultation call'} with ${selectedLawyer.full_name} has been scheduled.`,
          error: 'Failed to schedule consultation. Please try again.',
        }
      );

      if (result) {
        // Reset selection on success
        setSelectedLawyer(null);
      }
    } catch (error) {
      console.error('Error booking consultation:', error);
    } finally {
      setIsBooking(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Gavel className="mr-2 h-5 w-5" />
          Legal Consultation
        </CardTitle>
      </CardHeader>
      <CardContent>
        {selectedLawyer ? (
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2"
                onClick={() => setSelectedLawyer(null)}
              >
                ← Back
              </Button>
              <h3 className="font-medium">Book Consultation</h3>
            </div>

            <div className="flex items-center gap-3 p-3 bg-neutral-50 rounded-md">
              <UserAvatar
                fallbackText={selectedLawyer.full_name}
                avatarUrl={selectedLawyer.avatar_url}
                size="lg"
              />
              <div>
                <div className="font-medium">{selectedLawyer.full_name}</div>
                <div className="text-sm text-neutral-500">
                  {selectedLawyer.specialization?.join(', ')}
                </div>
                <div className="flex items-center text-sm">
                  <Star className="h-3 w-3 text-amber-400 mr-1" />
                  <span>{selectedLawyer.average_rating || 0}</span>
                  <span className="text-neutral-500 mx-1">·</span>
                  <span className="text-neutral-500">
                    {selectedLawyer.consultation_count || 0} reviews
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-sm font-medium">Consultation Type</h4>
              <div className="grid grid-cols-2 gap-2">
                <div
                  className={`p-3 border rounded-md cursor-pointer ${
                    consultationType === 'review'
                      ? 'border-accent-300 bg-accent-10/5'
                      : 'hover:bg-neutral-50'
                  }`}
                  onClick={() => setConsultationType('review')}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <FileCheck className="h-4 w-4 text-accent-300" />
                    <span className="font-medium text-sm">Document Review</span>
                  </div>
                  <p className="text-xs text-neutral-500">
                    Get your document reviewed by a legal expert
                  </p>
                </div>

                <div
                  className={`p-3 border rounded-md cursor-pointer ${
                    consultationType === 'call'
                      ? 'border-accent-300 bg-accent-10/5'
                      : 'hover:bg-neutral-50'
                  }`}
                  onClick={() => setConsultationType('call')}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <Video className="h-4 w-4 text-accent-300" />
                    <span className="font-medium text-sm">Video Call</span>
                  </div>
                  <p className="text-xs text-neutral-500">
                    Schedule a video consultation
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-sm font-medium">Select Date & Time</h4>
              <div className="grid grid-cols-3 gap-2">
                {['Today', 'Tomorrow', 'Wed, Jun 15'].map((date) => (
                  <div
                    key={date}
                    className="p-2 border rounded-md text-center cursor-pointer hover:bg-neutral-50"
                  >
                    <div className="text-sm">{date}</div>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-3 gap-2">
                {['9:00 AM', '11:30 AM', '2:00 PM'].map((time) => (
                  <div
                    key={time}
                    className="p-2 border rounded-md text-center cursor-pointer hover:bg-neutral-50"
                  >
                    <div className="text-sm">{time}</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="pt-3 border-t">
              <div className="flex justify-between items-center mb-3">
                <div className="text-sm font-medium">Total</div>
                <div className="font-medium">
                  $
                  {consultationType === 'review'
                    ? selectedLawyer.hourly_rate ||
                      selectedLawyer.consultation_fee ||
                      100
                    : (selectedLawyer.hourly_rate ||
                        selectedLawyer.consultation_fee ||
                        100) * 0.5}
                </div>
              </div>

              <Button
                className="w-full"
                onClick={handleBookConsultation}
                disabled={isBooking}
              >
                {isBooking ? 'Booking...' : 'Book Consultation'}
              </Button>
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="lawyers">Find a Lawyer</TabsTrigger>
              <TabsTrigger value="consultations">My Consultations</TabsTrigger>
            </TabsList>

            <TabsContent value="lawyers" className="space-y-4 pt-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Recommended Lawyers</h3>
                <p className="text-xs text-neutral-500">
                  Based on your document type and needs
                </p>

                {isLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : lawyers.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      No lawyers available at the moment
                    </p>
                  </div>
                ) : (
                  <ScrollArea className="h-[300px] pr-4">
                    <div className="space-y-3">
                      {lawyers.map((lawyer) => (
                        <div
                          key={lawyer.id}
                          className="p-3 border rounded-md hover:border-accent-300 cursor-pointer"
                          onClick={() => handleSelectLawyer(lawyer)}
                        >
                          <div className="flex items-center gap-3">
                            <UserAvatar
                              fallbackText={lawyer.full_name}
                              avatarUrl={lawyer.avatar_url}
                              size="md"
                            />
                            <div className="flex-1">
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="font-medium">
                                    {lawyer.full_name}
                                  </div>
                                  <div className="text-sm text-neutral-500">
                                    {lawyer.specialization?.join(', ')}
                                  </div>
                                </div>
                                <ChevronRight className="h-4 w-4 text-neutral-400" />
                              </div>

                              <div className="flex items-center text-sm mt-1">
                                <Star className="h-3 w-3 text-amber-400 mr-1" />
                                <span>{lawyer.average_rating || 0}</span>
                                <span className="text-neutral-500 mx-1">·</span>
                                <span className="text-neutral-500">
                                  {lawyer.consultation_count || 0} reviews
                                </span>
                                <span className="text-neutral-500 mx-1">·</span>
                                <span>
                                  $
                                  {lawyer.hourly_rate ||
                                    lawyer.consultation_fee ||
                                    0}
                                  /hr
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="mt-2 text-sm">{lawyer.bio}</div>

                          <div className="flex items-center justify-between mt-3">
                            <Badge variant="outline" className="text-green-600">
                              <Clock className="mr-1 h-3 w-3" />
                              Available for consultation
                            </Badge>

                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <Phone className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <Video className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <Mail className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </div>

              <div className="pt-3 border-t">
                <Button variant="outline" className="w-full" asChild>
                  <a
                    href="/lawyer-directory"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Gavel className="mr-2 h-4 w-4" />
                    Browse Full Lawyer Directory
                    <ExternalLink className="ml-2 h-3 w-3" />
                  </a>
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="consultations" className="space-y-4 pt-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Your Consultations</h3>

                {isLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : consultations.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      You don't have any consultations yet
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => setActiveTab('lawyers')}
                    >
                      Find a Lawyer
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Upcoming consultations */}
                    {consultations
                      .filter((c) => c.status === 'scheduled')
                      .map((consultation) => {
                        // Find the lawyer for this consultation
                        const lawyer = lawyers.find(
                          (l) => l.id === consultation.lawyer_id
                        );

                        return (
                          <div
                            key={consultation.id}
                            className="p-4 border rounded-md"
                          >
                            <div className="flex items-center gap-3">
                              {lawyer && (
                                <>
                                  <UserAvatar
                                    fallbackText={lawyer.full_name}
                                    avatarUrl={lawyer.avatar_url}
                                    size="md"
                                  />
                                  <div>
                                    <div className="font-medium">
                                      {lawyer.full_name}
                                    </div>
                                    <div className="text-sm text-neutral-500">
                                      {lawyer.specialization?.join(', ')}
                                    </div>
                                  </div>
                                </>
                              )}
                            </div>

                            <div className="mt-3 p-2 bg-neutral-50 rounded-md">
                              <div className="flex items-center gap-2 text-sm">
                                <Calendar className="h-4 w-4 text-neutral-500" />
                                <span>
                                  {consultation.consultation_date
                                    ? new Date(
                                        consultation.consultation_date
                                      ).toLocaleString()
                                    : 'Date not set'}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-sm mt-1">
                                <Video className="h-4 w-4 text-neutral-500" />
                                <span>
                                  {consultation.consultation_type === 'video'
                                    ? 'Video Consultation'
                                    : 'Document Review'}
                                  ({consultation.duration_minutes} min)
                                </span>
                              </div>
                            </div>

                            <div className="mt-3 flex justify-between">
                              <Button variant="outline" size="sm">
                                Reschedule
                              </Button>
                              {consultation.meeting_link && (
                                <Button size="sm" asChild>
                                  <a
                                    href={consultation.meeting_link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    Join Call
                                  </a>
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })}

                    {/* Past consultations */}
                    {consultations
                      .filter((c) => c.status === 'completed')
                      .map((consultation) => {
                        // Find the lawyer for this consultation
                        const lawyer = lawyers.find(
                          (l) => l.id === consultation.lawyer_id
                        );

                        return (
                          <div
                            key={consultation.id}
                            className="p-4 border rounded-md bg-neutral-50"
                          >
                            <div className="flex items-center gap-3">
                              {lawyer && (
                                <>
                                  <UserAvatar
                                    fallbackText={lawyer.full_name}
                                    avatarUrl={lawyer.avatar_url}
                                    size="md"
                                  />
                                  <div>
                                    <div className="font-medium">
                                      {lawyer.full_name}
                                    </div>
                                    <div className="text-sm text-neutral-500">
                                      {lawyer.specialization?.join(', ')}
                                    </div>
                                  </div>
                                </>
                              )}
                            </div>

                            <div className="mt-3 flex items-center justify-between">
                              <div className="text-sm text-neutral-500">
                                {consultation.consultation_date
                                  ? new Date(
                                      consultation.consultation_date
                                    ).toLocaleDateString()
                                  : 'Date not available'}{' '}
                                ·
                                {consultation.consultation_type === 'video'
                                  ? ' Video Call'
                                  : ' Document Review'}
                              </div>
                              <Button variant="outline" size="sm">
                                View Notes
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}

'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
  Grip,
  Lightbulb,
  MoveDown,
  MoveUp,
  Plus,
  Sparkles,
  Trash,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { AISuggestionPopover } from './AISuggestionPopover';
import { RichTextEditor } from './RichTextEditor';

export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  isSignatureSection?: boolean;
  aiSuggestions?: {
    id: string;
    text: string;
    explanation: string;
  }[];
}

export interface DocumentContent {
  sections: DocumentSection[];
  signature?: string | null;
}

interface SectionBasedEditorProps {
  initialSections?: DocumentSection[];
  onChange?: (sections: DocumentSection[]) => void;
  className?: string;
}

function SectionBasedEditor({
  initialSections = [],
  onChange,
  className,
}: SectionBasedEditorProps) {
  const [sections, setSections] = useState<DocumentSection[]>(
    initialSections.length > 0
      ? initialSections
      : [
          {
            id: generateId(),
            title: '',
            content: '',
            aiSuggestions: [],
          },
        ]
  );

  // Update sections when initialSections changes
  useEffect(() => {
    if (initialSections.length > 0) {
      console.log(
        'SectionBasedEditor: initialSections updated',
        initialSections.length
      );

      // Check if the sections are actually different to avoid unnecessary re-renders
      const currentIds = sections.map((s) => s.id).join(',');
      const newIds = initialSections.map((s) => s.id).join(',');

      if (currentIds !== newIds || sections.length !== initialSections.length) {
        console.log('Sections have changed, updating state');
        setSections(initialSections);
      } else {
        // Check if content has changed in any section
        const hasContentChanged = sections.some((section, index) => {
          if (index >= initialSections.length) return true;
          return (
            section.content !== initialSections[index].content ||
            section.title !== initialSections[index].title
          );
        });

        if (hasContentChanged) {
          console.log('Section content has changed, updating state');
          setSections(initialSections);
        } else {
          console.log('Sections are the same, not updating state');
        }
      }
    } else if (initialSections.length === 0 && sections.length > 1) {
      // If initialSections is empty but we have multiple sections, reset to a single empty section
      setSections([
        {
          id: generateId(),
          title: '',
          content: '',
          aiSuggestions: [],
        },
      ]);
    }
  }, [initialSections, sections]);

  const [showAISuggestions, setShowAISuggestions] = useState(true);

  const handleSectionChange = (
    index: number,
    field: keyof DocumentSection,
    value: string
  ) => {
    const updatedSections = [...sections];
    updatedSections[index] = {
      ...updatedSections[index],
      [field]: value,
    };

    setSections(updatedSections);

    if (onChange) {
      onChange(updatedSections);
    }
  };

  const addSection = () => {
    const newSections = [
      ...sections,
      {
        id: generateId(),
        title: '',
        content: '',
        aiSuggestions: [],
      },
    ];

    setSections(newSections);

    if (onChange) {
      onChange(newSections);
    }
  };

  // Generate AI suggestions based on section content
  useEffect(() => {
    if (!showAISuggestions) return;

    // This would be an API call in a real implementation
    const generateSuggestions = async () => {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const updatedSections = sections.map((section) => {
        // Only generate suggestions for sections with content
        if (!section.content || section.content.length < 50) return section;

        // Check if we already have suggestions
        if (section.aiSuggestions && section.aiSuggestions.length > 0)
          return section;

        // Generate mock suggestions based on section title
        const mockSuggestions = [];

        if (section.title.toLowerCase().includes('confidentiality')) {
          mockSuggestions.push({
            id: generateId(),
            text: 'The Receiving Party shall protect the Confidential Information with the same degree of care as it would use for its own confidential information, but in no event less than reasonable care.',
            explanation:
              'This clause establishes a standard of care for handling confidential information.',
          });
        } else if (
          section.title.toLowerCase().includes('term') ||
          section.title.toLowerCase().includes('duration')
        ) {
          mockSuggestions.push({
            id: generateId(),
            text: 'This Agreement shall remain in effect for a period of two (2) years from the Effective Date, unless terminated earlier as provided herein.',
            explanation:
              'This clause clearly defines the duration of the agreement.',
          });
        } else if (
          section.title.toLowerCase().includes('payment') ||
          section.title.toLowerCase().includes('compensation')
        ) {
          mockSuggestions.push({
            id: generateId(),
            text: 'Payment shall be made within thirty (30) days of receipt of a proper invoice. Late payments shall accrue interest at a rate of 1.5% per month.',
            explanation:
              'This clause establishes payment terms and consequences for late payment.',
          });
        } else if (section.title.toLowerCase().includes('termination')) {
          mockSuggestions.push({
            id: generateId(),
            text: 'Either party may terminate this Agreement upon thirty (30) days written notice to the other party in the event of a material breach that remains uncured during such notice period.',
            explanation:
              'This clause provides a standard termination process for material breaches.',
          });
        } else if (section.content.length > 100) {
          // Generic suggestion for any section with substantial content
          mockSuggestions.push({
            id: generateId(),
            text: 'For the avoidance of doubt, nothing in this section shall be construed to limit or restrict any rights or remedies otherwise available under applicable law or equity.',
            explanation:
              'This clause preserves rights and remedies that might otherwise be available.',
          });
        }

        return {
          ...section,
          aiSuggestions: mockSuggestions,
        };
      });

      setSections(updatedSections);
    };

    const timer = setTimeout(generateSuggestions, 2000);
    return () => clearTimeout(timer);
  }, [sections, showAISuggestions]);

  const removeSection = (index: number) => {
    if (sections.length === 1) {
      return; // Don't remove the last section
    }

    const newSections = sections.filter((_, i) => i !== index);
    setSections(newSections);

    if (onChange) {
      onChange(newSections);
    }
  };

  const moveSection = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === sections.length - 1)
    ) {
      return; // Can't move outside bounds
    }

    const newSections = [...sections];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    // Swap sections
    [newSections[index], newSections[newIndex]] = [
      newSections[newIndex],
      newSections[index],
    ];

    setSections(newSections);

    if (onChange) {
      onChange(newSections);
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {sections.map((section, index) => (
        <Card key={section.id} className="relative group">
          <div className="absolute left-2 top-4 opacity-30 group-hover:opacity-100 transition-opacity">
            <Grip className="h-5 w-5 text-neutral-400" />
          </div>

          <CardContent className="pt-6 pl-10">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="flex-1">
                  <Label
                    htmlFor={`section-title-${index}`}
                    className="text-sm font-medium text-neutral-500 mb-1 block"
                  >
                    Section Title
                  </Label>
                  <Input
                    id={`section-title-${index}`}
                    value={section.title}
                    onChange={(e) =>
                      handleSectionChange(index, 'title', e.target.value)
                    }
                    placeholder="Section Title (optional)"
                    className="mb-2"
                  />
                  <div className="flex items-center space-x-2 mt-1 mb-2">
                    <Checkbox
                      id={`signature-section-${index}`}
                      checked={section.isSignatureSection || false}
                      onCheckedChange={(checked) => {
                        const updatedSections = [...sections];
                        // Only one section can be a signature section
                        if (checked) {
                          // Uncheck all other sections
                          updatedSections.forEach((s, i) => {
                            if (i !== index) {
                              s.isSignatureSection = false;
                            }
                          });
                        }
                        updatedSections[index].isSignatureSection =
                          checked === true;
                        setSections(updatedSections);
                        if (onChange) {
                          onChange(updatedSections);
                        }
                      }}
                    />
                    <Label
                      htmlFor={`signature-section-${index}`}
                      className="text-sm text-neutral-500"
                    >
                      Mark as signature section
                    </Label>
                  </div>
                </div>

                <div className="flex items-center gap-1 self-end mb-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => moveSection(index, 'up')}
                    disabled={index === 0}
                    className="h-8 w-8"
                    title="Move Up"
                  >
                    <MoveUp className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => moveSection(index, 'down')}
                    disabled={index === sections.length - 1}
                    className="h-8 w-8"
                    title="Move Down"
                  >
                    <MoveDown className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeSection(index)}
                    disabled={sections.length === 1}
                    className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                    title="Remove Section"
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="relative">
                {section.aiSuggestions &&
                  section.aiSuggestions.length > 0 &&
                  showAISuggestions && (
                    <div className="absolute right-2 top-2 z-10">
                      <AISuggestionPopover
                        trigger={
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 gap-1 bg-accent-10/5 border-accent-10/20 text-accent-300 hover:bg-accent-10/10"
                          >
                            <Sparkles className="h-3.5 w-3.5" />
                            AI Suggestion
                          </Button>
                        }
                        suggestions={section.aiSuggestions}
                        onAccept={(suggestion) => {
                          // Insert the suggestion at the end of the current content
                          const updatedContent =
                            section.content + '\n\n' + suggestion;
                          handleSectionChange(index, 'content', updatedContent);

                          // Remove the suggestion to prevent showing it again
                          const updatedSection = {
                            ...section,
                            aiSuggestions: [],
                          };

                          const updatedSections = [...sections];
                          updatedSections[index] = updatedSection;
                          setSections(updatedSections);
                        }}
                        onDismiss={() => {
                          // Remove the suggestion
                          const updatedSection = {
                            ...section,
                            aiSuggestions: [],
                          };

                          const updatedSections = [...sections];
                          updatedSections[index] = updatedSection;
                          setSections(updatedSections);
                        }}
                      />
                    </div>
                  )}

                <RichTextEditor
                  initialContent={section.content}
                  placeholder={`Write content for ${section.title || 'this section'}...`}
                  onChange={(html) =>
                    handleSectionChange(index, 'content', html)
                  }
                  maxLength={5000}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      <div className="flex justify-center gap-2">
        <Button variant="outline" onClick={addSection} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Section
        </Button>

        <Button
          variant="outline"
          onClick={() => setShowAISuggestions(!showAISuggestions)}
          className="gap-2"
        >
          <Lightbulb className="h-4 w-4" />
          {showAISuggestions
            ? 'Disable AI Suggestions'
            : 'Enable AI Suggestions'}
        </Button>
      </div>
    </div>
  );
}

// Helper function to generate unique IDs
function generateId(): string {
  return Math.random().toString(36).substring(2, 11);
}

export default SectionBasedEditor;

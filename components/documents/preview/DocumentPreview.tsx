'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Document } from '@/lib/types/database-modules';
import DOMPurify from 'dompurify';
import { useMemo } from 'react';

interface DocumentSection {
  title?: string;
  content: string;
}

interface DocumentContent {
  sections: DocumentSection[];
}

interface DocumentPreviewProps {
  document: Document | null;
}

function DocumentPreview({ document }: DocumentPreviewProps) {
  // No internal loading state - parent will handle loading through Suspense

  // Parse document content - memoized to prevent unnecessary recalculations
  const parsedContent = useMemo((): DocumentContent => {
    // Handle null or undefined content
    if (!document || !document.content) {
      return {
        sections: [
          {
            title: 'No Content',
            content: 'This document has no content.',
          },
        ],
      };
    }

    try {
      // Case 1: Content is an object with sections array
      if (
        typeof document.content === 'object' &&
        document.content !== null &&
        !Array.isArray(document.content) &&
        'sections' in document.content &&
        Array.isArray((document.content as any).sections)
      ) {
        // Safely map sections with fallbacks for missing properties
        const sections = ((document.content as any).sections || []).map(
          (section: any) => ({
            title: section?.title || '',
            content: section?.content || '',
          })
        );

        // If no sections, provide a default
        if (sections.length === 0) {
          sections.push({
            title: 'Content',
            content: 'No sections found in this document.',
          });
        }

        return { sections };
      }

      // Case 2: Content is a JSON string
      if (typeof document.content === 'string') {
        try {
          const parsed = JSON.parse(document.content);

          // If parsed content has sections array
          if (
            parsed &&
            typeof parsed === 'object' &&
            parsed.sections &&
            Array.isArray(parsed.sections)
          ) {
            const sections = (parsed.sections || []).map((section: any) => ({
              title: section?.title || '',
              content: section?.content || '',
            }));

            if (sections.length === 0) {
              sections.push({
                title: 'Content',
                content: 'No sections found in this document.',
              });
            }

            return { sections };
          }

          // If parsed but no sections, create a section from the parsed content
          return {
            sections: [
              {
                title: 'Content',
                content: JSON.stringify(parsed, null, 2),
              },
            ],
          };
        } catch (e) {
          // Not valid JSON, treat as plain text
          return {
            sections: [
              {
                title: 'Content',
                content: document.content as string,
              },
            ],
          };
        }
      }

      // Case 3: Default - treat as a single section with plain text
      return {
        sections: [
          {
            title: 'Content',
            content:
              typeof document.content === 'string'
                ? document.content
                : JSON.stringify(document.content, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('Error parsing document content:', error);
      // Return a fallback section with error message
      return {
        sections: [
          {
            title: 'Error Displaying Content',
            content:
              'There was an error displaying the document content. Please try editing the document.',
          },
        ],
      };
    }
  }, [document]);

  // Create a styled HTML document for preview - memoized to prevent regeneration on every render
  const previewHTML = useMemo(() => {
    // Safely get document properties with fallbacks and sanitize them
    const rawTitle = document?.title || 'Untitled Document';
    const rawDescription = document?.description || '';
    const rawId = document?.id || 'unknown';

    const safeTitle = DOMPurify.sanitize(rawTitle);
    const safeDescription = DOMPurify.sanitize(rawDescription);
    const safeId = DOMPurify.sanitize(rawId);

    // Define CSS styles for the document
    const styles = `
      /* Reset and base styles */
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      /* Document styles */
      body {
        font-family: 'Times New Roman', Times, serif;
        font-size: 16px;
        line-height: 1.5;
        color: #000;
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
        padding: 40px;
        background-color: #fff;
        position: relative;
        overflow-x: hidden;
      }

      /* Typography */
      h1 {
        font-size: 1.5em;
        font-weight: bold;
        margin-bottom: 1em;
        text-align: center;
      }
      h2 {
        font-size: 1.2em;
        font-weight: bold;
        margin-top: 1.5em;
        margin-bottom: 0.5em;
      }
      h3 {
        font-size: 1.1em;
        font-weight: bold;
        margin-top: 1.25em;
        margin-bottom: 0.5em;
      }
      p {
        margin-bottom: 0.75em;
      }

      /* Content sections */
      .section {
        margin-bottom: 1.5em;
      }
      .section-content {
        overflow-wrap: break-word;
      }

      /* HTML content styling */
      .section-content ul, .section-content ol {
        margin-left: 1.5em;
        margin-bottom: 1em;
      }
      .section-content li {
        margin-bottom: 0.5em;
      }
      .section-content a {
        color: #0066cc;
        text-decoration: underline;
      }
      .section-content table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 1em;
      }
      .section-content th, .section-content td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      .section-content th {
        background-color: #f2f2f2;
      }

      /* Metadata */
      .metadata {
        color: #666;
        font-size: 0.9em;
        margin-bottom: 2em;
      }

      /* Code blocks */
      pre {
        white-space: pre-wrap;
        font-family: inherit;
        margin: 0;
      }

      /* Footer */
      footer {
        margin-top: 100px;
        color: #666;
        font-size: 0.8em;
        padding-top: 10px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      /* Images */
      img {
        max-width: 100%;
        height: auto;
      }

      /* Signature section */
      .signature-section {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #eee;
      }
    `;

    // Start building the HTML document
    let previewHTML = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${safeTitle}</title>
        <style>${styles}</style>
      </head>
      <body>
        <h1>${safeTitle}</h1>
        ${safeDescription ? `<p style="text-align: center;">${safeDescription}</p>` : ''}
    `;

    // Add document content sections
    if (parsedContent.sections && parsedContent.sections.length > 0) {
      parsedContent.sections.forEach((section: DocumentSection) => {
        const safeTitle = section.title || '';
        // Use DOMPurify to sanitize HTML content instead of escaping it
        const safeContent = DOMPurify.sanitize(section.content || '');

        previewHTML += `
          <div class="section">
            ${safeTitle ? `<h2>${safeTitle}</h2>` : ''}
            <div class="section-content">${safeContent}</div>
          </div>
        `;
      });
    }

    // Add signature if available
    try {
      if (
        document?.content &&
        typeof document.content === 'object' &&
        document.content !== null &&
        'signature' in document.content &&
        document.content.signature
      ) {
        // Sanitize the signature URL
        const signatureUrl = String(document.content.signature || '');
        const safeSignatureUrl = DOMPurify.sanitize(signatureUrl);
        previewHTML += `
          <div class="signature-section">
            <h2 style="font-size: 1.2rem; margin-bottom: 1rem;">Signature</h2>
            <div style="margin-bottom: 0.5rem;">
              <img src="${safeSignatureUrl}" alt="Signature" style="max-width: 200px; max-height: 80px;" />
            </div>
            <p>Signed electronically</p>
            <p>Date: ${new Date().toLocaleDateString()}</p>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error adding signature to preview:', error);
    }

    // Add footer with 3 columns
    const currentDate = new Date().toLocaleDateString();
    previewHTML += `
        <footer>
          <div style="text-align: left; flex: 1;">Generated By Forms Notamess | ${currentDate}</div>
          <div style="text-align: center; flex: 1;">${safeId}</div>
          <div style="text-align: right; flex: 1;">Page 1</div>
        </footer>
      </body>
      </html>
    `;

    return previewHTML;
  }, [document, parsedContent]);

  // Handle null or undefined document after hooks are called
  if (!document) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="p-4 border rounded-md bg-gray-50 min-h-[400px]">
            <p className="text-center text-muted-foreground">
              Document not found or failed to load
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle document with no content after hooks are called
  if (!document.content) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="p-4 border rounded-md bg-gray-50 min-h-[400px]">
            <p className="text-center text-muted-foreground">
              No content available for preview
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render the document preview
  return (
    <Card className="document-preview-card">
      <CardContent className="pt-6">
        <div className="flex justify-center p-6 bg-gray-50 rounded-md">
          <div
            className="border rounded-md overflow-hidden"
            style={{
              width: '800px',
              maxWidth: '100%',
              height: 'calc(100vh - 16rem)',
              minHeight: '600px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              position: 'relative',
              backgroundColor: 'white',
            }}
          >
            {/* Use a key based on document ID to force iframe recreation when document changes */}
            <iframe
              key={document?.id || 'default-key'}
              title="Document Preview"
              srcDoc={previewHTML}
              className="w-full h-full"
              style={{
                border: 'none',
                display: 'block',
                backgroundColor: 'white',
              }}
              sandbox="allow-same-origin allow-scripts"
              loading="eager"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default DocumentPreview;

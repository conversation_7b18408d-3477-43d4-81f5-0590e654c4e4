'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { DocumentStatus, DocumentType } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  CalendarIcon,
  Check,
  ChevronDown,
  Filter,
  RotateCcw,
  Search,
  SlidersHorizontal,
  Tag,
  X,
} from 'lucide-react';
import { useEffect, useState } from 'react';

export interface SearchFilters {
  query: string;
  status: DocumentStatus | 'all';
  type: DocumentType | 'all';
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  tags: string[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  showTemplates: boolean;
}

interface AdvancedSearchPanelProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  availableTags: string[];
  availableTypes: string[];
  availableStatuses: string[];
  className?: string;
}

export function AdvancedSearchPanel({
  filters,
  onFiltersChange,
  availableTags,
  availableTypes,
  availableStatuses,
  className,
}: AdvancedSearchPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<SearchFilters>(filters);
  const [tagsOpen, setTagsOpen] = useState(false);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleQueryChange = (query: string) => {
    setLocalFilters((prev) => ({ ...prev, query }));
    onFiltersChange({ ...localFilters, query });
  };

  const handleStatusChange = (status: DocumentStatus | 'all') => {
    setLocalFilters((prev) => ({ ...prev, status }));
    onFiltersChange({ ...localFilters, status });
  };

  const handleTypeChange = (type: DocumentType | 'all') => {
    setLocalFilters((prev) => ({ ...prev, type }));
    onFiltersChange({ ...localFilters, type });
  };

  const handleDateRangeChange = (
    range: { from: Date | undefined; to: Date | undefined }
  ) => {
    setLocalFilters((prev) => ({ ...prev, dateRange: range }));
    onFiltersChange({ ...localFilters, dateRange: range });
  };

  const handleSortChange = (sortBy: string) => {
    setLocalFilters((prev) => ({ ...prev, sortBy }));
    onFiltersChange({ ...localFilters, sortBy });
  };

  const handleSortOrderChange = (sortOrder: 'asc' | 'desc') => {
    setLocalFilters((prev) => ({ ...prev, sortOrder }));
    onFiltersChange({ ...localFilters, sortOrder });
  };

  const handleShowTemplatesChange = (showTemplates: boolean) => {
    setLocalFilters((prev) => ({ ...prev, showTemplates }));
    onFiltersChange({ ...localFilters, showTemplates });
  };

  const handleTagSelect = (tag: string) => {
    const newTags = localFilters.tags.includes(tag)
      ? localFilters.tags.filter((t) => t !== tag)
      : [...localFilters.tags, tag];

    setLocalFilters((prev) => ({ ...prev, tags: newTags }));
    onFiltersChange({ ...localFilters, tags: newTags });
  };

  const handleResetFilters = () => {
    const resetFilters: SearchFilters = {
      query: '',
      status: 'all',
      type: 'all',
      dateRange: { from: undefined, to: undefined },
      tags: [],
      sortBy: 'updated_at',
      sortOrder: 'desc',
      showTemplates: true,
    };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  const hasActiveFilters =
    localFilters.status !== 'all' ||
    localFilters.type !== 'all' ||
    localFilters.tags.length > 0 ||
    localFilters.dateRange.from !== undefined ||
    localFilters.dateRange.to !== undefined ||
    !localFilters.showTemplates;

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <input
            type="text"
            placeholder="Search documents..."
            className="w-full pl-9 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={localFilters.query}
            onChange={(e) => handleQueryChange(e.target.value)}
          />
          {localFilters.query && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
              onClick={() => handleQueryChange('')}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Advanced Filters Button */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'flex items-center gap-2',
                hasActiveFilters && 'bg-primary/5 border-primary/20'
              )}
            >
              <SlidersHorizontal className="h-4 w-4" />
              <span>Filters</span>
              {hasActiveFilters && (
                <span className="ml-1 rounded-full bg-primary w-5 h-5 text-xs flex items-center justify-center text-primary-foreground">
                  {
                    [
                      localFilters.status !== 'all' ? 1 : 0,
                      localFilters.type !== 'all' ? 1 : 0,
                      localFilters.tags.length > 0 ? 1 : 0,
                      localFilters.dateRange.from !== undefined ||
                      localFilters.dateRange.to !== undefined
                        ? 1
                        : 0,
                      !localFilters.showTemplates ? 1 : 0,
                    ].filter(Boolean).length
                  }
                </span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[340px] p-4" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Filters</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 text-xs"
                  onClick={handleResetFilters}
                >
                  <RotateCcw className="mr-2 h-3 w-3" />
                  Reset
                </Button>
              </div>
              <Separator />

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select
                  value={localFilters.status}
                  onValueChange={(value) =>
                    handleStatusChange(value as DocumentStatus | 'all')
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {availableStatuses.map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Type Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Type</label>
                <Select
                  value={localFilters.type}
                  onValueChange={(value) =>
                    handleTypeChange(value as DocumentType | 'all')
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {availableTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Date Range Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <div className="grid grid-cols-2 gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'justify-start text-left font-normal',
                          !localFilters.dateRange.from &&
                            'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.dateRange.from ? (
                          format(localFilters.dateRange.from, 'PPP')
                        ) : (
                          <span>From</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={localFilters.dateRange.from}
                        onSelect={(date) =>
                          handleDateRangeChange({
                            ...localFilters.dateRange,
                            from: date,
                          })
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'justify-start text-left font-normal',
                          !localFilters.dateRange.to && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.dateRange.to ? (
                          format(localFilters.dateRange.to, 'PPP')
                        ) : (
                          <span>To</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={localFilters.dateRange.to}
                        onSelect={(date) =>
                          handleDateRangeChange({
                            ...localFilters.dateRange,
                            to: date,
                          })
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Tags Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Tags</label>
                <Popover open={tagsOpen} onOpenChange={setTagsOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      className="w-full justify-between"
                    >
                      {localFilters.tags.length > 0
                        ? `${localFilters.tags.length} selected`
                        : 'Select tags'}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Search tags..." />
                      <CommandEmpty>No tags found.</CommandEmpty>
                      <CommandGroup>
                        {availableTags.map((tag) => (
                          <CommandItem
                            key={tag}
                            value={tag}
                            onSelect={() => handleTagSelect(tag)}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                localFilters.tags.includes(tag)
                                  ? 'opacity-100'
                                  : 'opacity-0'
                              )}
                            />
                            <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
                            {tag}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
                {localFilters.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {localFilters.tags.map((tag) => (
                      <div
                        key={tag}
                        className="flex items-center bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-xs"
                      >
                        {tag}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-1"
                          onClick={() => handleTagSelect(tag)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Show Templates Toggle */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Show Templates</label>
                <Switch
                  checked={localFilters.showTemplates}
                  onCheckedChange={handleShowTemplatesChange}
                />
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Sort By</label>
                <div className="flex gap-2">
                  <Select
                    value={localFilters.sortBy}
                    onValueChange={handleSortChange}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="title">Title</SelectItem>
                      <SelectItem value="updated_at">Last Updated</SelectItem>
                      <SelectItem value="created_at">Created Date</SelectItem>
                      <SelectItem value="document_type">Type</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={localFilters.sortOrder}
                    onValueChange={(value) =>
                      handleSortOrderChange(value as 'asc' | 'desc')
                    }
                  >
                    <SelectTrigger className="w-[100px]">
                      <SelectValue placeholder="Order" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">Ascending</SelectItem>
                      <SelectItem value="desc">Descending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 mt-2">
          {localFilters.status !== 'all' && (
            <div className="flex items-center bg-primary/10 text-primary px-3 py-1 rounded-full text-xs">
              Status: {localFilters.status}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1"
                onClick={() => handleStatusChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          {localFilters.type !== 'all' && (
            <div className="flex items-center bg-primary/10 text-primary px-3 py-1 rounded-full text-xs">
              Type: {localFilters.type}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1"
                onClick={() => handleTypeChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          {localFilters.dateRange.from && (
            <div className="flex items-center bg-primary/10 text-primary px-3 py-1 rounded-full text-xs">
              From: {format(localFilters.dateRange.from, 'PP')}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1"
                onClick={() =>
                  handleDateRangeChange({
                    ...localFilters.dateRange,
                    from: undefined,
                  })
                }
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          {localFilters.dateRange.to && (
            <div className="flex items-center bg-primary/10 text-primary px-3 py-1 rounded-full text-xs">
              To: {format(localFilters.dateRange.to, 'PP')}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1"
                onClick={() =>
                  handleDateRangeChange({
                    ...localFilters.dateRange,
                    to: undefined,
                  })
                }
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          {!localFilters.showTemplates && (
            <div className="flex items-center bg-primary/10 text-primary px-3 py-1 rounded-full text-xs">
              Hide Templates
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1"
                onClick={() => handleShowTemplatesChange(true)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs"
              onClick={handleResetFilters}
            >
              Clear All
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

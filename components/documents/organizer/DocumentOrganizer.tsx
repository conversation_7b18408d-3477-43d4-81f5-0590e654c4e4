'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Sheet } from '@/components/ui/sheet';
import { useDocuments } from '@/lib/hooks';
import { cn } from '@/lib/utils';
import {
  ChevronDown,
  ChevronRight,
  Circle,
  Edit,
  Folder,
  MoreHorizontal,
  Tag as TagIcon,
  Trash,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface DocumentOrganizerProps {
  onSelectFolder?: (folderId: string | null) => void;
  onSelectTag?: (tagId: string | null) => void;
  selectedFolderId?: string | null;
  selectedTagId?: string | null;
  activeTab?: 'folders' | 'tags';
}

export function DocumentOrganizer({
  onSelectFolder,
  onSelectTag,
  selectedFolderId,
  selectedTagId,
  activeTab = 'folders',
}: DocumentOrganizerProps) {
  const {
    folders,
    tags,
    fetchFolders,
    fetchTags,
    createFolder,
    updateFolder,
    deleteFolder,
    createTag,
    updateTag,
    deleteTag,
  } = useDocuments();

  const [expandedFolders, setExpandedFolders] = useState<
    Record<string, boolean>
  >({});
  const [editingFolder, setEditingFolder] = useState<string | null>(null);
  const [editingTag, setEditingTag] = useState<string | null>(null);
  const [editName, setEditName] = useState('');
  const [isRenameSheetOpen, setIsRenameSheetOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{
    id: string;
    type: 'folder' | 'tag';
  } | null>(null);

  // Fetch folders and tags on component mount
  useEffect(() => {
    fetchFolders();
    fetchTags();
  }, [fetchFolders, fetchTags]);

  const toggleFolder = (folderId: string) => {
    setExpandedFolders((prev) => ({
      ...prev,
      [folderId]: !prev[folderId],
    }));
  };

  const handleSelectFolder = (folderId: string | null) => {
    if (onSelectFolder) {
      onSelectFolder(folderId);
    }
  };

  const handleSelectTag = (tagId: string | null) => {
    if (onSelectTag) {
      onSelectTag(tagId);
    }
  };

  const handleEditFolder = (folder: any) => {
    setEditingFolder(folder.id);
    setEditName(folder.name);
    setIsRenameSheetOpen(true);
  };

  const handleEditTag = (tag: any) => {
    setEditingTag(tag.id);
    setEditName(tag.name);
    setIsRenameSheetOpen(true);
  };

  const handleSaveRename = async () => {
    if (!editName.trim()) {
      toast.error('Name cannot be empty');
      return;
    }

    try {
      if (editingFolder) {
        await updateFolder(editingFolder, { name: editName.trim() });
        toast.success('Folder renamed successfully');
        fetchFolders();
      } else if (editingTag) {
        await updateTag(editingTag, { name: editName.trim() });
        toast.success('Tag renamed successfully');
        fetchTags();
      }
    } catch (error) {
      console.error('Error renaming item:', error);
      toast.error('Failed to rename item');
    } finally {
      setIsRenameSheetOpen(false);
      setEditingFolder(null);
      setEditingTag(null);
      setEditName('');
    }
  };

  const confirmDelete = (id: string, type: 'folder' | 'tag') => {
    setItemToDelete({ id, type });
    setIsDeleteConfirmOpen(true);
  };

  const handleDelete = async () => {
    if (!itemToDelete) return;

    try {
      if (itemToDelete.type === 'folder') {
        await deleteFolder(itemToDelete.id);
        toast.success('Folder deleted successfully');
        fetchFolders();
      } else {
        await deleteTag(itemToDelete.id);
        toast.success('Tag deleted successfully');
        fetchTags();
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error('Failed to delete item');
    } finally {
      setIsDeleteConfirmOpen(false);
      setItemToDelete(null);
    }
  };

  // Recursive function to render folder tree
  const renderFolders = (parentId: string | null = null, level = 0) => {
    const filteredFolders = folders.filter((f) => f.parent_id === parentId);

    if (filteredFolders.length === 0) {
      return parentId === null ? (
        <div className="text-sm text-muted-foreground py-2">
          No folders created yet
        </div>
      ) : null;
    }

    return (
      <div className={cn(level > 0 && 'ml-4 pl-2 border-l')}>
        {filteredFolders.map((folder) => {
          const hasChildren = folders.some((f) => f.parent_id === folder.id);
          const isExpanded = expandedFolders[folder.id];
          const isSelected = selectedFolderId === folder.id;

          return (
            <div key={folder.id} className="mb-1">
              <div
                className={cn(
                  'flex items-center py-1 px-2 rounded-md hover:bg-muted group',
                  isSelected && 'bg-muted'
                )}
              >
                <button
                  type="button"
                  className="mr-1 h-4 w-4 flex items-center justify-center"
                  onClick={() => toggleFolder(folder.id)}
                  style={{ visibility: hasChildren ? 'visible' : 'hidden' }}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                </button>

                <Folder
                  className={cn(
                    'mr-2 h-4 w-4',
                    isSelected ? 'text-primary' : 'text-muted-foreground'
                  )}
                />

                <span
                  className="flex-1 text-sm cursor-pointer"
                  onClick={() => handleSelectFolder(folder.id)}
                >
                  {folder.name}
                </span>

                <Badge variant="outline" className="mr-2">
                  {folder.count || 0}
                </Badge>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 opacity-0 group-hover:opacity-100"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Actions</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEditFolder(folder)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Rename
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => confirmDelete(folder.id, 'folder')}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {isExpanded && hasChildren && renderFolders(folder.id, level + 1)}
            </div>
          );
        })}
      </div>
    );
  };

  const renderTags = () => {
    if (tags.length === 0) {
      return (
        <div className="text-sm text-muted-foreground py-2">
          No tags created yet
        </div>
      );
    }

    return (
      <div className="space-y-1">
        {tags.map((tag) => {
          const isSelected = selectedTagId === tag.id;
          const tagColor = 'gray';

          return (
            <div
              key={tag.id}
              className={cn(
                'flex items-center py-1 px-2 rounded-md hover:bg-muted group',
                isSelected && 'bg-muted'
              )}
            >
              <Circle
                className={cn(
                  'mr-2 h-3 w-3 fill-current',
                  `text-${tagColor}-500`
                )}
              />

              <span
                className="flex-1 text-sm cursor-pointer"
                onClick={() => handleSelectTag(tag.id)}
              >
                {tag.name}
              </span>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 opacity-0 group-hover:opacity-100"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">Actions</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleEditTag(tag)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Rename
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-destructive"
                    onClick={() => confirmDelete(tag.id, 'tag')}
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div>
      {/* All Documents option */}
      <div className="mb-4">
        <div
          className={cn(
            'flex items-center py-1 px-2 rounded-md hover:bg-muted cursor-pointer',
            !selectedFolderId && !selectedTagId && 'bg-muted'
          )}
          onClick={() =>
            activeTab === 'folders'
              ? handleSelectFolder(null)
              : handleSelectTag(null)
          }
        >
          {activeTab === 'folders' ? (
            <Folder className="mr-2 h-4 w-4 text-primary" />
          ) : (
            <TagIcon className="mr-2 h-4 w-4 text-primary" />
          )}
          <span className="text-sm font-medium">
            All {activeTab === 'folders' ? 'Documents' : 'Tags'}
          </span>
        </div>
      </div>

      {/* Folder or Tag list */}
      {activeTab === 'folders' ? renderFolders() : renderTags()}

      {/* Rename Sheet */}
      <Sheet open={isRenameSheetOpen} onOpenChange={setIsRenameSheetOpen}>
        <div className="p-4 sm:p-6">
          <Sheet.Title>Rename {editingFolder ? 'Folder' : 'Tag'}</Sheet.Title>
          <div className="py-4">
            <Label htmlFor="rename-input">New name</Label>
            <Input
              id="rename-input"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              className="mt-1"
              autoFocus
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsRenameSheetOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleSaveRename}>Save</Button>
          </div>
        </div>
      </Sheet>

      {/* Delete Confirmation Sheet */}
      <Sheet open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <div className="p-4 sm:p-6">
          <Sheet.Title>Confirm Delete</Sheet.Title>
          <div className="py-4">
            <p>
              Are you sure you want to delete this {itemToDelete?.type}? This
              action cannot be undone.
            </p>
          </div>
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="shadow_red" onClick={handleDelete}>
              Delete
            </Button>
          </div>
        </div>
      </Sheet>
    </div>
  );
}

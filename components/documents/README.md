# Documents Component Structure

This directory contains all components related to documents in the NotAMess Forms application. The components are organized into subdirectories based on their functionality.

## Directory Structure

- **analytics/** - Components for document analytics and reporting
- **blockchain/** - Components for blockchain integration (verification, transactions, etc.)
- **editor/** - Components for document editing and content creation
- **organizer/** - Components for organizing documents (folders, tags, etc.)
- **panels/** - Panel components that appear in the document view (collaboration, smart contracts, etc.)
- **preview/** - Components for document preview
- **sheets/** - Sheet/dialog components for various document operations
- **templates/** - Components for template selection and management
- **ui/** - Basic UI components specific to documents
- **version/** - Components for version history and management

## Usage

Import components from the main documents index:

```tsx
import {
  DocumentCard,
  DocumentTable,
  DocumentPreview,
  ExportSheet,
  // etc.
} from '@/components/documents';
```

## Component Organization Guidelines

When adding new components:

1. Place the component in the appropriate subdirectory based on its functionality
2. Export the component from the subdirectory's index.ts file
3. The main index.ts file will automatically re-export all components

## Naming Conventions

- Use PascalCase for component names
- Use descriptive names that indicate the component's purpose
- Group related components in the same file when appropriate

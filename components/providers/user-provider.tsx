'use client';

import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/client';
import { useEffect } from 'react';

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { user, updateProfile, removeProfile, removeUser } = userStore();

  // Set up realtime subscription to the user's profile
  useEffect(() => {
    if (!user) {
      console.log('No user available for profile subscription');
      // Import the clearAllStores function dynamically to avoid circular dependencies
      import('@/lib/store/clear-stores').then(({ clearAllStores }) => {
        // Clear all Zustand stores when no user is available
        clearAllStores();
      });
      return;
    }

    console.log('Setting up realtime subscription for user:', user.id);

    // First, fetch the current profile data
    const fetchProfile = async () => {
      try {
        const { data, error } = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching profile:', error);
          if (error.code === 'PGRST116') {
            console.log('No profile found for user in user-provider');
          }
          return;
        }

        console.log('Initial profile data:', data);
        const profileData = data as any;
        updateProfile(profileData);

        // Check if user is a lawyer
        const { data: lawyerData, error: lawyerError } = await supabaseClient
          .from('lawyers')
          .select('id')
          .eq('user_id', user.id)
          .single();

        if (!lawyerError && lawyerData) {
          console.log('User is a lawyer:', lawyerData);
          // The profile already has user_role from the database
          updateProfile(profileData);
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };

    fetchProfile();

    // Subscribe to changes on the profile
    const channel = supabaseClient
      .channel(`profile:${user.id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${user.id}`,
        },
        async (payload) => {
          console.log('Profile changed:', payload);

          // Fetch the updated profile
          const { data, error } = await supabaseClient
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

          if (error) {
            console.error('Error fetching updated profile:', error);
            if (error.code === 'PGRST116') {
              console.log('No profile found during realtime update');
            }
            return;
          }

          console.log('Updated profile data:', data);

          // Check if the user is a lawyer
          const { data: lawyerData, error: lawyerError } = await supabaseClient
            .from('lawyers')
            .select('id')
            .eq('user_id', user.id)
            .single();

          // The profile already has user_role from the database
          // No need to add a separate role property
          const updatedProfileData = data;

          console.log(
            'Final profile data with correct role:',
            updatedProfileData
          );
          updateProfile(updatedProfileData);
        }
      )
      .subscribe();

    // Clean up the subscription when the component unmounts
    return () => {
      console.log('Cleaning up profile subscription');
      supabaseClient.removeChannel(channel);
    };
  }, [user, updateProfile, removeProfile, removeUser]);

  return <>{children}</>;
}

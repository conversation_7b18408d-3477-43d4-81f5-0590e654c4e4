'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useOrganizations } from '@/lib/hooks';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Shield, User, UserPlus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

const addMemberSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  role: z.enum(['admin', 'member']),
});

type AddMemberFormValues = z.infer<typeof addMemberSchema>;
type MemberRole = 'admin' | 'member';

interface AddMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  organizationId: string;
}

export function AddMemberDialog({
  open,
  onOpenChange,
  organizationId,
}: AddMemberDialogProps) {
  const { addMember } = useOrganizations();
  const [isAdding, setIsAdding] = useState(false);

  const form = useForm<AddMemberFormValues>({
    resolver: zodResolver(addMemberSchema),
    defaultValues: {
      email: '',
      role: 'member',
    },
  });

  async function onSubmit(values: AddMemberFormValues) {
    setIsAdding(true);

    try {
      // Create a promise for the member addition process
      const addMemberPromise = (async () => {
        // Add the user to the organization using the hook
        const success = await addMember(
          organizationId,
          values.email,
          values.role as MemberRole
        );

        if (!success) {
          throw new Error('Failed to add member');
        }

        return success;
      })();

      // Use toast.promise to handle loading, success, and error states
      toast.promise(addMemberPromise, {
        loading: 'Adding member...',
        success: () => {
          // Close the dialog
          onOpenChange(false);
          // Reset the form
          form.reset();
          return `${values.email} has been added to the organization as a ${values.role}.`;
        },
        error: (err) => {
          return err.message || 'Failed to add member';
        },
      });

      await addMemberPromise;
    } catch (error) {
      console.error('Error adding member:', error);
      // Error is already handled by toast.promise
    } finally {
      setIsAdding(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Add Member
          </DialogTitle>
          <DialogDescription>
            Add a new member to your organization.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter email address"
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The user must already have an account in the system
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="space-y-3"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="admin" />
                        </FormControl>
                        <FormLabel className="font-normal flex items-center gap-2">
                          <Shield className="h-4 w-4 text-blue-600" />
                          <div>
                            <div>Admin</div>
                            <p className="text-xs text-muted-foreground">
                              Can manage members, teams, and settings
                            </p>
                          </div>
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="member" />
                        </FormControl>
                        <FormLabel className="font-normal flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-600" />
                          <div>
                            <div>Member</div>
                            <p className="text-xs text-muted-foreground">
                              Can view and use organization resources
                            </p>
                          </div>
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormDescription>
                    Choose the appropriate role for this member
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isAdding}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isAdding}>
                {isAdding ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  'Add Member'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

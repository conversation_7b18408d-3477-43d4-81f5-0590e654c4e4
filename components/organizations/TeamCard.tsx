'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Team } from '@/lib/types/database-modules';
import {
  ChevronRight,
  MoreHorizontal,
  Pencil,
  Trash,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useState } from 'react';

interface TeamCardProps {
  team: Team;
  onEdit?: (team: Team) => void;
  onDelete?: (team: Team) => void;
}

export function TeamCard({ team, onEdit, onDelete }: TeamCardProps) {
  const { username } = useParams();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{team.name}</CardTitle>
          {(onEdit || onDelete) && (
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(team)}>
                    <Pencil className="h-4 w-4 mr-2" />
                    Edit Team
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => onDelete(team)}
                    >
                      <Trash className="h-4 w-4 mr-2" />
                      Delete Team
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        <CardDescription>
          {team.description || 'No description provided'}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Users className="h-4 w-4 text-neutral-500 mr-1" />
            <span className="text-sm text-neutral-500">
              {/* This would be populated if we had team members count */}
              Members
            </span>
          </div>
          <div className="flex -space-x-2">
            {/* This would be populated with team member avatars */}
            <Avatar className="h-6 w-6 border-2 border-background">
              <AvatarFallback>
                <Users className="h-3 w-3" />
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Link
          href={`/${username}/organizations/${team.organization_id}/teams/${team.id}`}
          className="w-full"
        >
          <Button variant="outline" className="w-full justify-between">
            <span>View Team</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

{"name": "notamess_forms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "download-fonts": "node scripts/download-fonts.js"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.1.0", "@react-email/render": "^1.1.2", "@react-pdf/renderer": "^4.3.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@tiptap/extension-heading": "^2.22.0", "@tiptap/extension-link": "^2.22.0", "@tiptap/extension-placeholder": "^2.22.0", "@tiptap/react": "^2.22.0", "@tiptap/starter-kit": "^2.22.0", "@types/dompurify": "^3.2.0", "ai": "^4.3.16", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "docx": "^9.5.1", "dompurify": "^3.2.6", "groq-sdk": "^0.25.0", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "isomorphic-dompurify": "^2.25.0", "jspdf": "^3.0.1", "lenis": "^1.3.4", "lucide-react": "^0.519.0", "motion": "^12.18.1", "next": "15.3.4", "openai": "^5.6.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.58.1", "react-image-crop": "^11.0.10", "react-use-measure": "^2.1.7", "react-use-wizard": "^2.3.0", "react-window": "^1.8.11", "resend": "^4.6.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vitest": "^3.2.4"}}